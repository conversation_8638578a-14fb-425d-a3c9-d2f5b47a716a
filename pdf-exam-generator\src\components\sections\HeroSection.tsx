'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { FileUpload } from '@/components/ui/FileUpload';
import { Sparkles, Users, Clock, Shield, LogIn, UserPlus } from 'lucide-react';

export function HeroSection() {
  const router = useRouter();
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);

  const handleFileSelect = (file: File) => {
    setUploadedFile(file);
    // Store file in localStorage for now (in production, upload to server)
    localStorage.setItem('uploadedPDF', JSON.stringify({
      name: file.name,
      size: file.size,
      type: file.type,
      lastModified: file.lastModified
    }));
    
    // Navigate to analysis page after a short delay
    setTimeout(() => {
      router.push('/analyze');
    }, 1500);
  };

  const features = [
    {
      icon: Sparkles,
      title: 'AI-Powered',
      description: 'Advanced AI analyzes your PDF and generates relevant questions'
    },
    {
      icon: Users,
      title: 'For Educators',
      description: 'Perfect for teachers, trainers, and e-learning platforms'
    },
    {
      icon: Clock,
      title: 'Save Time',
      description: 'Create comprehensive exams in minutes, not hours'
    },
    {
      icon: Shield,
      title: 'Secure',
      description: 'Your documents are processed securely and privately'
    }
  ];

  return (
    <div className="relative overflow-hidden bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Background decoration */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
      <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-96 h-96 bg-blue-200 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse-slow"></div>
      <div className="absolute bottom-0 right-1/4 w-72 h-72 bg-purple-200 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse-slow" style={{ animationDelay: '1s' }}></div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 lg:py-24">
        <div className="text-center mb-16">
          {/* Badge */}
          <div className="inline-flex items-center px-4 py-2 rounded-full bg-blue-100 text-blue-800 text-sm font-medium mb-8 animate-fade-in">
            <Sparkles className="w-4 h-4 mr-2" />
            AI-Powered Exam Generation
          </div>

          {/* Main heading */}
          <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold text-gray-900 mb-6 animate-slide-up">
            Transform PDFs into
            <span className="block text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600">
              Interactive Exams
            </span>
          </h1>

          {/* Subtitle */}
          <p className="text-xl md:text-2xl text-gray-600 mb-12 max-w-4xl mx-auto leading-relaxed animate-slide-up" style={{ animationDelay: '0.2s' }}>
            Upload any PDF document and let our AI create comprehensive, interactive exams 
            with multiple question types. Perfect for educators, students, and training programs.
          </p>

          {/* File Upload */}
          <div className="max-w-2xl mx-auto mb-16 animate-slide-up" style={{ animationDelay: '0.4s' }}>
            <FileUpload onFileSelect={handleFileSelect} />
          </div>

          {/* Features grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
            {features.map((feature, index) => {
              const Icon = feature.icon;
              return (
                <div 
                  key={feature.title}
                  className="text-center animate-slide-up"
                  style={{ animationDelay: `${0.6 + index * 0.1}s` }}
                >
                  <div className="inline-flex items-center justify-center w-16 h-16 bg-white rounded-2xl shadow-lg mb-4 group-hover:shadow-xl transition-shadow">
                    <Icon className="w-8 h-8 text-blue-600" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">{feature.title}</h3>
                  <p className="text-gray-600 text-sm leading-relaxed">{feature.description}</p>
                </div>
              );
            })}
          </div>

          {/* CTA buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center animate-slide-up" style={{ animationDelay: '1s' }}>
            <Link
              href="/auth/register"
              className="inline-flex items-center px-8 py-4 bg-blue-600 text-white rounded-xl font-semibold hover:bg-blue-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
            >
              <UserPlus className="w-5 h-5 mr-2" />
              ابدأ مجاناً
            </Link>
            <Link
              href="/auth/login"
              className="inline-flex items-center px-8 py-4 border-2 border-gray-300 text-gray-700 rounded-xl font-semibold hover:border-gray-400 hover:bg-gray-50 transition-all duration-200"
            >
              <LogIn className="w-5 h-5 mr-2" />
              تسجيل الدخول
            </Link>
          </div>

          {/* Trust indicators */}
          <div className="mt-16 pt-8 border-t border-gray-200 animate-slide-up" style={{ animationDelay: '1.2s' }}>
            <p className="text-sm text-gray-500 mb-4">Trusted by educators worldwide</p>
            <div className="flex justify-center items-center space-x-8 opacity-60">
              <div className="text-2xl font-bold text-gray-400">EDU+</div>
              <div className="text-2xl font-bold text-gray-400">LearnTech</div>
              <div className="text-2xl font-bold text-gray-400">EduAI</div>
              <div className="text-2xl font-bold text-gray-400">SmartClass</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
