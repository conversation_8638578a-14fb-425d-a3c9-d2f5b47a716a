'use client';

import { useAuth } from '@/contexts/AuthContext';
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown,
  Users, 
  Clock, 
  Target,
  Award,
  Calendar,
  Download,
  Filter,
  ArrowLeft
} from 'lucide-react';
import Link from 'next/link';

export default function AnalyticsPage() {
  const { user } = useAuth();

  const performanceData = [
    { subject: 'الرياضيات', score: 85, trend: 'up', change: '+5%' },
    { subject: 'الفيزياء', score: 92, trend: 'up', change: '+8%' },
    { subject: 'الكيمياء', score: 78, trend: 'down', change: '-2%' },
    { subject: 'الأحياء', score: 88, trend: 'up', change: '+3%' },
    { subject: 'التاريخ', score: 76, trend: 'up', change: '+1%' }
  ];

  const weeklyProgress = [
    { day: 'السبت', exams: 3, score: 85 },
    { day: 'الأحد', exams: 2, score: 90 },
    { day: 'الاثنين', exams: 4, score: 82 },
    { day: 'الثلاثاء', exams: 1, score: 95 },
    { day: 'الأربعاء', exams: 3, score: 88 },
    { day: 'الخميس', exams: 2, score: 87 },
    { day: 'الجمعة', exams: 1, score: 92 }
  ];

  const topicAnalysis = [
    { topic: 'الجبر', mastery: 95, questions: 45, timeSpent: 120 },
    { topic: 'الهندسة', mastery: 88, questions: 32, timeSpent: 95 },
    { topic: 'الإحصاء', mastery: 76, questions: 28, timeSpent: 85 },
    { topic: 'التفاضل', mastery: 82, questions: 38, timeSpent: 110 },
    { topic: 'التكامل', mastery: 79, questions: 25, timeSpent: 75 }
  ];

  const getMasteryColor = (mastery: number) => {
    if (mastery >= 90) return 'text-green-600 bg-green-100';
    if (mastery >= 80) return 'text-blue-600 bg-blue-100';
    if (mastery >= 70) return 'text-yellow-600 bg-yellow-100';
    return 'text-red-600 bg-red-100';
  };

  return (
    <div className="p-6">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <Link 
              href="/dashboard"
              className="flex items-center text-gray-600 hover:text-gray-900 transition-colors ml-4"
            >
              <ArrowLeft className="w-5 h-5 ml-2" />
              العودة للوحة التحكم
            </Link>
          </div>
          <div className="flex items-center space-x-4 rtl:space-x-reverse">
            <button className="flex items-center px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
              <Filter className="w-4 h-4 ml-2" />
              تصفية
            </button>
            <button className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
              <Download className="w-4 h-4 ml-2" />
              تصدير التقرير
            </button>
          </div>
        </div>
        <h1 className="text-3xl font-bold text-gray-900 mb-2">التحليلات والتقارير</h1>
        <p className="text-gray-600">
          تحليل شامل لأدائك وتقدمك في الاختبارات والمواد المختلفة
        </p>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">المعدل العام</p>
              <p className="text-3xl font-bold text-gray-900 mt-2">85%</p>
              <div className="flex items-center mt-2">
                <TrendingUp className="w-4 h-4 text-green-500 ml-1" />
                <span className="text-sm font-medium text-green-600">+5%</span>
              </div>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <Target className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">إجمالي الاختبارات</p>
              <p className="text-3xl font-bold text-gray-900 mt-2">24</p>
              <div className="flex items-center mt-2">
                <TrendingUp className="w-4 h-4 text-green-500 ml-1" />
                <span className="text-sm font-medium text-green-600">+12%</span>
              </div>
            </div>
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <BarChart3 className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">ساعات الدراسة</p>
              <p className="text-3xl font-bold text-gray-900 mt-2">42</p>
              <div className="flex items-center mt-2">
                <TrendingUp className="w-4 h-4 text-green-500 ml-1" />
                <span className="text-sm font-medium text-green-600">+8%</span>
              </div>
            </div>
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <Clock className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">الترتيب</p>
              <p className="text-3xl font-bold text-gray-900 mt-2">3rd</p>
              <div className="flex items-center mt-2">
                <TrendingUp className="w-4 h-4 text-green-500 ml-1" />
                <span className="text-sm font-medium text-green-600">+2 مراكز</span>
              </div>
            </div>
            <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
              <Award className="w-6 h-6 text-yellow-600" />
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Performance by Subject */}
        <div className="lg:col-span-2">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-lg font-semibold text-gray-900">الأداء حسب المادة</h2>
              <select className="text-sm border border-gray-300 rounded-md px-3 py-1">
                <option>آخر 30 يوم</option>
                <option>آخر 7 أيام</option>
                <option>آخر 90 يوم</option>
              </select>
            </div>
            
            <div className="space-y-4">
              {performanceData.map((item, index) => (
                <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-center">
                    <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center ml-4">
                      <BarChart3 className="w-5 h-5 text-blue-600" />
                    </div>
                    <div>
                      <h3 className="font-medium text-gray-900">{item.subject}</h3>
                      <div className="flex items-center mt-1">
                        {item.trend === 'up' ? (
                          <TrendingUp className="w-4 h-4 text-green-500 ml-1" />
                        ) : (
                          <TrendingDown className="w-4 h-4 text-red-500 ml-1" />
                        )}
                        <span className={`text-sm font-medium ${
                          item.trend === 'up' ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {item.change}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="text-left">
                    <div className={`text-2xl font-bold ${
                      item.score >= 90 ? 'text-green-600' :
                      item.score >= 80 ? 'text-blue-600' :
                      item.score >= 70 ? 'text-yellow-600' : 'text-red-600'
                    }`}>
                      {item.score}%
                    </div>
                    <div className="w-24 bg-gray-200 rounded-full h-2 mt-2">
                      <div 
                        className={`h-2 rounded-full ${
                          item.score >= 90 ? 'bg-green-500' :
                          item.score >= 80 ? 'bg-blue-500' :
                          item.score >= 70 ? 'bg-yellow-500' : 'bg-red-500'
                        }`}
                        style={{ width: `${item.score}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Weekly Progress */}
        <div>
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">التقدم الأسبوعي</h2>
            <div className="space-y-3">
              {weeklyProgress.map((day, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center ml-3">
                      <span className="text-xs font-medium text-blue-600">{day.exams}</span>
                    </div>
                    <span className="text-sm font-medium text-gray-900">{day.day}</span>
                  </div>
                  <div className="text-left">
                    <div className="text-sm font-bold text-gray-900">{day.score}%</div>
                    <div className="w-16 bg-gray-200 rounded-full h-1">
                      <div 
                        className="bg-blue-500 h-1 rounded-full"
                        style={{ width: `${day.score}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Topic Mastery */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">إتقان المواضيع</h2>
            <div className="space-y-3">
              {topicAnalysis.slice(0, 5).map((topic, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div>
                    <div className="text-sm font-medium text-gray-900">{topic.topic}</div>
                    <div className="text-xs text-gray-500">{topic.questions} سؤال</div>
                  </div>
                  <div className={`px-2 py-1 rounded-full text-xs font-medium ${getMasteryColor(topic.mastery)}`}>
                    {topic.mastery}%
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
