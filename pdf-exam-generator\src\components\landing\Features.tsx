'use client';

import { useLanguage } from '@/contexts/LanguageContext';
import { 
  <PERSON>, 
  <PERSON>Pointer, 
  BarChart3, 
  Zap, 
  Globe, 
  Shield,
  FileText,
  Users,
  Clock,
  Target,
  Sparkles,
  TrendingUp
} from 'lucide-react';

export function Features() {
  const { t } = useLanguage();

  const features = [
    {
      icon: Brain,
      title: t('feature.ai.title'),
      description: t('feature.ai.description'),
      color: 'blue',
      gradient: 'from-blue-500 to-cyan-500'
    },
    {
      icon: MousePointer,
      title: t('feature.interactive.title'),
      description: t('feature.interactive.description'),
      color: 'purple',
      gradient: 'from-purple-500 to-pink-500'
    },
    {
      icon: BarChart3,
      title: t('feature.analytics.title'),
      description: t('feature.analytics.description'),
      color: 'green',
      gradient: 'from-green-500 to-emerald-500'
    },
    {
      icon: Zap,
      title: t('feature.realtime.title'),
      description: t('feature.realtime.description'),
      color: 'yellow',
      gradient: 'from-yellow-500 to-orange-500'
    },
    {
      icon: Globe,
      title: t('feature.multilang.title'),
      description: t('feature.multilang.description'),
      color: 'indigo',
      gradient: 'from-indigo-500 to-blue-500'
    },
    {
      icon: Shield,
      title: t('feature.secure.title'),
      description: t('feature.secure.description'),
      color: 'red',
      gradient: 'from-red-500 to-pink-500'
    }
  ];

  const stats = [
    {
      icon: FileText,
      number: '10,000+',
      label: 'PDFs Processed',
      color: 'blue'
    },
    {
      icon: Users,
      number: '5,000+',
      label: 'Active Teachers',
      color: 'green'
    },
    {
      icon: Target,
      number: '100,000+',
      label: 'Exams Created',
      color: 'purple'
    },
    {
      icon: TrendingUp,
      number: '95%',
      label: 'Satisfaction Rate',
      color: 'orange'
    }
  ];

  return (
    <section id="features" className="py-20 bg-white dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-6">
            {t('features.title')}
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            {t('features.subtitle')}
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20">
          {features.map((feature, index) => {
            const Icon = feature.icon;
            return (
              <div
                key={index}
                className="group relative bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-200 dark:border-gray-700 hover:border-transparent hover:scale-105"
              >
                {/* Gradient Border on Hover */}
                <div className={`absolute inset-0 bg-gradient-to-r ${feature.gradient} rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10`}></div>
                <div className="absolute inset-[1px] bg-white dark:bg-gray-800 rounded-2xl group-hover:bg-opacity-95 dark:group-hover:bg-opacity-95 transition-all duration-300"></div>
                
                <div className="relative">
                  {/* Icon */}
                  <div className={`w-16 h-16 bg-gradient-to-r ${feature.gradient} rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}>
                    <Icon className="w-8 h-8 text-white" />
                  </div>

                  {/* Content */}
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-4 group-hover:text-transparent group-hover:bg-clip-text group-hover:bg-gradient-to-r group-hover:from-blue-600 group-hover:to-purple-600 transition-all duration-300">
                    {feature.title}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                    {feature.description}
                  </p>
                </div>
              </div>
            );
          })}
        </div>

        {/* Stats Section */}
        <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-gray-800 dark:to-gray-700 rounded-3xl p-8 lg:p-12">
          <div className="text-center mb-12">
            <h3 className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-4">
              Trusted by Educators Worldwide
            </h3>
            <p className="text-gray-600 dark:text-gray-300">
              Join thousands of teachers who are already transforming their teaching with ExamAI
            </p>
          </div>

          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => {
              const Icon = stat.icon;
              return (
                <div key={index} className="text-center">
                  <div className={`w-16 h-16 mx-auto mb-4 bg-gradient-to-r ${
                    stat.color === 'blue' ? 'from-blue-500 to-cyan-500' :
                    stat.color === 'green' ? 'from-green-500 to-emerald-500' :
                    stat.color === 'purple' ? 'from-purple-500 to-pink-500' :
                    'from-orange-500 to-red-500'
                  } rounded-xl flex items-center justify-center`}>
                    <Icon className="w-8 h-8 text-white" />
                  </div>
                  <div className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                    {stat.number}
                  </div>
                  <div className="text-gray-600 dark:text-gray-300">
                    {stat.label}
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* How It Works Preview */}
        <div className="mt-20">
          <div className="text-center mb-12">
            <h3 className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-4">
              How It Works
            </h3>
            <p className="text-gray-600 dark:text-gray-300">
              Transform your PDFs into interactive exams in just 3 simple steps
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {[
              {
                step: '1',
                title: 'Upload PDF',
                description: 'Simply drag and drop your PDF document or click to select from your device',
                icon: FileText,
                color: 'blue'
              },
              {
                step: '2',
                title: 'AI Processing',
                description: 'Our advanced AI analyzes your content and generates relevant questions automatically',
                icon: Brain,
                color: 'purple'
              },
              {
                step: '3',
                title: 'Share & Analyze',
                description: 'Share your exam with students and get detailed analytics on their performance',
                icon: BarChart3,
                color: 'green'
              }
            ].map((step, index) => {
              const Icon = step.icon;
              return (
                <div key={index} className="text-center">
                  <div className={`w-20 h-20 mx-auto mb-6 bg-gradient-to-r ${
                    step.color === 'blue' ? 'from-blue-500 to-cyan-500' :
                    step.color === 'purple' ? 'from-purple-500 to-pink-500' :
                    'from-green-500 to-emerald-500'
                  } rounded-full flex items-center justify-center relative`}>
                    <Icon className="w-10 h-10 text-white" />
                    <div className="absolute -top-2 -right-2 w-8 h-8 bg-white dark:bg-gray-800 rounded-full flex items-center justify-center text-sm font-bold text-gray-900 dark:text-white border-2 border-gray-200 dark:border-gray-600">
                      {step.step}
                    </div>
                  </div>
                  <h4 className="text-xl font-bold text-gray-900 dark:text-white mb-3">
                    {step.title}
                  </h4>
                  <p className="text-gray-600 dark:text-gray-300">
                    {step.description}
                  </p>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </section>
  );
}
