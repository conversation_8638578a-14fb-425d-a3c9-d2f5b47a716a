{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_3ec15414.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "vNLqZB+qlV8AcMPY+LNBRll7JrSRwdPw8giPl/qonfM=", "__NEXT_PREVIEW_MODE_ID": "0aeb160902f17034e9aeccd2027d9c47", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "cdcf8db0aece995806c3d437dd923b98ed1b3d960741c7f51eea9702a91230e3", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "71b54fa249b124b0223a2c265950a3dcb908461b90c3fa466382f40ee26192dd"}}}, "sortedMiddleware": ["/"], "functions": {}}