globalThis.__RSC_MANIFEST = globalThis.__RSC_MANIFEST || {};
globalThis.__RSC_MANIFEST["/test-firebase/page"] = {"moduleLoading":{"prefix":"","crossOrigin":null},"clientModules":{"[project]/node_modules/next/dist/esm/client/components/layout-router.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/layout-router.js":{"id":"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-page.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-page.js":{"id":"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-segment.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-segment.js":{"id":"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/metadata/async-metadata.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/metadata/async-metadata.js":{"id":"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/error-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/error-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"async":false},"[project]/node_modules/next/dist/client/components/error-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"async":false},"[project]/node_modules/next/dist/client/components/error-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"async":false},"[project]/src/contexts/AuthContext.tsx <module evaluation>":{"id":"[project]/src/contexts/AuthContext.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/src_342bc2dc._.js","/_next/static/chunks/node_modules_%40firebase_auth_dist_esm2017_09b0b4ed._.js","/_next/static/chunks/node_modules_%40firebase_firestore_dist_index_esm2017_c2fcaa2e.js","/_next/static/chunks/node_modules_%40firebase_storage_dist_index_esm2017_b3a08d2a.js","/_next/static/chunks/node_modules_3c08d59f._.js","/_next/static/chunks/src_app_layout_tsx_007ca514._.js"],"async":false},"[project]/src/contexts/AuthContext.tsx":{"id":"[project]/src/contexts/AuthContext.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/src_342bc2dc._.js","/_next/static/chunks/node_modules_%40firebase_auth_dist_esm2017_09b0b4ed._.js","/_next/static/chunks/node_modules_%40firebase_firestore_dist_index_esm2017_c2fcaa2e.js","/_next/static/chunks/node_modules_%40firebase_storage_dist_index_esm2017_b3a08d2a.js","/_next/static/chunks/node_modules_3c08d59f._.js","/_next/static/chunks/src_app_layout_tsx_007ca514._.js"],"async":false},"[project]/src/components/layout/AppContent.tsx <module evaluation>":{"id":"[project]/src/components/layout/AppContent.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/src_342bc2dc._.js","/_next/static/chunks/node_modules_%40firebase_auth_dist_esm2017_09b0b4ed._.js","/_next/static/chunks/node_modules_%40firebase_firestore_dist_index_esm2017_c2fcaa2e.js","/_next/static/chunks/node_modules_%40firebase_storage_dist_index_esm2017_b3a08d2a.js","/_next/static/chunks/node_modules_3c08d59f._.js","/_next/static/chunks/src_app_layout_tsx_007ca514._.js"],"async":false},"[project]/src/components/layout/AppContent.tsx":{"id":"[project]/src/components/layout/AppContent.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/src_342bc2dc._.js","/_next/static/chunks/node_modules_%40firebase_auth_dist_esm2017_09b0b4ed._.js","/_next/static/chunks/node_modules_%40firebase_firestore_dist_index_esm2017_c2fcaa2e.js","/_next/static/chunks/node_modules_%40firebase_storage_dist_index_esm2017_b3a08d2a.js","/_next/static/chunks/node_modules_3c08d59f._.js","/_next/static/chunks/src_app_layout_tsx_007ca514._.js"],"async":false},"[project]/src/app/test-firebase/page.tsx <module evaluation>":{"id":"[project]/src/app/test-firebase/page.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/src_342bc2dc._.js","/_next/static/chunks/node_modules_%40firebase_auth_dist_esm2017_09b0b4ed._.js","/_next/static/chunks/node_modules_%40firebase_firestore_dist_index_esm2017_c2fcaa2e.js","/_next/static/chunks/node_modules_%40firebase_storage_dist_index_esm2017_b3a08d2a.js","/_next/static/chunks/node_modules_3c08d59f._.js","/_next/static/chunks/src_app_layout_tsx_007ca514._.js","/_next/static/chunks/node_modules_firebase_storage_dist_esm_index_esm_88f637ef.js","/_next/static/chunks/_613da21d._.js","/_next/static/chunks/src_app_test-firebase_page_tsx_029a22e9._.js"],"async":false},"[project]/src/app/test-firebase/page.tsx":{"id":"[project]/src/app/test-firebase/page.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/src_342bc2dc._.js","/_next/static/chunks/node_modules_%40firebase_auth_dist_esm2017_09b0b4ed._.js","/_next/static/chunks/node_modules_%40firebase_firestore_dist_index_esm2017_c2fcaa2e.js","/_next/static/chunks/node_modules_%40firebase_storage_dist_index_esm2017_b3a08d2a.js","/_next/static/chunks/node_modules_3c08d59f._.js","/_next/static/chunks/src_app_layout_tsx_007ca514._.js","/_next/static/chunks/node_modules_firebase_storage_dist_esm_index_esm_88f637ef.js","/_next/static/chunks/_613da21d._.js","/_next/static/chunks/src_app_test-firebase_page_tsx_029a22e9._.js"],"async":false}},"ssrModuleMapping":{"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/layout-router.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_9876f0e3._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_9876f0e3._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-page.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_9876f0e3._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-segment.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_9876f0e3._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_9876f0e3._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/metadata/async-metadata.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_9876f0e3._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_9876f0e3._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_9876f0e3._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/src/contexts/AuthContext.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/contexts/AuthContext.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/[turbopack]_browser_dev_hmr-client_hmr-client_ts_d8c1d0ca._.js","server/chunks/ssr/[root-of-the-server]__68dd712d._.js","server/chunks/ssr/node_modules_next_575a5e15._.js","server/chunks/ssr/node_modules_@firebase_auth_dist_node-esm_0edc2096._.js","server/chunks/ssr/node_modules_@grpc_grpc-js_cd2a3c10._.js","server/chunks/ssr/node_modules_protobufjs_75a0be44._.js","server/chunks/ssr/node_modules_@firebase_firestore_dist_index_node_mjs_e50549ea._.js","server/chunks/ssr/node_modules_@firebase_storage_dist_node-esm_index_node_esm_7c90b52b.js","server/chunks/ssr/node_modules_dcdbb7c9._.js"],"async":false}},"[project]/src/components/layout/AppContent.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/components/layout/AppContent.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/[turbopack]_browser_dev_hmr-client_hmr-client_ts_d8c1d0ca._.js","server/chunks/ssr/[root-of-the-server]__68dd712d._.js","server/chunks/ssr/node_modules_next_575a5e15._.js","server/chunks/ssr/node_modules_@firebase_auth_dist_node-esm_0edc2096._.js","server/chunks/ssr/node_modules_@grpc_grpc-js_cd2a3c10._.js","server/chunks/ssr/node_modules_protobufjs_75a0be44._.js","server/chunks/ssr/node_modules_@firebase_firestore_dist_index_node_mjs_e50549ea._.js","server/chunks/ssr/node_modules_@firebase_storage_dist_node-esm_index_node_esm_7c90b52b.js","server/chunks/ssr/node_modules_dcdbb7c9._.js"],"async":false}},"[project]/src/app/test-firebase/page.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/app/test-firebase/page.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/[turbopack]_browser_dev_hmr-client_hmr-client_ts_d8c1d0ca._.js","server/chunks/ssr/[root-of-the-server]__68dd712d._.js","server/chunks/ssr/node_modules_next_575a5e15._.js","server/chunks/ssr/node_modules_@firebase_auth_dist_node-esm_0edc2096._.js","server/chunks/ssr/node_modules_@grpc_grpc-js_cd2a3c10._.js","server/chunks/ssr/node_modules_protobufjs_75a0be44._.js","server/chunks/ssr/node_modules_@firebase_firestore_dist_index_node_mjs_e50549ea._.js","server/chunks/ssr/node_modules_@firebase_storage_dist_node-esm_index_node_esm_7c90b52b.js","server/chunks/ssr/node_modules_dcdbb7c9._.js","server/chunks/ssr/node_modules_firebase_storage_dist_index_mjs_b5402c51._.js","server/chunks/ssr/_903bfb8b._.js"],"async":false}}},"edgeSSRModuleMapping":{},"rscModuleMapping":{"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/layout-router.js (client reference/proxy)","name":"*","chunks":["server/app/test-firebase/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js (client reference/proxy)","name":"*","chunks":["server/app/test-firebase/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-page.js (client reference/proxy)","name":"*","chunks":["server/app/test-firebase/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-segment.js (client reference/proxy)","name":"*","chunks":["server/app/test-firebase/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js (client reference/proxy)","name":"*","chunks":["server/app/test-firebase/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/metadata/async-metadata.js (client reference/proxy)","name":"*","chunks":["server/app/test-firebase/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js (client reference/proxy)","name":"*","chunks":["server/app/test-firebase/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js (client reference/proxy)","name":"*","chunks":["server/app/test-firebase/page.js"],"async":false}},"[project]/src/contexts/AuthContext.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/contexts/AuthContext.tsx (client reference/proxy)","name":"*","chunks":["server/app/test-firebase/page.js"],"async":false}},"[project]/src/components/layout/AppContent.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/components/layout/AppContent.tsx (client reference/proxy)","name":"*","chunks":["server/app/test-firebase/page.js"],"async":false}},"[project]/src/app/test-firebase/page.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/app/test-firebase/page.tsx (client reference/proxy)","name":"*","chunks":["server/app/test-firebase/page.js"],"async":false}}},"edgeRscModuleMapping":{},"entryCSSFiles":{"[project]/src/app/favicon.ico":[],"[project]/src/app/layout":[{"path":"static/chunks/[root-of-the-server]__404369a3._.css","inlined":false}],"[project]/src/app/test-firebase/page":[{"path":"static/chunks/[root-of-the-server]__404369a3._.css","inlined":false}]},"entryJSFiles":{"[project]/src/app/favicon.ico":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_756fb309._.js"],"[project]/src/app/layout":["static/chunks/src_342bc2dc._.js","static/chunks/node_modules_@firebase_auth_dist_esm2017_09b0b4ed._.js","static/chunks/node_modules_@firebase_firestore_dist_index_esm2017_c2fcaa2e.js","static/chunks/node_modules_@firebase_storage_dist_index_esm2017_b3a08d2a.js","static/chunks/node_modules_3c08d59f._.js","static/chunks/src_app_layout_tsx_007ca514._.js"],"[project]/src/app/test-firebase/page":["static/chunks/src_342bc2dc._.js","static/chunks/node_modules_@firebase_auth_dist_esm2017_09b0b4ed._.js","static/chunks/node_modules_@firebase_firestore_dist_index_esm2017_c2fcaa2e.js","static/chunks/node_modules_@firebase_storage_dist_index_esm2017_b3a08d2a.js","static/chunks/node_modules_3c08d59f._.js","static/chunks/src_app_layout_tsx_007ca514._.js","static/chunks/node_modules_firebase_storage_dist_esm_index_esm_88f637ef.js","static/chunks/_613da21d._.js","static/chunks/src_app_test-firebase_page_tsx_029a22e9._.js"]}}
