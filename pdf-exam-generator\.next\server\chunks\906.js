"use strict";exports.id=906,exports.ids=[906],exports.modules={48340:(e,a,r)=>{r.d(a,{A:()=>s});let s=(0,r(62688).A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]])},74906:(e,a,r)=>{r.r(a),r.d(a,{Footer:()=>y});var s=r(60687),t=r(34393),l=r(62688);let c=(0,l.A)("twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]]),i=(0,l.A)("facebook",[["path",{d:"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z",key:"1jg4f8"}]]),n=(0,l.A)("linkedin",[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]]),o=(0,l.A)("instagram",[["rect",{width:"20",height:"20",x:"2",y:"2",rx:"5",ry:"5",key:"2e1cvw"}],["path",{d:"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z",key:"9exkf1"}],["line",{x1:"17.5",x2:"17.51",y1:"6.5",y2:"6.5",key:"r4j83e"}]]),h=(0,l.A)("youtube",[["path",{d:"M2.5 17a24.12 24.12 0 0 1 0-10 2 2 0 0 1 1.4-1.4 49.56 49.56 0 0 1 16.2 0A2 2 0 0 1 21.5 7a24.12 24.12 0 0 1 0 10 2 2 0 0 1-1.4 1.4 49.55 49.55 0 0 1-16.2 0A2 2 0 0 1 2.5 17",key:"1q2vi4"}],["path",{d:"m10 15 5-3-5-3z",key:"1jp15x"}]]);var m=r(10022),x=r(41550),d=r(48340);let p=(0,l.A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]),f=(0,l.A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]);function y(){let{t:e}=(0,t.o)();return(0,s.jsxs)("footer",{className:"bg-gray-900 dark:bg-black text-white",children:[(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8",children:[(0,s.jsxs)("div",{className:"lg:col-span-2",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 rtl:space-x-reverse mb-6",children:[(0,s.jsx)("div",{className:"w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center",children:(0,s.jsx)(m.A,{className:"w-5 h-5 text-white"})}),(0,s.jsx)("span",{className:"text-xl font-bold",children:"ExamAI"})]}),(0,s.jsx)("p",{className:"text-gray-400 mb-6 leading-relaxed",children:"Transform your PDFs into interactive exams with the power of AI. Join thousands of educators worldwide who trust ExamAI for better learning outcomes."}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3 rtl:space-x-reverse text-gray-400",children:[(0,s.jsx)(x.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:"<EMAIL>"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-3 rtl:space-x-reverse text-gray-400",children:[(0,s.jsx)(d.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:"+1 (555) 123-4567"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-3 rtl:space-x-reverse text-gray-400",children:[(0,s.jsx)(p,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:"San Francisco, CA"})]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold mb-6",children:e("footer.product")}),(0,s.jsx)("ul",{className:"space-y-3",children:[{name:"Features",href:"#features"},{name:"Pricing",href:"#pricing"},{name:"API",href:"/api"},{name:"Integrations",href:"/integrations"},{name:"Changelog",href:"/changelog"}].map((e,a)=>(0,s.jsx)("li",{children:(0,s.jsx)("a",{href:e.href,className:"text-gray-400 hover:text-white transition-colors",children:e.name})},a))})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold mb-6",children:e("footer.company")}),(0,s.jsx)("ul",{className:"space-y-3",children:[{name:"About Us",href:"/about"},{name:"Careers",href:"/careers"},{name:"Blog",href:"/blog"},{name:"Press",href:"/press"},{name:"Partners",href:"/partners"}].map((e,a)=>(0,s.jsx)("li",{children:(0,s.jsx)("a",{href:e.href,className:"text-gray-400 hover:text-white transition-colors",children:e.name})},a))})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold mb-6",children:e("footer.support")}),(0,s.jsx)("ul",{className:"space-y-3",children:[{name:"Help Center",href:"/help"},{name:"Documentation",href:"/docs"},{name:"Community",href:"/community"},{name:"Contact Us",href:"/contact"},{name:"Status",href:"/status"}].map((e,a)=>(0,s.jsx)("li",{children:(0,s.jsx)("a",{href:e.href,className:"text-gray-400 hover:text-white transition-colors",children:e.name})},a))})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold mb-6",children:e("footer.legal")}),(0,s.jsx)("ul",{className:"space-y-3",children:[{name:"Privacy Policy",href:"/privacy"},{name:"Terms of Service",href:"/terms"},{name:"Cookie Policy",href:"/cookies"},{name:"GDPR",href:"/gdpr"},{name:"Security",href:"/security"}].map((e,a)=>(0,s.jsx)("li",{children:(0,s.jsx)("a",{href:e.href,className:"text-gray-400 hover:text-white transition-colors",children:e.name})},a))})]})]}),(0,s.jsx)("div",{className:"border-t border-gray-800 pt-12 mt-12",children:(0,s.jsxs)("div",{className:"max-w-md mx-auto text-center",children:[(0,s.jsx)("h3",{className:"text-xl font-semibold mb-4",children:"Stay Updated"}),(0,s.jsx)("p",{className:"text-gray-400 mb-6",children:"Get the latest updates, tips, and educational resources delivered to your inbox."}),(0,s.jsxs)("div",{className:"flex space-x-3 rtl:space-x-reverse",children:[(0,s.jsx)("input",{type:"email",placeholder:"Enter your email",className:"flex-1 px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg focus:outline-none focus:border-blue-500 text-white placeholder-gray-400"}),(0,s.jsx)("button",{className:"bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200 font-medium",children:"Subscribe"})]})]})})]}),(0,s.jsx)("div",{className:"border-t border-gray-800",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:(0,s.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0",children:[(0,s.jsxs)("div",{className:"text-gray-400 text-sm",children:["\xa9 2024 ExamAI. All rights reserved. Made with"," ",(0,s.jsx)(f,{className:"w-4 h-4 text-red-500 inline mx-1"}),"for educators worldwide."]}),(0,s.jsx)("div",{className:"flex items-center space-x-4 rtl:space-x-reverse",children:[{name:"Twitter",icon:c,href:"https://twitter.com/examai"},{name:"Facebook",icon:i,href:"https://facebook.com/examai"},{name:"LinkedIn",icon:n,href:"https://linkedin.com/company/examai"},{name:"Instagram",icon:o,href:"https://instagram.com/examai"},{name:"YouTube",icon:h,href:"https://youtube.com/examai"}].map((e,a)=>{let r=e.icon;return(0,s.jsx)("a",{href:e.href,target:"_blank",rel:"noopener noreferrer",className:"text-gray-400 hover:text-white transition-colors","aria-label":e.name,children:(0,s.jsx)(r,{className:"w-5 h-5"})},a)})})]})})})]})}}};