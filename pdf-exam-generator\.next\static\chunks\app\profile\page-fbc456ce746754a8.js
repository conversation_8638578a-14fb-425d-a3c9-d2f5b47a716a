(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[636],{844:(e,t,s)=>{"use strict";s.d(t,{AuthProvider:()=>p,A:()=>y});var r=s(5155),l=s(2115),a=s(6203),i=s(5317),d=s(3915),n=s(858);let c={apiKey:"demo-key",authDomain:"demo-project.firebaseapp.com",projectId:"demo-project",storageBucket:"demo-project.appspot.com",messagingSenderId:"*********",appId:"1:*********:web:abcdef",measurementId:s(9509).env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID||"G-ABCDEF"},o=0===(0,d.Dk)().length?(0,d.Wp)(c):(0,d.Dk)()[0],m=(0,a.xI)(o),u=(0,i.aU)(o);(0,n.c7)(o);let x=new a.HF,h=new a.sk;x.setCustomParameters({prompt:"select_account"}),h.setCustomParameters({display:"popup"});let g=(0,l.createContext)(void 0),y=()=>{let e=(0,l.useContext)(g);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e},p=e=>{let{children:t}=e,[s,d]=(0,l.useState)(null),[n,c]=(0,l.useState)(null),[o,y]=(0,l.useState)(!0),p=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!e)return;let s=(0,i.H9)(u,"users",e.uid),r=await (0,i.x7)(s);if(r.exists()){let e={...r.data(),lastLoginAt:new Date};await (0,i.BN)(s,e,{merge:!0}),c(e)}else{let{displayName:r,email:l,photoURL:a}=e,d=new Date,n={uid:e.uid,email:l||"",displayName:r||"",photoURL:a||void 0,role:t.role||"student",createdAt:d,lastLoginAt:d,preferences:{language:"ar",theme:"light",notifications:!0},stats:{totalExams:0,averageScore:0,totalQuestions:0,studyTime:0},...t};try{await (0,i.BN)(s,n),c(n)}catch(e){console.error("Error creating user profile:",e)}}},j=async(e,t)=>{y(!0);try{let s=await (0,a.x9)(m,e,t);await p(s.user)}catch(e){throw console.error("Error signing in:",e),e}finally{y(!1)}},v=async(e,t)=>{try{return await j(e,t),!0}catch(e){return!1}},f=async(e,t,s,r)=>{y(!0);try{let l=await (0,a.eJ)(m,e,t);await (0,a.r7)(l.user,{displayName:s}),await p(l.user,{role:r,displayName:s})}catch(e){throw console.error("Error signing up:",e),e}finally{y(!1)}},b=async()=>{y(!0);try{let e=await (0,a.df)(m,x);await p(e.user)}catch(e){throw console.error("Error signing in with Google:",e),e}finally{y(!1)}},N=async()=>{y(!0);try{let e=await (0,a.df)(m,h);await p(e.user)}catch(e){throw console.error("Error signing in with Facebook:",e),e}finally{y(!1)}},w=async()=>{y(!0);try{await (0,a.CI)(m),d(null),c(null),localStorage.removeItem("examResults"),localStorage.removeItem("examAnswers"),localStorage.removeItem("uploadedPDF"),localStorage.removeItem("examConfig")}catch(e){throw console.error("Error signing out:",e),e}finally{y(!1)}},A=async e=>{try{await (0,a.J1)(m,e)}catch(e){throw console.error("Error sending password reset email:",e),e}},k=async e=>{if(s)try{let t=(0,i.H9)(u,"users",s.uid);await (0,i.BN)(t,e,{merge:!0}),n&&c({...n,...e})}catch(e){throw console.error("Error updating user profile:",e),e}};return(0,l.useEffect)(()=>(0,a.hg)(m,async e=>{e?(d(e),await p(e)):(d(null),c(null)),y(!1)}),[]),(0,r.jsx)(g.Provider,{value:{user:s,userProfile:n,loading:o,isAuthenticated:!!s,signIn:j,signUp:f,signInWithGoogle:b,signInWithFacebook:N,logout:w,resetPassword:A,updateUserProfile:k,login:v},children:t})}},1007:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},4186:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},4229:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},4338:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});var r=s(5155);function l(e){let{size:t="md",color:s="blue",text:l,fullScreen:a=!1}=e,i=(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full border-2 border-gray-200 ".concat({blue:"border-blue-600",green:"border-green-600",gray:"border-gray-600"}[s]," border-t-transparent ").concat({sm:"h-4 w-4",md:"h-8 w-8",lg:"h-12 w-12"}[t])}),l&&(0,r.jsx)("p",{className:"mt-2 text-sm text-gray-600",children:l})]});return a?(0,r.jsx)("div",{className:"fixed inset-0 bg-white bg-opacity-75 flex items-center justify-center z-50",children:i}):i}},4416:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},5040:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},5695:(e,t,s)=>{"use strict";var r=s(8999);s.o(r,"usePathname")&&s.d(t,{usePathname:function(){return r.usePathname}}),s.o(r,"useRouter")&&s.d(t,{useRouter:function(){return r.useRouter}})},5876:(e,t,s)=>{"use strict";s.d(t,{A:()=>n});var r=s(5155),l=s(2115),a=s(5695),i=s(844),d=s(4338);function n(e){let{children:t,requiredRole:s,redirectTo:n="/auth/login"}=e,{user:c,userProfile:o,loading:m,isAuthenticated:u}=(0,i.A)(),x=(0,a.useRouter)();return((0,l.useEffect)(()=>{if(!m){if(!u)return void x.push(n);if(s&&o&&o.role!==s)return void x.push("/dashboard")}},[c,o,m,u,s,x,n]),m||!u||s&&o&&o.role!==s)?(0,r.jsx)(d.A,{}):(0,r.jsx)(r.Fragment,{children:t})}},6181:(e,t,s)=>{Promise.resolve().then(s.bind(s,8398))},8398:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>y});var r=s(5155),l=s(2115),a=s(844),i=s(5876),d=s(1007),n=s(4416);let c=(0,s(9946).A)("pen",[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}]]);var o=s(8883),m=s(9074),u=s(4229),x=s(5040),h=s(9037),g=s(4186);function y(){var e,t,s,y;let{userProfile:p,updateUserProfile:j,user:v}=(0,a.A)(),[f,b]=(0,l.useState)(!1),[N,w]=(0,l.useState)({displayName:(null==p?void 0:p.displayName)||"",email:(null==p?void 0:p.email)||"",role:(null==p?void 0:p.role)||"student"}),A=async()=>{try{await j({displayName:N.displayName,role:N.role}),b(!1)}catch(e){console.error("Error updating profile:",e)}},k=e=>e?new Date(e).toLocaleDateString("ar-SA"):"غير محدد";return(0,r.jsx)(i.A,{children:(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)("div",{className:"bg-white border-b border-gray-200",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:[(0,r.jsxs)("h1",{className:"text-2xl font-bold text-gray-900 flex items-center",dir:"rtl",children:[(0,r.jsx)(d.A,{className:"w-6 h-6 ml-3"}),"الملف الشخصي"]}),(0,r.jsx)("p",{className:"text-gray-600 mt-1",dir:"rtl",children:"عرض وتحديث معلوماتك الشخصية"})]})}),(0,r.jsx)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-gray-900",dir:"rtl",children:"المعلومات الأساسية"}),(0,r.jsx)("button",{onClick:()=>b(!f),className:"flex items-center px-3 py-2 text-sm font-medium text-blue-600 hover:text-blue-700",dir:"rtl",children:f?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(n.A,{className:"w-4 h-4 ml-1"}),"إلغاء"]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(c,{className:"w-4 h-4 ml-1"}),"تعديل"]})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"col-span-full flex items-center space-x-6 space-x-reverse",children:[(0,r.jsx)("div",{className:"w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center",children:(null==p?void 0:p.photoURL)?(0,r.jsx)("img",{src:p.photoURL,alt:"Profile",className:"w-20 h-20 rounded-full object-cover"}):(0,r.jsx)(d.A,{className:"w-10 h-10 text-blue-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900",dir:"rtl",children:(null==p?void 0:p.displayName)||"مستخدم جديد"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",dir:"rtl",children:(null==p?void 0:p.role)==="teacher"?"معلم":(null==p?void 0:p.role)==="student"?"طالب":"مدير"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",dir:"rtl",children:"الاسم الكامل"}),f?(0,r.jsx)("input",{type:"text",value:N.displayName,onChange:e=>w(t=>({...t,displayName:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",dir:"rtl"}):(0,r.jsx)("p",{className:"text-gray-900",dir:"rtl",children:(null==p?void 0:p.displayName)||"غير محدد"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",dir:"rtl",children:"البريد الإلكتروني"}),(0,r.jsxs)("p",{className:"text-gray-900 flex items-center",dir:"rtl",children:[(0,r.jsx)(o.A,{className:"w-4 h-4 ml-2"}),null==p?void 0:p.email]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",dir:"rtl",children:"الدور"}),f?(0,r.jsxs)("select",{value:N.role,onChange:e=>w(t=>({...t,role:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",dir:"rtl",children:[(0,r.jsx)("option",{value:"student",children:"طالب"}),(0,r.jsx)("option",{value:"teacher",children:"معلم"})]}):(0,r.jsx)("p",{className:"text-gray-900",dir:"rtl",children:(null==p?void 0:p.role)==="teacher"?"معلم":(null==p?void 0:p.role)==="student"?"طالب":"مدير"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",dir:"rtl",children:"تاريخ الانضمام"}),(0,r.jsxs)("p",{className:"text-gray-900 flex items-center",dir:"rtl",children:[(0,r.jsx)(m.A,{className:"w-4 h-4 ml-2"}),k(null==p?void 0:p.createdAt)]})]})]}),f&&(0,r.jsx)("div",{className:"mt-6 flex justify-end",children:(0,r.jsxs)("button",{onClick:A,className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",dir:"rtl",children:[(0,r.jsx)(u.A,{className:"w-4 h-4 ml-2"}),"حفظ التغييرات"]})})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-6",dir:"rtl",children:"الإحصائيات"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3",children:(0,r.jsx)(x.A,{className:"w-6 h-6 text-blue-600"})}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:(null==p||null==(e=p.stats)?void 0:e.totalExams)||0}),(0,r.jsx)("p",{className:"text-sm text-gray-500",dir:"rtl",children:"إجمالي الامتحانات"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3",children:(0,r.jsx)(h.A,{className:"w-6 h-6 text-green-600"})}),(0,r.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[(null==p||null==(t=p.stats)?void 0:t.averageScore)||0,"%"]}),(0,r.jsx)("p",{className:"text-sm text-gray-500",dir:"rtl",children:"متوسط النتائج"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-3",children:(0,r.jsx)(x.A,{className:"w-6 h-6 text-purple-600"})}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:(null==p||null==(s=p.stats)?void 0:s.totalQuestions)||0}),(0,r.jsx)("p",{className:"text-sm text-gray-500",dir:"rtl",children:"إجمالي الأسئلة"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mx-auto mb-3",children:(0,r.jsx)(g.A,{className:"w-6 h-6 text-orange-600"})}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:Math.round(((null==p||null==(y=p.stats)?void 0:y.studyTime)||0)/60)}),(0,r.jsx)("p",{className:"text-sm text-gray-500",dir:"rtl",children:"ساعات الدراسة"})]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-6",dir:"rtl",children:"أمان الحساب"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg",children:[(0,r.jsxs)("div",{dir:"rtl",children:[(0,r.jsx)("h3",{className:"font-medium text-gray-900",children:"كلمة المرور"}),(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:["آخر تحديث: ",k(null==p?void 0:p.lastLoginAt)]})]}),(0,r.jsx)("button",{className:"px-4 py-2 text-sm font-medium text-blue-600 hover:text-blue-700",children:"تغيير كلمة المرور"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg",children:[(0,r.jsxs)("div",{dir:"rtl",children:[(0,r.jsx)("h3",{className:"font-medium text-gray-900",children:"المصادقة الثنائية"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"حماية إضافية لحسابك"})]}),(0,r.jsx)("button",{className:"px-4 py-2 text-sm font-medium text-blue-600 hover:text-blue-700",children:"تفعيل"})]})]})]})]})})]})})}},8883:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},9037:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},9074:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[992,811,100,470,441,684,358],()=>t(6181)),_N_E=e.O()}]);