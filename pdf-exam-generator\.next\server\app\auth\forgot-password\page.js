(()=>{var e={};e.id=413,e.ids=[413],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19128:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var s=r(65239),a=r(48088),i=r(88170),n=r.n(i),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["auth",{children:["forgot-password",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,81165)),"D:\\templete\\pdf-exam-generator\\src\\app\\auth\\forgot-password\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:"/manifest.webmanifest"}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"D:\\templete\\pdf-exam-generator\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:"/manifest.webmanifest"}}]}.children,c=["D:\\templete\\pdf-exam-generator\\src\\app\\auth\\forgot-password\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/auth/forgot-password/page",pathname:"/auth/forgot-password",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28559:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31843:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var s=r(60687),a=r(43210),i=r(85814),n=r.n(i),o=r(7613),l=r(5336),d=r(28559),c=r(41550);function u(){let[e,t]=(0,a.useState)(""),[r,i]=(0,a.useState)(""),[u,m]=(0,a.useState)(!1),[p,x]=(0,a.useState)(!1),{resetPassword:h}=(0,o.A)(),f=async t=>{t.preventDefault(),i(""),m(!0);try{await h(e),x(!0)}catch(e){i(b(e.code||"unknown-error"))}finally{m(!1)}},b=e=>{switch(e){case"auth/user-not-found":return"لا يوجد حساب مرتبط بهذا البريد الإلكتروني";case"auth/invalid-email":return"البريد الإلكتروني غير صالح";case"auth/too-many-requests":return"تم تجاوز عدد المحاولات المسموح. حاول مرة أخرى لاحقاً";default:return"حدث خطأ أثناء إرسال رابط إعادة التعيين. حاول مرة أخرى"}};return p?(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4",children:(0,s.jsx)("div",{className:"max-w-md w-full space-y-8",children:(0,s.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl p-8 text-center",children:[(0,s.jsx)("div",{className:"mx-auto h-16 w-16 bg-green-100 rounded-full flex items-center justify-center mb-6",children:(0,s.jsx)(l.A,{className:"h-8 w-8 text-green-600"})}),(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"تم إرسال الرابط!"}),(0,s.jsx)("p",{className:"text-gray-600 mb-6",children:"تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني:"}),(0,s.jsx)("p",{className:"text-blue-600 font-medium mb-6",children:e}),(0,s.jsx)("p",{className:"text-sm text-gray-500 mb-8",children:"تحقق من صندوق الوارد وصندوق الرسائل المزعجة. قد يستغرق وصول الرسالة بضع دقائق."}),(0,s.jsxs)(n(),{href:"/auth/login",className:"inline-flex items-center justify-center w-full py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors",children:[(0,s.jsx)(d.A,{className:"h-4 w-4 ml-2"}),"العودة إلى تسجيل الدخول"]})]})})}):(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4",children:(0,s.jsx)("div",{className:"max-w-md w-full space-y-8",children:(0,s.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl p-8",children:[(0,s.jsxs)("div",{className:"text-center mb-8",children:[(0,s.jsx)("div",{className:"mx-auto h-12 w-12 bg-blue-600 rounded-xl flex items-center justify-center mb-4",children:(0,s.jsx)(c.A,{className:"h-6 w-6 text-white"})}),(0,s.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-2",children:"نسيت كلمة المرور؟"}),(0,s.jsx)("p",{className:"text-gray-600",children:"أدخل بريدك الإلكتروني وسنرسل لك رابط إعادة التعيين"})]}),r&&(0,s.jsx)("div",{className:"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg",children:(0,s.jsx)("p",{className:"text-red-600 text-sm text-center",children:r})}),(0,s.jsxs)("form",{onSubmit:f,className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"البريد الإلكتروني"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:(0,s.jsx)(c.A,{className:"h-5 w-5 text-gray-400"})}),(0,s.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:e,onChange:e=>t(e.target.value),className:"block w-full pr-10 pl-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-right",placeholder:"أدخل بريدك الإلكتروني",dir:"rtl"})]})]}),(0,s.jsx)("button",{type:"submit",disabled:u,className:"w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:u?(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"}),"جاري الإرسال..."]}):"إرسال رابط إعادة التعيين"})]}),(0,s.jsx)("div",{className:"mt-6 text-center",children:(0,s.jsxs)(n(),{href:"/auth/login",className:"inline-flex items-center text-sm text-blue-600 hover:text-blue-500",children:[(0,s.jsx)(d.A,{className:"h-4 w-4 ml-1"}),"العودة إلى تسجيل الدخول"]})})]})})})}},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37366:e=>{"use strict";e.exports=require("dns")},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81165:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\templete\\\\pdf-exam-generator\\\\src\\\\app\\\\auth\\\\forgot-password\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\templete\\pdf-exam-generator\\src\\app\\auth\\forgot-password\\page.tsx","default")},81630:e=>{"use strict";e.exports=require("http")},83377:(e,t,r)=>{Promise.resolve().then(r.bind(r,31843))},91645:e=>{"use strict";e.exports=require("net")},93105:(e,t,r)=>{Promise.resolve().then(r.bind(r,81165))},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,248,658,647],()=>r(19128));module.exports=s})();