1:"$Sreact.fragment"
2:I[9283,["992","static/chunks/bc9e92e6-c7f3b62d1269ad72.js","811","static/chunks/ae6eea6a-2eeb374ab7242b27.js","100","static/chunks/100-d321196fa9edc660.js","470","static/chunks/470-0d186e1d59a76dd3.js","874","static/chunks/874-47544833a2996116.js","642","static/chunks/642-5daa29c205a82658.js","177","static/chunks/app/layout-93b1c4d11822a0bd.js"],"LanguageProvider"]
3:I[844,["992","static/chunks/bc9e92e6-c7f3b62d1269ad72.js","811","static/chunks/ae6eea6a-2eeb374ab7242b27.js","100","static/chunks/100-d321196fa9edc660.js","470","static/chunks/470-0d186e1d59a76dd3.js","874","static/chunks/874-47544833a2996116.js","642","static/chunks/642-5daa29c205a82658.js","177","static/chunks/app/layout-93b1c4d11822a0bd.js"],"AuthProvider"]
4:I[4791,["992","static/chunks/bc9e92e6-c7f3b62d1269ad72.js","811","static/chunks/ae6eea6a-2eeb374ab7242b27.js","100","static/chunks/100-d321196fa9edc660.js","470","static/chunks/470-0d186e1d59a76dd3.js","874","static/chunks/874-47544833a2996116.js","642","static/chunks/642-5daa29c205a82658.js","177","static/chunks/app/layout-93b1c4d11822a0bd.js"],"AppContent"]
5:I[7555,[],""]
6:I[1295,[],""]
7:I[894,[],"ClientPageRoot"]
8:I[730,["992","static/chunks/bc9e92e6-c7f3b62d1269ad72.js","811","static/chunks/ae6eea6a-2eeb374ab7242b27.js","100","static/chunks/100-d321196fa9edc660.js","470","static/chunks/470-0d186e1d59a76dd3.js","874","static/chunks/874-47544833a2996116.js","859","static/chunks/app/auth/login/page-b684800d91643efe.js"],"default"]
b:I[9665,[],"OutletBoundary"]
e:I[4911,[],"AsyncMetadataOutlet"]
10:I[9665,[],"ViewportBoundary"]
12:I[9665,[],"MetadataBoundary"]
14:I[6614,[],""]
:HL["/_next/static/css/c04a1b321be81a74.css","style"]
0:{"P":null,"b":"rIw512Ff4BmrOpBeiV-O-","p":"","c":["","auth","login",""],"i":false,"f":[[["",{"children":["auth",{"children":["login",{"children":["__PAGE__",{}]}]}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/c04a1b321be81a74.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"en","className":"__variable_e8ce0c","children":["$","body",null,{"className":"font-inter antialiased bg-gray-50 text-gray-900 min-h-screen","children":["$","$L2",null,{"children":["$","$L3",null,{"children":["$","$L4",null,{"children":["$","$L5",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L6",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}]}]}]}]}]]}],{"children":["auth",["$","$1","c",{"children":[null,["$","$L5",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L6",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["login",["$","$1","c",{"children":[null,["$","$L5",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L6",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","$L7",null,{"Component":"$8","searchParams":{},"params":{},"promises":["$@9","$@a"]}],null,["$","$Lb",null,{"children":["$Lc","$Ld",["$","$Le",null,{"promise":"$@f"}]]}]]}],{},null,false]},null,false]},null,false]},null,false],["$","$1","h",{"children":[null,["$","$1","y9C5IfrCPjwDm6UaBqNeSv",{"children":[["$","$L10",null,{"children":"$L11"}],null]}],["$","$L12",null,{"children":"$L13"}]]}],false]],"m":"$undefined","G":["$14","$undefined"],"s":false,"S":true}
15:"$Sreact.suspense"
16:I[4911,[],"AsyncMetadata"]
9:{}
a:{}
13:["$","div",null,{"hidden":true,"children":["$","$15",null,{"fallback":null,"children":["$","$L16",null,{"promise":"$@17"}]}]}]
d:null
11:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
c:null
f:{"metadata":[["$","title","0",{"children":"PDF to Interactive Exam Generator"}],["$","meta","1",{"name":"description","content":"Transform PDF documents into interactive exams using AI. Perfect for teachers, students, and e-learning platforms."}],["$","meta","2",{"name":"author","content":"PDF Exam Generator Team"}],["$","meta","3",{"name":"keywords","content":"PDF,exam,interactive,AI,education,e-learning"}],["$","link","4",{"rel":"icon","href":"/favicon.ico","type":"image/x-icon","sizes":"16x16"}]],"error":null,"digest":"$undefined"}
17:{"metadata":"$f:metadata","error":null,"digest":"$undefined"}
