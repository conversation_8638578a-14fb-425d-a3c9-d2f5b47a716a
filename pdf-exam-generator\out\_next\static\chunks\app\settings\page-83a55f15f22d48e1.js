(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[662],{381:(e,a,t)=>{"use strict";t.d(a,{A:()=>r});let r=(0,t(9946).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},844:(e,a,t)=>{"use strict";t.d(a,{AuthProvider:()=>p,A:()=>f});var r=t(5155),s=t(2115),l=t(6203),i=t(5317),n=t(3915),o=t(858);let c={apiKey:"demo-key",authDomain:"demo-project.firebaseapp.com",projectId:"demo-project",storageBucket:"demo-project.appspot.com",messagingSenderId:"*********",appId:"1:*********:web:abcdef",measurementId:t(9509).env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID||"G-ABCDEF"},d=0===(0,n.Dk)().length?(0,n.Wp)(c):(0,n.Dk)()[0],u=(0,l.xI)(d),m=(0,i.aU)(d);(0,o.c7)(d);let h=new l.HF,x=new l.sk;h.setCustomParameters({prompt:"select_account"}),x.setCustomParameters({display:"popup"});let g=(0,s.createContext)(void 0),f=()=>{let e=(0,s.useContext)(g);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e},p=e=>{let{children:a}=e,[t,n]=(0,s.useState)(null),[o,c]=(0,s.useState)(null),[d,f]=(0,s.useState)(!0),p=async function(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!e)return;let t=(0,i.H9)(m,"users",e.uid),r=await (0,i.x7)(t);if(r.exists()){let e={...r.data(),lastLoginAt:new Date};await (0,i.BN)(t,e,{merge:!0}),c(e)}else{let{displayName:r,email:s,photoURL:l}=e,n=new Date,o={uid:e.uid,email:s||"",displayName:r||"",photoURL:l||void 0,role:a.role||"student",createdAt:n,lastLoginAt:n,preferences:{language:"ar",theme:"light",notifications:!0},stats:{totalExams:0,averageScore:0,totalQuestions:0,studyTime:0},...a};try{await (0,i.BN)(t,o),c(o)}catch(e){console.error("Error creating user profile:",e)}}},y=async(e,a)=>{f(!0);try{let t=await (0,l.x9)(u,e,a);await p(t.user)}catch(e){throw console.error("Error signing in:",e),e}finally{f(!1)}},b=async(e,a)=>{try{return await y(e,a),!0}catch(e){return!1}},v=async(e,a,t,r)=>{f(!0);try{let s=await (0,l.eJ)(u,e,a);await (0,l.r7)(s.user,{displayName:t}),await p(s.user,{role:r,displayName:t})}catch(e){throw console.error("Error signing up:",e),e}finally{f(!1)}},j=async()=>{f(!0);try{let e=await (0,l.df)(u,h);await p(e.user)}catch(e){throw console.error("Error signing in with Google:",e),e}finally{f(!1)}},N=async()=>{f(!0);try{let e=await (0,l.df)(u,x);await p(e.user)}catch(e){throw console.error("Error signing in with Facebook:",e),e}finally{f(!1)}},w=async()=>{f(!0);try{await (0,l.CI)(u),n(null),c(null),localStorage.removeItem("examResults"),localStorage.removeItem("examAnswers"),localStorage.removeItem("uploadedPDF"),localStorage.removeItem("examConfig")}catch(e){throw console.error("Error signing out:",e),e}finally{f(!1)}},k=async e=>{try{await (0,l.J1)(u,e)}catch(e){throw console.error("Error sending password reset email:",e),e}},A=async e=>{if(t)try{let a=(0,i.H9)(m,"users",t.uid);await (0,i.BN)(a,e,{merge:!0}),o&&c({...o,...e})}catch(e){throw console.error("Error updating user profile:",e),e}};return(0,s.useEffect)(()=>(0,l.hg)(u,async e=>{e?(n(e),await p(e)):(n(null),c(null)),f(!1)}),[]),(0,r.jsx)(g.Provider,{value:{user:t,userProfile:o,loading:d,isAuthenticated:!!t,signIn:y,signUp:v,signInWithGoogle:j,signInWithFacebook:N,logout:w,resetPassword:k,updateUserProfile:A,login:b},children:a})}},1007:(e,a,t)=>{"use strict";t.d(a,{A:()=>r});let r=(0,t(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},2608:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>h});var r=t(5155),s=t(2115),l=t(844),i=t(5876),n=t(381),o=t(1007),c=t(3861),d=t(5525),u=t(4869),m=t(4229);function h(){let{userProfile:e,updateUserProfile:a}=(0,l.A)(),[t,h]=(0,s.useState)({language:"ar",theme:"light",defaultDifficulty:"intermediate",defaultQuestionCount:20,emailNotifications:!0,examReminders:!0,resultNotifications:!1,dataSharing:!1,analytics:!0,apiKey:"",aiProvider:"openai"}),[x,g]=(0,s.useState)(!1);(0,s.useEffect)(()=>{(null==e?void 0:e.preferences)&&h(a=>({...a,language:e.preferences.language,theme:e.preferences.theme,emailNotifications:e.preferences.notifications}))},[e]);let f=async()=>{try{await a({preferences:{language:t.language,theme:t.theme,notifications:t.emailNotifications}}),localStorage.setItem("userSettings",JSON.stringify(t)),g(!0),setTimeout(()=>g(!1),2e3)}catch(e){console.error("Error saving settings:",e)}},p=(e,a)=>{h(t=>({...t,[e]:a}))};return(0,r.jsx)(i.A,{children:(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)("div",{className:"bg-white border-b border-gray-200",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:[(0,r.jsxs)("h1",{className:"text-2xl font-bold text-gray-900 flex items-center",dir:"rtl",children:[(0,r.jsx)(n.A,{className:"w-6 h-6 ml-3"}),"الإعدادات"]}),(0,r.jsx)("p",{className:"text-gray-600 mt-1",dir:"rtl",children:"إدارة تفضيلاتك وإعدادات حسابك"})]})}),(0,r.jsx)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsxs)("h2",{className:"text-lg font-semibold text-gray-900 mb-6 flex items-center",dir:"rtl",children:[(0,r.jsx)(o.A,{className:"w-5 h-5 ml-2"}),"تفضيلات المستخدم"]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",dir:"rtl",children:"اللغة الافتراضية"}),(0,r.jsxs)("select",{value:t.language,onChange:e=>p("language",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",dir:"rtl",children:[(0,r.jsx)("option",{value:"en",children:"English"}),(0,r.jsx)("option",{value:"ar",children:"العربية"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",dir:"rtl",children:"المظهر"}),(0,r.jsxs)("select",{value:t.theme,onChange:e=>p("theme",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",dir:"rtl",children:[(0,r.jsx)("option",{value:"light",children:"فاتح"}),(0,r.jsx)("option",{value:"dark",children:"داكن"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Default Difficulty"}),(0,r.jsxs)("select",{value:t.defaultDifficulty,onChange:e=>p("defaultDifficulty",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[(0,r.jsx)("option",{value:"beginner",children:"Beginner"}),(0,r.jsx)("option",{value:"intermediate",children:"Intermediate"}),(0,r.jsx)("option",{value:"advanced",children:"Advanced"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Default Question Count"}),(0,r.jsx)("input",{type:"number",min:"5",max:"50",value:t.defaultQuestionCount,onChange:e=>p("defaultQuestionCount",parseInt(e.target.value)),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsxs)("h2",{className:"text-lg font-semibold text-gray-900 mb-6 flex items-center",children:[(0,r.jsx)(c.A,{className:"w-5 h-5 mr-2"}),"Notifications"]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("label",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"checkbox",checked:t.emailNotifications,onChange:e=>p("emailNotifications",e.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,r.jsx)("span",{className:"ml-3 text-sm text-gray-700",children:"Email notifications"})]}),(0,r.jsxs)("label",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"checkbox",checked:t.examReminders,onChange:e=>p("examReminders",e.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,r.jsx)("span",{className:"ml-3 text-sm text-gray-700",children:"Exam reminders"})]}),(0,r.jsxs)("label",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"checkbox",checked:t.resultNotifications,onChange:e=>p("resultNotifications",e.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,r.jsx)("span",{className:"ml-3 text-sm text-gray-700",children:"Result notifications"})]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsxs)("h2",{className:"text-lg font-semibold text-gray-900 mb-6 flex items-center",children:[(0,r.jsx)(d.A,{className:"w-5 h-5 mr-2"}),"Privacy & Security"]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("label",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"checkbox",checked:t.dataSharing,onChange:e=>p("dataSharing",e.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,r.jsx)("span",{className:"ml-3 text-sm text-gray-700",children:"Allow data sharing for service improvement"})]}),(0,r.jsxs)("label",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"checkbox",checked:t.analytics,onChange:e=>p("analytics",e.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,r.jsx)("span",{className:"ml-3 text-sm text-gray-700",children:"Enable analytics to help improve the platform"})]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsxs)("h2",{className:"text-lg font-semibold text-gray-900 mb-6 flex items-center",children:[(0,r.jsx)(u.A,{className:"w-5 h-5 mr-2"}),"AI Integration"]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"AI Provider"}),(0,r.jsxs)("select",{value:t.aiProvider,onChange:e=>p("aiProvider",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[(0,r.jsx)("option",{value:"openai",children:"OpenAI"}),(0,r.jsx)("option",{value:"anthropic",children:"Anthropic"}),(0,r.jsx)("option",{value:"google",children:"Google AI"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"API Key (Optional)"}),(0,r.jsx)("input",{type:"password",value:t.apiKey,onChange:e=>p("apiKey",e.target.value),placeholder:"Enter your API key for enhanced features",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),(0,r.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Providing your own API key enables unlimited usage and advanced features"})]})]})]}),(0,r.jsx)("div",{className:"flex justify-end",children:(0,r.jsxs)("button",{onClick:f,className:"flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-all duration-200 ".concat(x?"bg-green-600 text-white":"bg-blue-600 text-white hover:bg-blue-700"),children:[(0,r.jsx)(m.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:x?"Saved!":"Save Settings"})]})})]})})]})})}},3861:(e,a,t)=>{"use strict";t.d(a,{A:()=>r});let r=(0,t(9946).A)("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]])},4229:(e,a,t)=>{"use strict";t.d(a,{A:()=>r});let r=(0,t(9946).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},4338:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});var r=t(5155);function s(e){let{size:a="md",color:t="blue",text:s,fullScreen:l=!1}=e,i=(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full border-2 border-gray-200 ".concat({blue:"border-blue-600",green:"border-green-600",gray:"border-gray-600"}[t]," border-t-transparent ").concat({sm:"h-4 w-4",md:"h-8 w-8",lg:"h-12 w-12"}[a])}),s&&(0,r.jsx)("p",{className:"mt-2 text-sm text-gray-600",children:s})]});return l?(0,r.jsx)("div",{className:"fixed inset-0 bg-white bg-opacity-75 flex items-center justify-center z-50",children:i}):i}},4869:(e,a,t)=>{"use strict";t.d(a,{A:()=>r});let r=(0,t(9946).A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},5525:(e,a,t)=>{"use strict";t.d(a,{A:()=>r});let r=(0,t(9946).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},5695:(e,a,t)=>{"use strict";var r=t(8999);t.o(r,"usePathname")&&t.d(a,{usePathname:function(){return r.usePathname}}),t.o(r,"useRouter")&&t.d(a,{useRouter:function(){return r.useRouter}})},5876:(e,a,t)=>{"use strict";t.d(a,{A:()=>o});var r=t(5155),s=t(2115),l=t(5695),i=t(844),n=t(4338);function o(e){let{children:a,requiredRole:t,redirectTo:o="/auth/login"}=e,{user:c,userProfile:d,loading:u,isAuthenticated:m}=(0,i.A)(),h=(0,l.useRouter)();return((0,s.useEffect)(()=>{if(!u){if(!m)return void h.push(o);if(t&&d&&d.role!==t)return void h.push("/dashboard")}},[c,d,u,m,t,h,o]),u||!m||t&&d&&d.role!==t)?(0,r.jsx)(n.A,{}):(0,r.jsx)(r.Fragment,{children:a})}},7887:(e,a,t)=>{Promise.resolve().then(t.bind(t,2608))}},e=>{var a=a=>e(e.s=a);e.O(0,[992,811,100,470,441,684,358],()=>a(7887)),_N_E=e.O()}]);