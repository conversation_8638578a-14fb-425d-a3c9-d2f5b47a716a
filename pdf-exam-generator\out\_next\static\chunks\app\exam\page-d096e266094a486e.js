(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[344],{646:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},3789:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>f});var a=s(5155),r=s(2115),n=s(5695),i=s(9946);let l=(0,i.A)("flag",[["path",{d:"M4 22V4a1 1 0 0 1 .4-.8A6 6 0 0 1 8 2c3 0 5 2 7.333 2q2 0 3.067-.8A1 1 0 0 1 20 4v10a1 1 0 0 1-.4.8A6 6 0 0 1 16 16c-3 0-5-2-8-2a6 6 0 0 0-4 1.528",key:"1jaruq"}]]),c=(0,i.A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]);var o=s(646);let d=(0,i.A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);function x(e){let{questions:t,currentQuestion:s,answers:n,onAnswerChange:i,onQuestionChange:x,onSubmit:m}=e,[u,h]=(0,r.useState)(new Set),g=t[s],p=n[g.id];return(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden",children:[(0,a.jsx)("div",{className:"px-6 py-4 border-b border-gray-200 bg-gray-50",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("span",{className:"text-sm font-medium text-gray-600",children:["Question ",s+1," of ",t.length]}),(0,a.jsx)("span",{className:"px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded-full capitalize",children:g.type.replace("-"," ")})]}),(0,a.jsxs)("button",{onClick:()=>{h(e=>{let t=new Set(e);return t.has(g.id)?t.delete(g.id):t.add(g.id),t})},className:"flex items-center space-x-1 px-3 py-1 rounded-lg transition-colors ".concat(u.has(g.id)?"bg-yellow-100 text-yellow-800":"bg-gray-100 text-gray-600 hover:bg-gray-200"),children:[(0,a.jsx)(l,{className:"w-4 h-4"}),(0,a.jsx)("span",{className:"text-sm",children:u.has(g.id)?"Flagged":"Flag"})]})]})}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("h2",{className:"text-lg font-medium text-gray-900 mb-6 leading-relaxed",children:g.question}),(()=>{switch(g.type){case"multiple-choice":var e;return(0,a.jsx)("div",{className:"space-y-3",children:null==(e=g.options)?void 0:e.map((e,t)=>(0,a.jsxs)("label",{className:"flex items-start space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors",children:[(0,a.jsx)("input",{type:"radio",name:"question-".concat(g.id),value:t,checked:p===t,onChange:()=>i(g.id,t),className:"mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"}),(0,a.jsx)("span",{className:"text-gray-900 flex-1",children:e})]},t))});case"true-false":return(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("label",{className:"flex items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors",children:[(0,a.jsx)("input",{type:"radio",name:"question-".concat(g.id),value:"true",checked:!0===p,onChange:()=>i(g.id,!0),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"}),(0,a.jsx)("span",{className:"text-gray-900",children:"True"})]}),(0,a.jsxs)("label",{className:"flex items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors",children:[(0,a.jsx)("input",{type:"radio",name:"question-".concat(g.id),value:"false",checked:!1===p,onChange:()=>i(g.id,!1),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"}),(0,a.jsx)("span",{className:"text-gray-900",children:"False"})]})]});case"short-answer":return(0,a.jsx)("textarea",{value:p||"",onChange:e=>i(g.id,e.target.value),placeholder:"Type your answer here...",className:"w-full h-32 p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"});case"fill-blank":return(0,a.jsx)("div",{className:"space-y-4",children:(0,a.jsx)("p",{className:"text-gray-700",children:g.question.split("______").map((e,t,s)=>(0,a.jsxs)("span",{children:[e,t<s.length-1&&(0,a.jsx)("input",{type:"text",value:p||"",onChange:e=>i(g.id,e.target.value),className:"mx-2 px-3 py-1 border-b-2 border-blue-300 focus:outline-none focus:border-blue-500 bg-transparent min-w-32",placeholder:"your answer"})]},t))})});default:return null}})()]}),(0,a.jsx)("div",{className:"px-6 py-4 border-t border-gray-200 bg-gray-50",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("button",{onClick:()=>{s>0&&x(s-1)},disabled:0===s,className:"flex items-center space-x-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:[(0,a.jsx)(c,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Previous"})]}),(0,a.jsx)("div",{className:"flex items-center space-x-2",children:t.map((e,r)=>(0,a.jsx)("button",{onClick:()=>x(r),className:"w-8 h-8 rounded-full text-sm font-medium transition-colors ".concat(r===s?"bg-blue-600 text-white":void 0!==n[t[r].id]?"bg-green-100 text-green-800":"bg-gray-100 text-gray-600 hover:bg-gray-200"),children:r+1},r))}),s===t.length-1?(0,a.jsxs)("button",{onClick:m,className:"flex items-center space-x-2 px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium",children:[(0,a.jsx)(o.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Submit Exam"})]}):(0,a.jsxs)("button",{onClick:()=>{s<t.length-1&&x(s+1)},className:"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,a.jsx)("span",{children:"Next"}),(0,a.jsx)(d,{className:"w-4 h-4"})]})]})}),(0,a.jsxs)("div",{className:"border-t border-gray-200 bg-gray-50 p-4",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-900 mb-3",children:"Question Overview"}),(0,a.jsx)("div",{className:"grid grid-cols-10 gap-2",children:t.map((e,r)=>{let i=void 0!==n[t[r].id],c=u.has(t[r].id),o=r===s;return(0,a.jsxs)("button",{onClick:()=>x(r),className:"relative w-8 h-8 rounded text-xs font-medium transition-colors ".concat(o?"bg-blue-600 text-white":i?"bg-green-100 text-green-800 hover:bg-green-200":"bg-gray-100 text-gray-600 hover:bg-gray-200"),children:[r+1,c&&(0,a.jsx)(l,{className:"absolute -top-1 -right-1 w-3 h-3 text-yellow-500"})]},r)})}),(0,a.jsxs)("div",{className:"mt-4 flex items-center justify-between text-xs text-gray-600",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)("div",{className:"w-3 h-3 bg-green-100 rounded"}),(0,a.jsx)("span",{children:"Answered"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)("div",{className:"w-3 h-3 bg-gray-100 rounded"}),(0,a.jsx)("span",{children:"Unanswered"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(l,{className:"w-3 h-3 text-yellow-500"}),(0,a.jsx)("span",{children:"Flagged"})]})]}),(0,a.jsxs)("span",{children:[Object.keys(n).length,"/",t.length," completed"]})]})]})]})}let m=(0,i.A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);var u=s(4186);function h(e){let{timeRemaining:t,onTimeUp:s,onTimeUpdate:n}=e,[i,l]=(0,r.useState)(t);(0,r.useEffect)(()=>{l(t)},[t]),(0,r.useEffect)(()=>{if(i<=0)return void s();let e=setInterval(()=>{l(e=>{let t=e-1;return(n(t),t<=0)?(s(),0):t})},1e3);return()=>clearInterval(e)},[i,s,n]);let c=i<=600,o=i<=300;return(0,a.jsxs)("div",{className:"flex items-center space-x-2 px-3 py-2 rounded-lg border ".concat(i<=300?"bg-red-50 border-red-200":i<=600?"bg-yellow-50 border-yellow-200":"bg-gray-50 border-gray-200"),children:[c?(0,a.jsx)(m,{className:"w-4 h-4 ".concat(o?"text-red-600":"text-yellow-600")}):(0,a.jsx)(u.A,{className:"w-4 h-4 text-gray-600"}),(0,a.jsx)("span",{className:"font-mono text-sm font-medium ".concat(i<=300?"text-red-600":i<=600?"text-yellow-600":"text-gray-700"),children:(e=>{let t=Math.floor(e/3600),s=Math.floor(e%3600/60),a=e%60;return t>0?"".concat(t,":").concat(s.toString().padStart(2,"0"),":").concat(a.toString().padStart(2,"0")):"".concat(s,":").concat(a.toString().padStart(2,"0"))})(i)}),c&&(0,a.jsx)("span",{className:"text-xs ".concat(o?"text-red-600":"text-yellow-600"),children:o?"Time running out!":"Time warning"})]})}let g=(0,i.A)("circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]);function p(e){let{current:t,total:s,answered:r}=e,n=r/s*100;return(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Progress:"}),(0,a.jsx)("div",{className:"w-32 bg-gray-200 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:"".concat(n,"%")}})}),(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:[r,"/",s]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-600",children:[(0,a.jsx)(g,{className:"w-4 h-4"}),(0,a.jsxs)("span",{children:["Question ",t]})]})]})}var b=s(7550);let y=[{id:1,type:"multiple-choice",question:"What is the primary goal of supervised learning in machine learning?",options:["To find hidden patterns in unlabeled data","To learn from labeled training data to make predictions on new data","To reduce the dimensionality of the dataset","To cluster similar data points together"],correctAnswer:1,explanation:"Supervised learning uses labeled training data to learn patterns and make predictions on new, unseen data."},{id:2,type:"true-false",question:"Neural networks can only be used for classification problems.",correctAnswer:!1,explanation:"Neural networks can be used for both classification and regression problems, as well as other tasks like clustering and dimensionality reduction."},{id:3,type:"multiple-choice",question:"Which of the following is NOT a common activation function in neural networks?",options:["ReLU (Rectified Linear Unit)","Sigmoid","Tanh","Linear Regression"],correctAnswer:3,explanation:"Linear Regression is a machine learning algorithm, not an activation function. ReLU, Sigmoid, and Tanh are all common activation functions."},{id:4,type:"short-answer",question:"Explain what overfitting means in machine learning and how it can be prevented.",correctAnswer:"Overfitting occurs when a model learns the training data too well, including noise and irrelevant patterns, leading to poor performance on new data. It can be prevented through techniques like cross-validation, regularization, early stopping, and using more training data.",explanation:"Overfitting is a common problem where the model memorizes the training data rather than learning generalizable patterns."},{id:5,type:"fill-blank",question:"The process of adjusting the weights in a neural network during training is called ______.",correctAnswer:"backpropagation",explanation:"Backpropagation is the algorithm used to calculate gradients and update weights in neural networks during training."}];function f(){let e=(0,n.useRouter)(),[t,s]=(0,r.useState)(null),[i,l]=(0,r.useState)(0),[c,o]=(0,r.useState)({}),[d,m]=(0,r.useState)(null),[u,g]=(0,r.useState)(!1),[f,j]=(0,r.useState)(!1);(0,r.useEffect)(()=>{let t=localStorage.getItem("examConfig");if(t){let e=JSON.parse(t);s(e),e.hasTimeLimit&&m(60*e.timeLimit)}else e.push("/analyze")},[e]),(0,r.useEffect)(()=>{Object.keys(c).length>0&&localStorage.setItem("examAnswers",JSON.stringify(c))},[c]);let N=()=>{localStorage.setItem("examResults",JSON.stringify({answers:c,questions:y,config:t,completedAt:new Date().toISOString()})),e.push("/results")};return t?u?(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,a.jsx)("div",{className:"bg-white border-b border-gray-200 sticky top-0 z-10",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("h1",{className:"text-xl font-semibold text-gray-900",children:"Interactive Exam"}),(0,a.jsx)(p,{current:i+1,total:y.length,answered:Object.keys(c).length})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[null!==d&&(0,a.jsx)(h,{timeRemaining:d,onTimeUp:()=>{N()},onTimeUpdate:m}),(0,a.jsx)("button",{onClick:()=>j(!0),className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium",children:"Submit Exam"})]})]})})}),(0,a.jsx)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,a.jsx)(x,{questions:y,currentQuestion:i,answers:c,onAnswerChange:(e,t)=>{o(s=>({...s,[e]:t}))},onQuestionChange:l,onSubmit:()=>j(!0)})}),f&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-md mx-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Submit Exam?"}),(0,a.jsxs)("p",{className:"text-gray-600 mb-6",children:["Are you sure you want to submit your exam? You have answered ",Object.keys(c).length," out of ",y.length," questions."]}),(0,a.jsxs)("div",{className:"flex space-x-4",children:[(0,a.jsx)("button",{onClick:()=>j(!1),className:"px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors",children:"Continue Exam"}),(0,a.jsx)("button",{onClick:N,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Submit Exam"})]})]})})]}):(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,a.jsx)("div",{className:"max-w-2xl mx-auto p-8",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-8 text-center",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Ready to Start Your Exam?"}),(0,a.jsxs)("div",{className:"bg-blue-50 rounded-lg p-6 mb-8",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-blue-900 mb-4",children:"Exam Details"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-blue-700 font-medium",children:"Questions:"}),(0,a.jsx)("p",{className:"text-blue-600",children:y.length})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-blue-700 font-medium",children:"Time Limit:"}),(0,a.jsx)("p",{className:"text-blue-600",children:t.hasTimeLimit?"".concat(t.timeLimit," minutes"):"No limit"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-blue-700 font-medium",children:"Difficulty:"}),(0,a.jsx)("p",{className:"text-blue-600 capitalize",children:t.difficulty})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-blue-700 font-medium",children:"Language:"}),(0,a.jsx)("p",{className:"text-blue-600 capitalize",children:t.language})]})]})]}),(0,a.jsxs)("div",{className:"space-y-4 mb-8 text-left",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900",children:"Instructions:"}),(0,a.jsxs)("ul",{className:"text-sm text-gray-600 space-y-2",children:[(0,a.jsx)("li",{children:"• Read each question carefully before answering"}),(0,a.jsx)("li",{children:"• You can navigate between questions using the Previous/Next buttons"}),(0,a.jsx)("li",{children:"• Your progress is automatically saved"}),(0,a.jsx)("li",{children:"• You can flag questions for review"}),t.hasTimeLimit&&(0,a.jsx)("li",{children:"• The exam will auto-submit when time runs out"})]})]}),(0,a.jsxs)("div",{className:"flex space-x-4 justify-center",children:[(0,a.jsxs)("button",{onClick:()=>{e.push("/analyze")},className:"px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,a.jsx)(b.A,{className:"w-4 h-4 inline mr-2"}),"Back to Configuration"]}),(0,a.jsx)("button",{onClick:()=>{g(!0)},className:"px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-semibold",children:"Start Exam"})]})]})})}):(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Loading exam..."})]})})}},4186:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},5695:(e,t,s)=>{"use strict";var a=s(8999);s.o(a,"usePathname")&&s.d(t,{usePathname:function(){return a.usePathname}}),s.o(a,"useRouter")&&s.d(t,{useRouter:function(){return a.useRouter}})},6209:(e,t,s)=>{Promise.resolve().then(s.bind(s,3789))},7550:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},9946:(e,t,s)=>{"use strict";s.d(t,{A:()=>x});var a=s(2115);let r=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),n=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,s)=>s?s.toUpperCase():t.toLowerCase()),i=e=>{let t=n(e);return t.charAt(0).toUpperCase()+t.slice(1)},l=function(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return t.filter((e,t,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===t).join(" ").trim()},c=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,a.forwardRef)((e,t)=>{let{color:s="currentColor",size:r=24,strokeWidth:n=2,absoluteStrokeWidth:i,className:d="",children:x,iconNode:m,...u}=e;return(0,a.createElement)("svg",{ref:t,...o,width:r,height:r,stroke:s,strokeWidth:i?24*Number(n)/Number(r):n,className:l("lucide",d),...!x&&!c(u)&&{"aria-hidden":"true"},...u},[...m.map(e=>{let[t,s]=e;return(0,a.createElement)(t,s)}),...Array.isArray(x)?x:[x]])}),x=(e,t)=>{let s=(0,a.forwardRef)((s,n)=>{let{className:c,...o}=s;return(0,a.createElement)(d,{ref:n,iconNode:t,className:l("lucide-".concat(r(i(e))),"lucide-".concat(e),c),...o})});return s.displayName=i(e),s}}},e=>{var t=t=>e(e.s=t);e.O(0,[441,684,358],()=>t(6209)),_N_E=e.O()}]);