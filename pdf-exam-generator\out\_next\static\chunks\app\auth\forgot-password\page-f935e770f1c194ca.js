(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[413],{646:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},844:(e,t,a)=>{"use strict";a.d(t,{AuthProvider:()=>p,A:()=>f});var r=a(5155),s=a(2115),l=a(6203),i=a(5317),n=a(3915),c=a(858);let o=0===(0,n.Dk)().length?(0,n.Wp)({apiKey:"AIzaSyCMX5UJ5HYwRvt7eLX2xG2WXUnd85FBvWw",authDomain:"ahmed-962e0.firebaseapp.com",projectId:"ahmed-962e0",storageBucket:"ahmed-962e0.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:1dc4c867a315fe228a2850",measurementId:"G-0S2RCZTPJX"}):(0,n.Dk)()[0],d=(0,l.xI)(o),u=(0,i.aU)(o);(0,c.c7)(o);let m=new l.HF,h=new l.sk;m.setCustomParameters({prompt:"select_account"}),h.setCustomParameters({display:"popup"});let x=(0,s.createContext)(void 0),f=()=>{let e=(0,s.useContext)(x);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e},p=e=>{let{children:t}=e,[a,n]=(0,s.useState)(null),[c,o]=(0,s.useState)(null),[f,p]=(0,s.useState)(!0),b=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!e)return;let a=(0,i.H9)(u,"users",e.uid),r=await (0,i.x7)(a);if(r.exists()){let e={...r.data(),lastLoginAt:new Date};await (0,i.BN)(a,e,{merge:!0}),o(e)}else{let{displayName:r,email:s,photoURL:l}=e,n=new Date,c={uid:e.uid,email:s||"",displayName:r||"",photoURL:l||void 0,role:t.role||"student",createdAt:n,lastLoginAt:n,preferences:{language:"ar",theme:"light",notifications:!0},stats:{totalExams:0,averageScore:0,totalQuestions:0,studyTime:0},...t};try{await (0,i.BN)(a,c),o(c)}catch(e){}}},w=async(e,t)=>{p(!0);try{let a=await (0,l.x9)(d,e,t);await b(a.user)}catch(e){throw e}finally{p(!1)}},y=async(e,t)=>{try{return await w(e,t),!0}catch(e){return!1}},g=async(e,t,a,r)=>{p(!0);try{let s=await (0,l.eJ)(d,e,t);await (0,l.r7)(s.user,{displayName:a}),await b(s.user,{role:r,displayName:a})}catch(e){throw e}finally{p(!1)}},v=async()=>{p(!0);try{let e=await (0,l.df)(d,m);await b(e.user)}catch(e){if("auth/popup-closed-by-user"===e.code)throw Error("تم إغلاق نافذة تسجيل الدخول");if("auth/popup-blocked"===e.code)throw Error("تم حظر النافذة المنبثقة. يرجى السماح بالنوافذ المنبثقة");if("auth/cancelled-popup-request"===e.code)throw Error("تم إلغاء طلب تسجيل الدخول");throw e}finally{p(!1)}},j=async()=>{p(!0);try{let e=await (0,l.df)(d,h);await b(e.user)}catch(e){if("auth/popup-closed-by-user"===e.code)throw Error("تم إغلاق نافذة تسجيل الدخول");if("auth/popup-blocked"===e.code)throw Error("تم حظر النافذة المنبثقة. يرجى السماح بالنوافذ المنبثقة");if("auth/cancelled-popup-request"===e.code)throw Error("تم إلغاء طلب تسجيل الدخول");throw e}finally{p(!1)}},N=async()=>{p(!0);try{await (0,l.CI)(d),n(null),o(null),localStorage.removeItem("examResults"),localStorage.removeItem("examAnswers"),localStorage.removeItem("uploadedPDF"),localStorage.removeItem("examConfig")}catch(e){throw e}finally{p(!1)}},A=async e=>{try{await (0,l.J1)(d,e)}catch(e){throw e}},k=async e=>{if(a)try{let t=(0,i.H9)(u,"users",a.uid);await (0,i.BN)(t,e,{merge:!0}),c&&o({...c,...e})}catch(e){throw e}};return(0,s.useEffect)(()=>(0,l.hg)(d,async e=>{e?(n(e),await b(e)):(n(null),o(null)),p(!1)}),[]),(0,r.jsx)(x.Provider,{value:{user:a,userProfile:c,loading:f,isAuthenticated:!!a,signIn:w,signUp:g,signInWithGoogle:v,signInWithFacebook:j,logout:N,resetPassword:A,updateUserProfile:k,login:y},children:t})}},5039:(e,t,a)=>{Promise.resolve().then(a.bind(a,6195))},6195:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>u});var r=a(5155),s=a(2115),l=a(6874),i=a.n(l),n=a(844),c=a(646),o=a(7550),d=a(8883);function u(){let[e,t]=(0,s.useState)(""),[a,l]=(0,s.useState)(""),[u,m]=(0,s.useState)(!1),[h,x]=(0,s.useState)(!1),{resetPassword:f}=(0,n.A)(),p=async t=>{t.preventDefault(),l(""),m(!0);try{await f(e),x(!0)}catch(e){l(b(e.code||"unknown-error"))}finally{m(!1)}},b=e=>{switch(e){case"auth/user-not-found":return"لا يوجد حساب مرتبط بهذا البريد الإلكتروني";case"auth/invalid-email":return"البريد الإلكتروني غير صالح";case"auth/too-many-requests":return"تم تجاوز عدد المحاولات المسموح. حاول مرة أخرى لاحقاً";default:return"حدث خطأ أثناء إرسال رابط إعادة التعيين. حاول مرة أخرى"}};return h?(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4",children:(0,r.jsx)("div",{className:"max-w-md w-full space-y-8",children:(0,r.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl p-8 text-center",children:[(0,r.jsx)("div",{className:"mx-auto h-16 w-16 bg-green-100 rounded-full flex items-center justify-center mb-6",children:(0,r.jsx)(c.A,{className:"h-8 w-8 text-green-600"})}),(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"تم إرسال الرابط!"}),(0,r.jsx)("p",{className:"text-gray-600 mb-6",children:"تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني:"}),(0,r.jsx)("p",{className:"text-blue-600 font-medium mb-6",children:e}),(0,r.jsx)("p",{className:"text-sm text-gray-500 mb-8",children:"تحقق من صندوق الوارد وصندوق الرسائل المزعجة. قد يستغرق وصول الرسالة بضع دقائق."}),(0,r.jsxs)(i(),{href:"/auth/login",className:"inline-flex items-center justify-center w-full py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors",children:[(0,r.jsx)(o.A,{className:"h-4 w-4 ml-2"}),"العودة إلى تسجيل الدخول"]})]})})}):(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4",children:(0,r.jsx)("div",{className:"max-w-md w-full space-y-8",children:(0,r.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl p-8",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsx)("div",{className:"mx-auto h-12 w-12 bg-blue-600 rounded-xl flex items-center justify-center mb-4",children:(0,r.jsx)(d.A,{className:"h-6 w-6 text-white"})}),(0,r.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-2",children:"نسيت كلمة المرور؟"}),(0,r.jsx)("p",{className:"text-gray-600",children:"أدخل بريدك الإلكتروني وسنرسل لك رابط إعادة التعيين"})]}),a&&(0,r.jsx)("div",{className:"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg",children:(0,r.jsx)("p",{className:"text-red-600 text-sm text-center",children:a})}),(0,r.jsxs)("form",{onSubmit:p,className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"البريد الإلكتروني"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:(0,r.jsx)(d.A,{className:"h-5 w-5 text-gray-400"})}),(0,r.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:e,onChange:e=>t(e.target.value),className:"block w-full pr-10 pl-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-right",placeholder:"أدخل بريدك الإلكتروني",dir:"rtl"})]})]}),(0,r.jsx)("button",{type:"submit",disabled:u,className:"w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:u?(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"}),"جاري الإرسال..."]}):"إرسال رابط إعادة التعيين"})]}),(0,r.jsx)("div",{className:"mt-6 text-center",children:(0,r.jsxs)(i(),{href:"/auth/login",className:"inline-flex items-center text-sm text-blue-600 hover:text-blue-500",children:[(0,r.jsx)(o.A,{className:"h-4 w-4 ml-1"}),"العودة إلى تسجيل الدخول"]})})]})})})}},7550:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},8883:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(9946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[992,811,100,470,874,441,684,358],()=>t(5039)),_N_E=e.O()}]);