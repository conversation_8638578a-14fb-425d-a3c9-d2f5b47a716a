(()=>{var a={};a.id=979,a.ids=[979],a.modules={3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9776:(a,i,e)=>{"use strict";e.d(i,{A:()=>n});var t=e(60687);function n({size:a="md",color:i="blue",text:e,fullScreen:n=!1}){let p=(0,t.jsxs)("div",{className:"flex flex-col items-center justify-center",children:[(0,t.jsx)("div",{className:`animate-spin rounded-full border-2 border-gray-200 ${{blue:"border-blue-600",green:"border-green-600",gray:"border-gray-600"}[i]} border-t-transparent ${{sm:"h-4 w-4",md:"h-8 w-8",lg:"h-12 w-12"}[a]}`}),e&&(0,t.jsx)("p",{className:"mt-2 text-sm text-gray-600",children:e})]});return n?(0,t.jsx)("div",{className:"fixed inset-0 bg-white bg-opacity-75 flex items-center justify-center z-50",children:p}):p}},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12866:(a,i,e)=>{"use strict";e.r(i),e.d(i,{default:()=>au});var t=e(60687),n=e(43210),p=e(16189),o=e(87955),l=e(4363);let c=new Map([["1km","application/vnd.1000minds.decision-model+xml"],["3dml","text/vnd.in3d.3dml"],["3ds","image/x-3ds"],["3g2","video/3gpp2"],["3gp","video/3gp"],["3gpp","video/3gpp"],["3mf","model/3mf"],["7z","application/x-7z-compressed"],["7zip","application/x-7z-compressed"],["123","application/vnd.lotus-1-2-3"],["aab","application/x-authorware-bin"],["aac","audio/x-acc"],["aam","application/x-authorware-map"],["aas","application/x-authorware-seg"],["abw","application/x-abiword"],["ac","application/vnd.nokia.n-gage.ac+xml"],["ac3","audio/ac3"],["acc","application/vnd.americandynamics.acc"],["ace","application/x-ace-compressed"],["acu","application/vnd.acucobol"],["acutc","application/vnd.acucorp"],["adp","audio/adpcm"],["aep","application/vnd.audiograph"],["afm","application/x-font-type1"],["afp","application/vnd.ibm.modcap"],["ahead","application/vnd.ahead.space"],["ai","application/pdf"],["aif","audio/x-aiff"],["aifc","audio/x-aiff"],["aiff","audio/x-aiff"],["air","application/vnd.adobe.air-application-installer-package+zip"],["ait","application/vnd.dvb.ait"],["ami","application/vnd.amiga.ami"],["amr","audio/amr"],["apk","application/vnd.android.package-archive"],["apng","image/apng"],["appcache","text/cache-manifest"],["application","application/x-ms-application"],["apr","application/vnd.lotus-approach"],["arc","application/x-freearc"],["arj","application/x-arj"],["asc","application/pgp-signature"],["asf","video/x-ms-asf"],["asm","text/x-asm"],["aso","application/vnd.accpac.simply.aso"],["asx","video/x-ms-asf"],["atc","application/vnd.acucorp"],["atom","application/atom+xml"],["atomcat","application/atomcat+xml"],["atomdeleted","application/atomdeleted+xml"],["atomsvc","application/atomsvc+xml"],["atx","application/vnd.antix.game-component"],["au","audio/x-au"],["avi","video/x-msvideo"],["avif","image/avif"],["aw","application/applixware"],["azf","application/vnd.airzip.filesecure.azf"],["azs","application/vnd.airzip.filesecure.azs"],["azv","image/vnd.airzip.accelerator.azv"],["azw","application/vnd.amazon.ebook"],["b16","image/vnd.pco.b16"],["bat","application/x-msdownload"],["bcpio","application/x-bcpio"],["bdf","application/x-font-bdf"],["bdm","application/vnd.syncml.dm+wbxml"],["bdoc","application/x-bdoc"],["bed","application/vnd.realvnc.bed"],["bh2","application/vnd.fujitsu.oasysprs"],["bin","application/octet-stream"],["blb","application/x-blorb"],["blorb","application/x-blorb"],["bmi","application/vnd.bmi"],["bmml","application/vnd.balsamiq.bmml+xml"],["bmp","image/bmp"],["book","application/vnd.framemaker"],["box","application/vnd.previewsystems.box"],["boz","application/x-bzip2"],["bpk","application/octet-stream"],["bpmn","application/octet-stream"],["bsp","model/vnd.valve.source.compiled-map"],["btif","image/prs.btif"],["buffer","application/octet-stream"],["bz","application/x-bzip"],["bz2","application/x-bzip2"],["c","text/x-c"],["c4d","application/vnd.clonk.c4group"],["c4f","application/vnd.clonk.c4group"],["c4g","application/vnd.clonk.c4group"],["c4p","application/vnd.clonk.c4group"],["c4u","application/vnd.clonk.c4group"],["c11amc","application/vnd.cluetrust.cartomobile-config"],["c11amz","application/vnd.cluetrust.cartomobile-config-pkg"],["cab","application/vnd.ms-cab-compressed"],["caf","audio/x-caf"],["cap","application/vnd.tcpdump.pcap"],["car","application/vnd.curl.car"],["cat","application/vnd.ms-pki.seccat"],["cb7","application/x-cbr"],["cba","application/x-cbr"],["cbr","application/x-cbr"],["cbt","application/x-cbr"],["cbz","application/x-cbr"],["cc","text/x-c"],["cco","application/x-cocoa"],["cct","application/x-director"],["ccxml","application/ccxml+xml"],["cdbcmsg","application/vnd.contact.cmsg"],["cda","application/x-cdf"],["cdf","application/x-netcdf"],["cdfx","application/cdfx+xml"],["cdkey","application/vnd.mediastation.cdkey"],["cdmia","application/cdmi-capability"],["cdmic","application/cdmi-container"],["cdmid","application/cdmi-domain"],["cdmio","application/cdmi-object"],["cdmiq","application/cdmi-queue"],["cdr","application/cdr"],["cdx","chemical/x-cdx"],["cdxml","application/vnd.chemdraw+xml"],["cdy","application/vnd.cinderella"],["cer","application/pkix-cert"],["cfs","application/x-cfs-compressed"],["cgm","image/cgm"],["chat","application/x-chat"],["chm","application/vnd.ms-htmlhelp"],["chrt","application/vnd.kde.kchart"],["cif","chemical/x-cif"],["cii","application/vnd.anser-web-certificate-issue-initiation"],["cil","application/vnd.ms-artgalry"],["cjs","application/node"],["cla","application/vnd.claymore"],["class","application/octet-stream"],["clkk","application/vnd.crick.clicker.keyboard"],["clkp","application/vnd.crick.clicker.palette"],["clkt","application/vnd.crick.clicker.template"],["clkw","application/vnd.crick.clicker.wordbank"],["clkx","application/vnd.crick.clicker"],["clp","application/x-msclip"],["cmc","application/vnd.cosmocaller"],["cmdf","chemical/x-cmdf"],["cml","chemical/x-cml"],["cmp","application/vnd.yellowriver-custom-menu"],["cmx","image/x-cmx"],["cod","application/vnd.rim.cod"],["coffee","text/coffeescript"],["com","application/x-msdownload"],["conf","text/plain"],["cpio","application/x-cpio"],["cpp","text/x-c"],["cpt","application/mac-compactpro"],["crd","application/x-mscardfile"],["crl","application/pkix-crl"],["crt","application/x-x509-ca-cert"],["crx","application/x-chrome-extension"],["cryptonote","application/vnd.rig.cryptonote"],["csh","application/x-csh"],["csl","application/vnd.citationstyles.style+xml"],["csml","chemical/x-csml"],["csp","application/vnd.commonspace"],["csr","application/octet-stream"],["css","text/css"],["cst","application/x-director"],["csv","text/csv"],["cu","application/cu-seeme"],["curl","text/vnd.curl"],["cww","application/prs.cww"],["cxt","application/x-director"],["cxx","text/x-c"],["dae","model/vnd.collada+xml"],["daf","application/vnd.mobius.daf"],["dart","application/vnd.dart"],["dataless","application/vnd.fdsn.seed"],["davmount","application/davmount+xml"],["dbf","application/vnd.dbf"],["dbk","application/docbook+xml"],["dcr","application/x-director"],["dcurl","text/vnd.curl.dcurl"],["dd2","application/vnd.oma.dd2+xml"],["ddd","application/vnd.fujixerox.ddd"],["ddf","application/vnd.syncml.dmddf+xml"],["dds","image/vnd.ms-dds"],["deb","application/x-debian-package"],["def","text/plain"],["deploy","application/octet-stream"],["der","application/x-x509-ca-cert"],["dfac","application/vnd.dreamfactory"],["dgc","application/x-dgc-compressed"],["dic","text/x-c"],["dir","application/x-director"],["dis","application/vnd.mobius.dis"],["disposition-notification","message/disposition-notification"],["dist","application/octet-stream"],["distz","application/octet-stream"],["djv","image/vnd.djvu"],["djvu","image/vnd.djvu"],["dll","application/octet-stream"],["dmg","application/x-apple-diskimage"],["dmn","application/octet-stream"],["dmp","application/vnd.tcpdump.pcap"],["dms","application/octet-stream"],["dna","application/vnd.dna"],["doc","application/msword"],["docm","application/vnd.ms-word.template.macroEnabled.12"],["docx","application/vnd.openxmlformats-officedocument.wordprocessingml.document"],["dot","application/msword"],["dotm","application/vnd.ms-word.template.macroEnabled.12"],["dotx","application/vnd.openxmlformats-officedocument.wordprocessingml.template"],["dp","application/vnd.osgi.dp"],["dpg","application/vnd.dpgraph"],["dra","audio/vnd.dra"],["drle","image/dicom-rle"],["dsc","text/prs.lines.tag"],["dssc","application/dssc+der"],["dtb","application/x-dtbook+xml"],["dtd","application/xml-dtd"],["dts","audio/vnd.dts"],["dtshd","audio/vnd.dts.hd"],["dump","application/octet-stream"],["dvb","video/vnd.dvb.file"],["dvi","application/x-dvi"],["dwd","application/atsc-dwd+xml"],["dwf","model/vnd.dwf"],["dwg","image/vnd.dwg"],["dxf","image/vnd.dxf"],["dxp","application/vnd.spotfire.dxp"],["dxr","application/x-director"],["ear","application/java-archive"],["ecelp4800","audio/vnd.nuera.ecelp4800"],["ecelp7470","audio/vnd.nuera.ecelp7470"],["ecelp9600","audio/vnd.nuera.ecelp9600"],["ecma","application/ecmascript"],["edm","application/vnd.novadigm.edm"],["edx","application/vnd.novadigm.edx"],["efif","application/vnd.picsel"],["ei6","application/vnd.pg.osasli"],["elc","application/octet-stream"],["emf","image/emf"],["eml","message/rfc822"],["emma","application/emma+xml"],["emotionml","application/emotionml+xml"],["emz","application/x-msmetafile"],["eol","audio/vnd.digital-winds"],["eot","application/vnd.ms-fontobject"],["eps","application/postscript"],["epub","application/epub+zip"],["es","application/ecmascript"],["es3","application/vnd.eszigno3+xml"],["esa","application/vnd.osgi.subsystem"],["esf","application/vnd.epson.esf"],["et3","application/vnd.eszigno3+xml"],["etx","text/x-setext"],["eva","application/x-eva"],["evy","application/x-envoy"],["exe","application/octet-stream"],["exi","application/exi"],["exp","application/express"],["exr","image/aces"],["ext","application/vnd.novadigm.ext"],["ez","application/andrew-inset"],["ez2","application/vnd.ezpix-album"],["ez3","application/vnd.ezpix-package"],["f","text/x-fortran"],["f4v","video/mp4"],["f77","text/x-fortran"],["f90","text/x-fortran"],["fbs","image/vnd.fastbidsheet"],["fcdt","application/vnd.adobe.formscentral.fcdt"],["fcs","application/vnd.isac.fcs"],["fdf","application/vnd.fdf"],["fdt","application/fdt+xml"],["fe_launch","application/vnd.denovo.fcselayout-link"],["fg5","application/vnd.fujitsu.oasysgp"],["fgd","application/x-director"],["fh","image/x-freehand"],["fh4","image/x-freehand"],["fh5","image/x-freehand"],["fh7","image/x-freehand"],["fhc","image/x-freehand"],["fig","application/x-xfig"],["fits","image/fits"],["flac","audio/x-flac"],["fli","video/x-fli"],["flo","application/vnd.micrografx.flo"],["flv","video/x-flv"],["flw","application/vnd.kde.kivio"],["flx","text/vnd.fmi.flexstor"],["fly","text/vnd.fly"],["fm","application/vnd.framemaker"],["fnc","application/vnd.frogans.fnc"],["fo","application/vnd.software602.filler.form+xml"],["for","text/x-fortran"],["fpx","image/vnd.fpx"],["frame","application/vnd.framemaker"],["fsc","application/vnd.fsc.weblaunch"],["fst","image/vnd.fst"],["ftc","application/vnd.fluxtime.clip"],["fti","application/vnd.anser-web-funds-transfer-initiation"],["fvt","video/vnd.fvt"],["fxp","application/vnd.adobe.fxp"],["fxpl","application/vnd.adobe.fxp"],["fzs","application/vnd.fuzzysheet"],["g2w","application/vnd.geoplan"],["g3","image/g3fax"],["g3w","application/vnd.geospace"],["gac","application/vnd.groove-account"],["gam","application/x-tads"],["gbr","application/rpki-ghostbusters"],["gca","application/x-gca-compressed"],["gdl","model/vnd.gdl"],["gdoc","application/vnd.google-apps.document"],["geo","application/vnd.dynageo"],["geojson","application/geo+json"],["gex","application/vnd.geometry-explorer"],["ggb","application/vnd.geogebra.file"],["ggt","application/vnd.geogebra.tool"],["ghf","application/vnd.groove-help"],["gif","image/gif"],["gim","application/vnd.groove-identity-message"],["glb","model/gltf-binary"],["gltf","model/gltf+json"],["gml","application/gml+xml"],["gmx","application/vnd.gmx"],["gnumeric","application/x-gnumeric"],["gpg","application/gpg-keys"],["gph","application/vnd.flographit"],["gpx","application/gpx+xml"],["gqf","application/vnd.grafeq"],["gqs","application/vnd.grafeq"],["gram","application/srgs"],["gramps","application/x-gramps-xml"],["gre","application/vnd.geometry-explorer"],["grv","application/vnd.groove-injector"],["grxml","application/srgs+xml"],["gsf","application/x-font-ghostscript"],["gsheet","application/vnd.google-apps.spreadsheet"],["gslides","application/vnd.google-apps.presentation"],["gtar","application/x-gtar"],["gtm","application/vnd.groove-tool-message"],["gtw","model/vnd.gtw"],["gv","text/vnd.graphviz"],["gxf","application/gxf"],["gxt","application/vnd.geonext"],["gz","application/gzip"],["gzip","application/gzip"],["h","text/x-c"],["h261","video/h261"],["h263","video/h263"],["h264","video/h264"],["hal","application/vnd.hal+xml"],["hbci","application/vnd.hbci"],["hbs","text/x-handlebars-template"],["hdd","application/x-virtualbox-hdd"],["hdf","application/x-hdf"],["heic","image/heic"],["heics","image/heic-sequence"],["heif","image/heif"],["heifs","image/heif-sequence"],["hej2","image/hej2k"],["held","application/atsc-held+xml"],["hh","text/x-c"],["hjson","application/hjson"],["hlp","application/winhlp"],["hpgl","application/vnd.hp-hpgl"],["hpid","application/vnd.hp-hpid"],["hps","application/vnd.hp-hps"],["hqx","application/mac-binhex40"],["hsj2","image/hsj2"],["htc","text/x-component"],["htke","application/vnd.kenameaapp"],["htm","text/html"],["html","text/html"],["hvd","application/vnd.yamaha.hv-dic"],["hvp","application/vnd.yamaha.hv-voice"],["hvs","application/vnd.yamaha.hv-script"],["i2g","application/vnd.intergeo"],["icc","application/vnd.iccprofile"],["ice","x-conference/x-cooltalk"],["icm","application/vnd.iccprofile"],["ico","image/x-icon"],["ics","text/calendar"],["ief","image/ief"],["ifb","text/calendar"],["ifm","application/vnd.shana.informed.formdata"],["iges","model/iges"],["igl","application/vnd.igloader"],["igm","application/vnd.insors.igm"],["igs","model/iges"],["igx","application/vnd.micrografx.igx"],["iif","application/vnd.shana.informed.interchange"],["img","application/octet-stream"],["imp","application/vnd.accpac.simply.imp"],["ims","application/vnd.ms-ims"],["in","text/plain"],["ini","text/plain"],["ink","application/inkml+xml"],["inkml","application/inkml+xml"],["install","application/x-install-instructions"],["iota","application/vnd.astraea-software.iota"],["ipfix","application/ipfix"],["ipk","application/vnd.shana.informed.package"],["irm","application/vnd.ibm.rights-management"],["irp","application/vnd.irepository.package+xml"],["iso","application/x-iso9660-image"],["itp","application/vnd.shana.informed.formtemplate"],["its","application/its+xml"],["ivp","application/vnd.immervision-ivp"],["ivu","application/vnd.immervision-ivu"],["jad","text/vnd.sun.j2me.app-descriptor"],["jade","text/jade"],["jam","application/vnd.jam"],["jar","application/java-archive"],["jardiff","application/x-java-archive-diff"],["java","text/x-java-source"],["jhc","image/jphc"],["jisp","application/vnd.jisp"],["jls","image/jls"],["jlt","application/vnd.hp-jlyt"],["jng","image/x-jng"],["jnlp","application/x-java-jnlp-file"],["joda","application/vnd.joost.joda-archive"],["jp2","image/jp2"],["jpe","image/jpeg"],["jpeg","image/jpeg"],["jpf","image/jpx"],["jpg","image/jpeg"],["jpg2","image/jp2"],["jpgm","video/jpm"],["jpgv","video/jpeg"],["jph","image/jph"],["jpm","video/jpm"],["jpx","image/jpx"],["js","application/javascript"],["json","application/json"],["json5","application/json5"],["jsonld","application/ld+json"],["jsonl","application/jsonl"],["jsonml","application/jsonml+json"],["jsx","text/jsx"],["jxr","image/jxr"],["jxra","image/jxra"],["jxrs","image/jxrs"],["jxs","image/jxs"],["jxsc","image/jxsc"],["jxsi","image/jxsi"],["jxss","image/jxss"],["kar","audio/midi"],["karbon","application/vnd.kde.karbon"],["kdb","application/octet-stream"],["kdbx","application/x-keepass2"],["key","application/x-iwork-keynote-sffkey"],["kfo","application/vnd.kde.kformula"],["kia","application/vnd.kidspiration"],["kml","application/vnd.google-earth.kml+xml"],["kmz","application/vnd.google-earth.kmz"],["kne","application/vnd.kinar"],["knp","application/vnd.kinar"],["kon","application/vnd.kde.kontour"],["kpr","application/vnd.kde.kpresenter"],["kpt","application/vnd.kde.kpresenter"],["kpxx","application/vnd.ds-keypoint"],["ksp","application/vnd.kde.kspread"],["ktr","application/vnd.kahootz"],["ktx","image/ktx"],["ktx2","image/ktx2"],["ktz","application/vnd.kahootz"],["kwd","application/vnd.kde.kword"],["kwt","application/vnd.kde.kword"],["lasxml","application/vnd.las.las+xml"],["latex","application/x-latex"],["lbd","application/vnd.llamagraphics.life-balance.desktop"],["lbe","application/vnd.llamagraphics.life-balance.exchange+xml"],["les","application/vnd.hhe.lesson-player"],["less","text/less"],["lgr","application/lgr+xml"],["lha","application/octet-stream"],["link66","application/vnd.route66.link66+xml"],["list","text/plain"],["list3820","application/vnd.ibm.modcap"],["listafp","application/vnd.ibm.modcap"],["litcoffee","text/coffeescript"],["lnk","application/x-ms-shortcut"],["log","text/plain"],["lostxml","application/lost+xml"],["lrf","application/octet-stream"],["lrm","application/vnd.ms-lrm"],["ltf","application/vnd.frogans.ltf"],["lua","text/x-lua"],["luac","application/x-lua-bytecode"],["lvp","audio/vnd.lucent.voice"],["lwp","application/vnd.lotus-wordpro"],["lzh","application/octet-stream"],["m1v","video/mpeg"],["m2a","audio/mpeg"],["m2v","video/mpeg"],["m3a","audio/mpeg"],["m3u","text/plain"],["m3u8","application/vnd.apple.mpegurl"],["m4a","audio/x-m4a"],["m4p","application/mp4"],["m4s","video/iso.segment"],["m4u","application/vnd.mpegurl"],["m4v","video/x-m4v"],["m13","application/x-msmediaview"],["m14","application/x-msmediaview"],["m21","application/mp21"],["ma","application/mathematica"],["mads","application/mads+xml"],["maei","application/mmt-aei+xml"],["mag","application/vnd.ecowin.chart"],["maker","application/vnd.framemaker"],["man","text/troff"],["manifest","text/cache-manifest"],["map","application/json"],["mar","application/octet-stream"],["markdown","text/markdown"],["mathml","application/mathml+xml"],["mb","application/mathematica"],["mbk","application/vnd.mobius.mbk"],["mbox","application/mbox"],["mc1","application/vnd.medcalcdata"],["mcd","application/vnd.mcd"],["mcurl","text/vnd.curl.mcurl"],["md","text/markdown"],["mdb","application/x-msaccess"],["mdi","image/vnd.ms-modi"],["mdx","text/mdx"],["me","text/troff"],["mesh","model/mesh"],["meta4","application/metalink4+xml"],["metalink","application/metalink+xml"],["mets","application/mets+xml"],["mfm","application/vnd.mfmp"],["mft","application/rpki-manifest"],["mgp","application/vnd.osgeo.mapguide.package"],["mgz","application/vnd.proteus.magazine"],["mid","audio/midi"],["midi","audio/midi"],["mie","application/x-mie"],["mif","application/vnd.mif"],["mime","message/rfc822"],["mj2","video/mj2"],["mjp2","video/mj2"],["mjs","application/javascript"],["mk3d","video/x-matroska"],["mka","audio/x-matroska"],["mkd","text/x-markdown"],["mks","video/x-matroska"],["mkv","video/x-matroska"],["mlp","application/vnd.dolby.mlp"],["mmd","application/vnd.chipnuts.karaoke-mmd"],["mmf","application/vnd.smaf"],["mml","text/mathml"],["mmr","image/vnd.fujixerox.edmics-mmr"],["mng","video/x-mng"],["mny","application/x-msmoney"],["mobi","application/x-mobipocket-ebook"],["mods","application/mods+xml"],["mov","video/quicktime"],["movie","video/x-sgi-movie"],["mp2","audio/mpeg"],["mp2a","audio/mpeg"],["mp3","audio/mpeg"],["mp4","video/mp4"],["mp4a","audio/mp4"],["mp4s","application/mp4"],["mp4v","video/mp4"],["mp21","application/mp21"],["mpc","application/vnd.mophun.certificate"],["mpd","application/dash+xml"],["mpe","video/mpeg"],["mpeg","video/mpeg"],["mpg","video/mpeg"],["mpg4","video/mp4"],["mpga","audio/mpeg"],["mpkg","application/vnd.apple.installer+xml"],["mpm","application/vnd.blueice.multipass"],["mpn","application/vnd.mophun.application"],["mpp","application/vnd.ms-project"],["mpt","application/vnd.ms-project"],["mpy","application/vnd.ibm.minipay"],["mqy","application/vnd.mobius.mqy"],["mrc","application/marc"],["mrcx","application/marcxml+xml"],["ms","text/troff"],["mscml","application/mediaservercontrol+xml"],["mseed","application/vnd.fdsn.mseed"],["mseq","application/vnd.mseq"],["msf","application/vnd.epson.msf"],["msg","application/vnd.ms-outlook"],["msh","model/mesh"],["msi","application/x-msdownload"],["msl","application/vnd.mobius.msl"],["msm","application/octet-stream"],["msp","application/octet-stream"],["msty","application/vnd.muvee.style"],["mtl","model/mtl"],["mts","model/vnd.mts"],["mus","application/vnd.musician"],["musd","application/mmt-usd+xml"],["musicxml","application/vnd.recordare.musicxml+xml"],["mvb","application/x-msmediaview"],["mvt","application/vnd.mapbox-vector-tile"],["mwf","application/vnd.mfer"],["mxf","application/mxf"],["mxl","application/vnd.recordare.musicxml"],["mxmf","audio/mobile-xmf"],["mxml","application/xv+xml"],["mxs","application/vnd.triscape.mxs"],["mxu","video/vnd.mpegurl"],["n-gage","application/vnd.nokia.n-gage.symbian.install"],["n3","text/n3"],["nb","application/mathematica"],["nbp","application/vnd.wolfram.player"],["nc","application/x-netcdf"],["ncx","application/x-dtbncx+xml"],["nfo","text/x-nfo"],["ngdat","application/vnd.nokia.n-gage.data"],["nitf","application/vnd.nitf"],["nlu","application/vnd.neurolanguage.nlu"],["nml","application/vnd.enliven"],["nnd","application/vnd.noblenet-directory"],["nns","application/vnd.noblenet-sealer"],["nnw","application/vnd.noblenet-web"],["npx","image/vnd.net-fpx"],["nq","application/n-quads"],["nsc","application/x-conference"],["nsf","application/vnd.lotus-notes"],["nt","application/n-triples"],["ntf","application/vnd.nitf"],["numbers","application/x-iwork-numbers-sffnumbers"],["nzb","application/x-nzb"],["oa2","application/vnd.fujitsu.oasys2"],["oa3","application/vnd.fujitsu.oasys3"],["oas","application/vnd.fujitsu.oasys"],["obd","application/x-msbinder"],["obgx","application/vnd.openblox.game+xml"],["obj","model/obj"],["oda","application/oda"],["odb","application/vnd.oasis.opendocument.database"],["odc","application/vnd.oasis.opendocument.chart"],["odf","application/vnd.oasis.opendocument.formula"],["odft","application/vnd.oasis.opendocument.formula-template"],["odg","application/vnd.oasis.opendocument.graphics"],["odi","application/vnd.oasis.opendocument.image"],["odm","application/vnd.oasis.opendocument.text-master"],["odp","application/vnd.oasis.opendocument.presentation"],["ods","application/vnd.oasis.opendocument.spreadsheet"],["odt","application/vnd.oasis.opendocument.text"],["oga","audio/ogg"],["ogex","model/vnd.opengex"],["ogg","audio/ogg"],["ogv","video/ogg"],["ogx","application/ogg"],["omdoc","application/omdoc+xml"],["onepkg","application/onenote"],["onetmp","application/onenote"],["onetoc","application/onenote"],["onetoc2","application/onenote"],["opf","application/oebps-package+xml"],["opml","text/x-opml"],["oprc","application/vnd.palm"],["opus","audio/ogg"],["org","text/x-org"],["osf","application/vnd.yamaha.openscoreformat"],["osfpvg","application/vnd.yamaha.openscoreformat.osfpvg+xml"],["osm","application/vnd.openstreetmap.data+xml"],["otc","application/vnd.oasis.opendocument.chart-template"],["otf","font/otf"],["otg","application/vnd.oasis.opendocument.graphics-template"],["oth","application/vnd.oasis.opendocument.text-web"],["oti","application/vnd.oasis.opendocument.image-template"],["otp","application/vnd.oasis.opendocument.presentation-template"],["ots","application/vnd.oasis.opendocument.spreadsheet-template"],["ott","application/vnd.oasis.opendocument.text-template"],["ova","application/x-virtualbox-ova"],["ovf","application/x-virtualbox-ovf"],["owl","application/rdf+xml"],["oxps","application/oxps"],["oxt","application/vnd.openofficeorg.extension"],["p","text/x-pascal"],["p7a","application/x-pkcs7-signature"],["p7b","application/x-pkcs7-certificates"],["p7c","application/pkcs7-mime"],["p7m","application/pkcs7-mime"],["p7r","application/x-pkcs7-certreqresp"],["p7s","application/pkcs7-signature"],["p8","application/pkcs8"],["p10","application/x-pkcs10"],["p12","application/x-pkcs12"],["pac","application/x-ns-proxy-autoconfig"],["pages","application/x-iwork-pages-sffpages"],["pas","text/x-pascal"],["paw","application/vnd.pawaafile"],["pbd","application/vnd.powerbuilder6"],["pbm","image/x-portable-bitmap"],["pcap","application/vnd.tcpdump.pcap"],["pcf","application/x-font-pcf"],["pcl","application/vnd.hp-pcl"],["pclxl","application/vnd.hp-pclxl"],["pct","image/x-pict"],["pcurl","application/vnd.curl.pcurl"],["pcx","image/x-pcx"],["pdb","application/x-pilot"],["pde","text/x-processing"],["pdf","application/pdf"],["pem","application/x-x509-user-cert"],["pfa","application/x-font-type1"],["pfb","application/x-font-type1"],["pfm","application/x-font-type1"],["pfr","application/font-tdpfr"],["pfx","application/x-pkcs12"],["pgm","image/x-portable-graymap"],["pgn","application/x-chess-pgn"],["pgp","application/pgp"],["php","application/x-httpd-php"],["php3","application/x-httpd-php"],["php4","application/x-httpd-php"],["phps","application/x-httpd-php-source"],["phtml","application/x-httpd-php"],["pic","image/x-pict"],["pkg","application/octet-stream"],["pki","application/pkixcmp"],["pkipath","application/pkix-pkipath"],["pkpass","application/vnd.apple.pkpass"],["pl","application/x-perl"],["plb","application/vnd.3gpp.pic-bw-large"],["plc","application/vnd.mobius.plc"],["plf","application/vnd.pocketlearn"],["pls","application/pls+xml"],["pm","application/x-perl"],["pml","application/vnd.ctc-posml"],["png","image/png"],["pnm","image/x-portable-anymap"],["portpkg","application/vnd.macports.portpkg"],["pot","application/vnd.ms-powerpoint"],["potm","application/vnd.ms-powerpoint.presentation.macroEnabled.12"],["potx","application/vnd.openxmlformats-officedocument.presentationml.template"],["ppa","application/vnd.ms-powerpoint"],["ppam","application/vnd.ms-powerpoint.addin.macroEnabled.12"],["ppd","application/vnd.cups-ppd"],["ppm","image/x-portable-pixmap"],["pps","application/vnd.ms-powerpoint"],["ppsm","application/vnd.ms-powerpoint.slideshow.macroEnabled.12"],["ppsx","application/vnd.openxmlformats-officedocument.presentationml.slideshow"],["ppt","application/powerpoint"],["pptm","application/vnd.ms-powerpoint.presentation.macroEnabled.12"],["pptx","application/vnd.openxmlformats-officedocument.presentationml.presentation"],["pqa","application/vnd.palm"],["prc","application/x-pilot"],["pre","application/vnd.lotus-freelance"],["prf","application/pics-rules"],["provx","application/provenance+xml"],["ps","application/postscript"],["psb","application/vnd.3gpp.pic-bw-small"],["psd","application/x-photoshop"],["psf","application/x-font-linux-psf"],["pskcxml","application/pskc+xml"],["pti","image/prs.pti"],["ptid","application/vnd.pvi.ptid1"],["pub","application/x-mspublisher"],["pvb","application/vnd.3gpp.pic-bw-var"],["pwn","application/vnd.3m.post-it-notes"],["pya","audio/vnd.ms-playready.media.pya"],["pyv","video/vnd.ms-playready.media.pyv"],["qam","application/vnd.epson.quickanime"],["qbo","application/vnd.intu.qbo"],["qfx","application/vnd.intu.qfx"],["qps","application/vnd.publishare-delta-tree"],["qt","video/quicktime"],["qwd","application/vnd.quark.quarkxpress"],["qwt","application/vnd.quark.quarkxpress"],["qxb","application/vnd.quark.quarkxpress"],["qxd","application/vnd.quark.quarkxpress"],["qxl","application/vnd.quark.quarkxpress"],["qxt","application/vnd.quark.quarkxpress"],["ra","audio/x-realaudio"],["ram","audio/x-pn-realaudio"],["raml","application/raml+yaml"],["rapd","application/route-apd+xml"],["rar","application/x-rar"],["ras","image/x-cmu-raster"],["rcprofile","application/vnd.ipunplugged.rcprofile"],["rdf","application/rdf+xml"],["rdz","application/vnd.data-vision.rdz"],["relo","application/p2p-overlay+xml"],["rep","application/vnd.businessobjects"],["res","application/x-dtbresource+xml"],["rgb","image/x-rgb"],["rif","application/reginfo+xml"],["rip","audio/vnd.rip"],["ris","application/x-research-info-systems"],["rl","application/resource-lists+xml"],["rlc","image/vnd.fujixerox.edmics-rlc"],["rld","application/resource-lists-diff+xml"],["rm","audio/x-pn-realaudio"],["rmi","audio/midi"],["rmp","audio/x-pn-realaudio-plugin"],["rms","application/vnd.jcp.javame.midlet-rms"],["rmvb","application/vnd.rn-realmedia-vbr"],["rnc","application/relax-ng-compact-syntax"],["rng","application/xml"],["roa","application/rpki-roa"],["roff","text/troff"],["rp9","application/vnd.cloanto.rp9"],["rpm","audio/x-pn-realaudio-plugin"],["rpss","application/vnd.nokia.radio-presets"],["rpst","application/vnd.nokia.radio-preset"],["rq","application/sparql-query"],["rs","application/rls-services+xml"],["rsa","application/x-pkcs7"],["rsat","application/atsc-rsat+xml"],["rsd","application/rsd+xml"],["rsheet","application/urc-ressheet+xml"],["rss","application/rss+xml"],["rtf","text/rtf"],["rtx","text/richtext"],["run","application/x-makeself"],["rusd","application/route-usd+xml"],["rv","video/vnd.rn-realvideo"],["s","text/x-asm"],["s3m","audio/s3m"],["saf","application/vnd.yamaha.smaf-audio"],["sass","text/x-sass"],["sbml","application/sbml+xml"],["sc","application/vnd.ibm.secure-container"],["scd","application/x-msschedule"],["scm","application/vnd.lotus-screencam"],["scq","application/scvp-cv-request"],["scs","application/scvp-cv-response"],["scss","text/x-scss"],["scurl","text/vnd.curl.scurl"],["sda","application/vnd.stardivision.draw"],["sdc","application/vnd.stardivision.calc"],["sdd","application/vnd.stardivision.impress"],["sdkd","application/vnd.solent.sdkm+xml"],["sdkm","application/vnd.solent.sdkm+xml"],["sdp","application/sdp"],["sdw","application/vnd.stardivision.writer"],["sea","application/octet-stream"],["see","application/vnd.seemail"],["seed","application/vnd.fdsn.seed"],["sema","application/vnd.sema"],["semd","application/vnd.semd"],["semf","application/vnd.semf"],["senmlx","application/senml+xml"],["sensmlx","application/sensml+xml"],["ser","application/java-serialized-object"],["setpay","application/set-payment-initiation"],["setreg","application/set-registration-initiation"],["sfd-hdstx","application/vnd.hydrostatix.sof-data"],["sfs","application/vnd.spotfire.sfs"],["sfv","text/x-sfv"],["sgi","image/sgi"],["sgl","application/vnd.stardivision.writer-global"],["sgm","text/sgml"],["sgml","text/sgml"],["sh","application/x-sh"],["shar","application/x-shar"],["shex","text/shex"],["shf","application/shf+xml"],["shtml","text/html"],["sid","image/x-mrsid-image"],["sieve","application/sieve"],["sig","application/pgp-signature"],["sil","audio/silk"],["silo","model/mesh"],["sis","application/vnd.symbian.install"],["sisx","application/vnd.symbian.install"],["sit","application/x-stuffit"],["sitx","application/x-stuffitx"],["siv","application/sieve"],["skd","application/vnd.koan"],["skm","application/vnd.koan"],["skp","application/vnd.koan"],["skt","application/vnd.koan"],["sldm","application/vnd.ms-powerpoint.slide.macroenabled.12"],["sldx","application/vnd.openxmlformats-officedocument.presentationml.slide"],["slim","text/slim"],["slm","text/slim"],["sls","application/route-s-tsid+xml"],["slt","application/vnd.epson.salt"],["sm","application/vnd.stepmania.stepchart"],["smf","application/vnd.stardivision.math"],["smi","application/smil"],["smil","application/smil"],["smv","video/x-smv"],["smzip","application/vnd.stepmania.package"],["snd","audio/basic"],["snf","application/x-font-snf"],["so","application/octet-stream"],["spc","application/x-pkcs7-certificates"],["spdx","text/spdx"],["spf","application/vnd.yamaha.smaf-phrase"],["spl","application/x-futuresplash"],["spot","text/vnd.in3d.spot"],["spp","application/scvp-vp-response"],["spq","application/scvp-vp-request"],["spx","audio/ogg"],["sql","application/x-sql"],["src","application/x-wais-source"],["srt","application/x-subrip"],["sru","application/sru+xml"],["srx","application/sparql-results+xml"],["ssdl","application/ssdl+xml"],["sse","application/vnd.kodak-descriptor"],["ssf","application/vnd.epson.ssf"],["ssml","application/ssml+xml"],["sst","application/octet-stream"],["st","application/vnd.sailingtracker.track"],["stc","application/vnd.sun.xml.calc.template"],["std","application/vnd.sun.xml.draw.template"],["stf","application/vnd.wt.stf"],["sti","application/vnd.sun.xml.impress.template"],["stk","application/hyperstudio"],["stl","model/stl"],["stpx","model/step+xml"],["stpxz","model/step-xml+zip"],["stpz","model/step+zip"],["str","application/vnd.pg.format"],["stw","application/vnd.sun.xml.writer.template"],["styl","text/stylus"],["stylus","text/stylus"],["sub","text/vnd.dvb.subtitle"],["sus","application/vnd.sus-calendar"],["susp","application/vnd.sus-calendar"],["sv4cpio","application/x-sv4cpio"],["sv4crc","application/x-sv4crc"],["svc","application/vnd.dvb.service"],["svd","application/vnd.svd"],["svg","image/svg+xml"],["svgz","image/svg+xml"],["swa","application/x-director"],["swf","application/x-shockwave-flash"],["swi","application/vnd.aristanetworks.swi"],["swidtag","application/swid+xml"],["sxc","application/vnd.sun.xml.calc"],["sxd","application/vnd.sun.xml.draw"],["sxg","application/vnd.sun.xml.writer.global"],["sxi","application/vnd.sun.xml.impress"],["sxm","application/vnd.sun.xml.math"],["sxw","application/vnd.sun.xml.writer"],["t","text/troff"],["t3","application/x-t3vm-image"],["t38","image/t38"],["taglet","application/vnd.mynfc"],["tao","application/vnd.tao.intent-module-archive"],["tap","image/vnd.tencent.tap"],["tar","application/x-tar"],["tcap","application/vnd.3gpp2.tcap"],["tcl","application/x-tcl"],["td","application/urc-targetdesc+xml"],["teacher","application/vnd.smart.teacher"],["tei","application/tei+xml"],["teicorpus","application/tei+xml"],["tex","application/x-tex"],["texi","application/x-texinfo"],["texinfo","application/x-texinfo"],["text","text/plain"],["tfi","application/thraud+xml"],["tfm","application/x-tex-tfm"],["tfx","image/tiff-fx"],["tga","image/x-tga"],["tgz","application/x-tar"],["thmx","application/vnd.ms-officetheme"],["tif","image/tiff"],["tiff","image/tiff"],["tk","application/x-tcl"],["tmo","application/vnd.tmobile-livetv"],["toml","application/toml"],["torrent","application/x-bittorrent"],["tpl","application/vnd.groove-tool-template"],["tpt","application/vnd.trid.tpt"],["tr","text/troff"],["tra","application/vnd.trueapp"],["trig","application/trig"],["trm","application/x-msterminal"],["ts","video/mp2t"],["tsd","application/timestamped-data"],["tsv","text/tab-separated-values"],["ttc","font/collection"],["ttf","font/ttf"],["ttl","text/turtle"],["ttml","application/ttml+xml"],["twd","application/vnd.simtech-mindmapper"],["twds","application/vnd.simtech-mindmapper"],["txd","application/vnd.genomatix.tuxedo"],["txf","application/vnd.mobius.txf"],["txt","text/plain"],["u8dsn","message/global-delivery-status"],["u8hdr","message/global-headers"],["u8mdn","message/global-disposition-notification"],["u8msg","message/global"],["u32","application/x-authorware-bin"],["ubj","application/ubjson"],["udeb","application/x-debian-package"],["ufd","application/vnd.ufdl"],["ufdl","application/vnd.ufdl"],["ulx","application/x-glulx"],["umj","application/vnd.umajin"],["unityweb","application/vnd.unity"],["uoml","application/vnd.uoml+xml"],["uri","text/uri-list"],["uris","text/uri-list"],["urls","text/uri-list"],["usdz","model/vnd.usdz+zip"],["ustar","application/x-ustar"],["utz","application/vnd.uiq.theme"],["uu","text/x-uuencode"],["uva","audio/vnd.dece.audio"],["uvd","application/vnd.dece.data"],["uvf","application/vnd.dece.data"],["uvg","image/vnd.dece.graphic"],["uvh","video/vnd.dece.hd"],["uvi","image/vnd.dece.graphic"],["uvm","video/vnd.dece.mobile"],["uvp","video/vnd.dece.pd"],["uvs","video/vnd.dece.sd"],["uvt","application/vnd.dece.ttml+xml"],["uvu","video/vnd.uvvu.mp4"],["uvv","video/vnd.dece.video"],["uvva","audio/vnd.dece.audio"],["uvvd","application/vnd.dece.data"],["uvvf","application/vnd.dece.data"],["uvvg","image/vnd.dece.graphic"],["uvvh","video/vnd.dece.hd"],["uvvi","image/vnd.dece.graphic"],["uvvm","video/vnd.dece.mobile"],["uvvp","video/vnd.dece.pd"],["uvvs","video/vnd.dece.sd"],["uvvt","application/vnd.dece.ttml+xml"],["uvvu","video/vnd.uvvu.mp4"],["uvvv","video/vnd.dece.video"],["uvvx","application/vnd.dece.unspecified"],["uvvz","application/vnd.dece.zip"],["uvx","application/vnd.dece.unspecified"],["uvz","application/vnd.dece.zip"],["vbox","application/x-virtualbox-vbox"],["vbox-extpack","application/x-virtualbox-vbox-extpack"],["vcard","text/vcard"],["vcd","application/x-cdlink"],["vcf","text/x-vcard"],["vcg","application/vnd.groove-vcard"],["vcs","text/x-vcalendar"],["vcx","application/vnd.vcx"],["vdi","application/x-virtualbox-vdi"],["vds","model/vnd.sap.vds"],["vhd","application/x-virtualbox-vhd"],["vis","application/vnd.visionary"],["viv","video/vnd.vivo"],["vlc","application/videolan"],["vmdk","application/x-virtualbox-vmdk"],["vob","video/x-ms-vob"],["vor","application/vnd.stardivision.writer"],["vox","application/x-authorware-bin"],["vrml","model/vrml"],["vsd","application/vnd.visio"],["vsf","application/vnd.vsf"],["vss","application/vnd.visio"],["vst","application/vnd.visio"],["vsw","application/vnd.visio"],["vtf","image/vnd.valve.source.texture"],["vtt","text/vtt"],["vtu","model/vnd.vtu"],["vxml","application/voicexml+xml"],["w3d","application/x-director"],["wad","application/x-doom"],["wadl","application/vnd.sun.wadl+xml"],["war","application/java-archive"],["wasm","application/wasm"],["wav","audio/x-wav"],["wax","audio/x-ms-wax"],["wbmp","image/vnd.wap.wbmp"],["wbs","application/vnd.criticaltools.wbs+xml"],["wbxml","application/wbxml"],["wcm","application/vnd.ms-works"],["wdb","application/vnd.ms-works"],["wdp","image/vnd.ms-photo"],["weba","audio/webm"],["webapp","application/x-web-app-manifest+json"],["webm","video/webm"],["webmanifest","application/manifest+json"],["webp","image/webp"],["wg","application/vnd.pmi.widget"],["wgt","application/widget"],["wks","application/vnd.ms-works"],["wm","video/x-ms-wm"],["wma","audio/x-ms-wma"],["wmd","application/x-ms-wmd"],["wmf","image/wmf"],["wml","text/vnd.wap.wml"],["wmlc","application/wmlc"],["wmls","text/vnd.wap.wmlscript"],["wmlsc","application/vnd.wap.wmlscriptc"],["wmv","video/x-ms-wmv"],["wmx","video/x-ms-wmx"],["wmz","application/x-msmetafile"],["woff","font/woff"],["woff2","font/woff2"],["word","application/msword"],["wpd","application/vnd.wordperfect"],["wpl","application/vnd.ms-wpl"],["wps","application/vnd.ms-works"],["wqd","application/vnd.wqd"],["wri","application/x-mswrite"],["wrl","model/vrml"],["wsc","message/vnd.wfa.wsc"],["wsdl","application/wsdl+xml"],["wspolicy","application/wspolicy+xml"],["wtb","application/vnd.webturbo"],["wvx","video/x-ms-wvx"],["x3d","model/x3d+xml"],["x3db","model/x3d+fastinfoset"],["x3dbz","model/x3d+binary"],["x3dv","model/x3d-vrml"],["x3dvz","model/x3d+vrml"],["x3dz","model/x3d+xml"],["x32","application/x-authorware-bin"],["x_b","model/vnd.parasolid.transmit.binary"],["x_t","model/vnd.parasolid.transmit.text"],["xaml","application/xaml+xml"],["xap","application/x-silverlight-app"],["xar","application/vnd.xara"],["xav","application/xcap-att+xml"],["xbap","application/x-ms-xbap"],["xbd","application/vnd.fujixerox.docuworks.binder"],["xbm","image/x-xbitmap"],["xca","application/xcap-caps+xml"],["xcs","application/calendar+xml"],["xdf","application/xcap-diff+xml"],["xdm","application/vnd.syncml.dm+xml"],["xdp","application/vnd.adobe.xdp+xml"],["xdssc","application/dssc+xml"],["xdw","application/vnd.fujixerox.docuworks"],["xel","application/xcap-el+xml"],["xenc","application/xenc+xml"],["xer","application/patch-ops-error+xml"],["xfdf","application/vnd.adobe.xfdf"],["xfdl","application/vnd.xfdl"],["xht","application/xhtml+xml"],["xhtml","application/xhtml+xml"],["xhvml","application/xv+xml"],["xif","image/vnd.xiff"],["xl","application/excel"],["xla","application/vnd.ms-excel"],["xlam","application/vnd.ms-excel.addin.macroEnabled.12"],["xlc","application/vnd.ms-excel"],["xlf","application/xliff+xml"],["xlm","application/vnd.ms-excel"],["xls","application/vnd.ms-excel"],["xlsb","application/vnd.ms-excel.sheet.binary.macroEnabled.12"],["xlsm","application/vnd.ms-excel.sheet.macroEnabled.12"],["xlsx","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"],["xlt","application/vnd.ms-excel"],["xltm","application/vnd.ms-excel.template.macroEnabled.12"],["xltx","application/vnd.openxmlformats-officedocument.spreadsheetml.template"],["xlw","application/vnd.ms-excel"],["xm","audio/xm"],["xml","application/xml"],["xns","application/xcap-ns+xml"],["xo","application/vnd.olpc-sugar"],["xop","application/xop+xml"],["xpi","application/x-xpinstall"],["xpl","application/xproc+xml"],["xpm","image/x-xpixmap"],["xpr","application/vnd.is-xpr"],["xps","application/vnd.ms-xpsdocument"],["xpw","application/vnd.intercon.formnet"],["xpx","application/vnd.intercon.formnet"],["xsd","application/xml"],["xsl","application/xml"],["xslt","application/xslt+xml"],["xsm","application/vnd.syncml+xml"],["xspf","application/xspf+xml"],["xul","application/vnd.mozilla.xul+xml"],["xvm","application/xv+xml"],["xvml","application/xv+xml"],["xwd","image/x-xwindowdump"],["xyz","chemical/x-xyz"],["xz","application/x-xz"],["yaml","text/yaml"],["yang","application/yang"],["yin","application/yin+xml"],["yml","text/yaml"],["ymp","text/x-suse-ymp"],["z","application/x-compress"],["z1","application/x-zmachine"],["z2","application/x-zmachine"],["z3","application/x-zmachine"],["z4","application/x-zmachine"],["z5","application/x-zmachine"],["z6","application/x-zmachine"],["z7","application/x-zmachine"],["z8","application/x-zmachine"],["zaz","application/vnd.zzazz.deck+xml"],["zip","application/zip"],["zir","application/vnd.zul"],["zirz","application/vnd.zul"],["zmm","application/vnd.handheld-entertainment+xml"],["zsh","text/x-scriptzsh"]]);function r(a,i,e){let t=function(a){let{name:i}=a;if(i&&-1!==i.lastIndexOf(".")&&!a.type){let e=i.split(".").pop().toLowerCase(),t=c.get(e);t&&Object.defineProperty(a,"type",{value:t,writable:!1,configurable:!1,enumerable:!0})}return a}(a),{webkitRelativePath:n}=a,p="string"==typeof i?i:"string"==typeof n&&n.length>0?n:`./${a.name}`;return"string"!=typeof t.path&&s(t,"path",p),void 0!==e&&Object.defineProperty(t,"handle",{value:e,writable:!1,configurable:!1,enumerable:!0}),s(t,"relativePath",p),t}function s(a,i,e){Object.defineProperty(a,i,{value:e,writable:!1,configurable:!1,enumerable:!0})}let d=[".DS_Store","Thumbs.db"];function m(a){return"object"==typeof a&&null!==a}function x(a){return a.filter(a=>-1===d.indexOf(a.name))}function u(a){if(null===a)return[];let i=[];for(let e=0;e<a.length;e++){let t=a[e];i.push(t)}return i}function v(a){if("function"!=typeof a.webkitGetAsEntry)return f(a);let i=a.webkitGetAsEntry();return i&&i.isDirectory?b(i):f(a,i)}function f(a,i){return(0,l.sH)(this,void 0,void 0,function*(){var e;if(globalThis.isSecureContext&&"function"==typeof a.getAsFileSystemHandle){let i=yield a.getAsFileSystemHandle();if(null===i)throw Error(`${a} is not a File`);if(void 0!==i){let a=yield i.getFile();return a.handle=i,r(a)}}let t=a.getAsFile();if(!t)throw Error(`${a} is not a File`);return r(t,null!=(e=null==i?void 0:i.fullPath)?e:void 0)})}function g(a){return(0,l.sH)(this,void 0,void 0,function*(){return a.isDirectory?b(a):function(a){return(0,l.sH)(this,void 0,void 0,function*(){return new Promise((i,e)=>{a.file(e=>{i(r(e,a.fullPath))},a=>{e(a)})})})}(a)})}function b(a){let i=a.createReader();return new Promise((a,e)=>{let t=[];!function n(){i.readEntries(i=>(0,l.sH)(this,void 0,void 0,function*(){if(i.length){let a=Promise.all(i.map(g));t.push(a),n()}else try{let i=yield Promise.all(t);a(i)}catch(a){e(a)}}),a=>{e(a)})}()})}var h=e(27406);function y(a){return function(a){if(Array.isArray(a))return A(a)}(a)||function(a){if("undefined"!=typeof Symbol&&null!=a[Symbol.iterator]||null!=a["@@iterator"])return Array.from(a)}(a)||D(a)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function w(a,i){var e=Object.keys(a);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(a);i&&(t=t.filter(function(i){return Object.getOwnPropertyDescriptor(a,i).enumerable})),e.push.apply(e,t)}return e}function k(a){for(var i=1;i<arguments.length;i++){var e=null!=arguments[i]?arguments[i]:{};i%2?w(Object(e),!0).forEach(function(i){j(a,i,e[i])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(e)):w(Object(e)).forEach(function(i){Object.defineProperty(a,i,Object.getOwnPropertyDescriptor(e,i))})}return a}function j(a,i,e){return i in a?Object.defineProperty(a,i,{value:e,enumerable:!0,configurable:!0,writable:!0}):a[i]=e,a}function z(a,i){return function(a){if(Array.isArray(a))return a}(a)||function(a,i){var e,t,n=null==a?null:"undefined"!=typeof Symbol&&a[Symbol.iterator]||a["@@iterator"];if(null!=n){var p=[],o=!0,l=!1;try{for(n=n.call(a);!(o=(e=n.next()).done)&&(p.push(e.value),!i||p.length!==i);o=!0);}catch(a){l=!0,t=a}finally{try{o||null==n.return||n.return()}finally{if(l)throw t}}return p}}(a,i)||D(a,i)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function D(a,i){if(a){if("string"==typeof a)return A(a,i);var e=Object.prototype.toString.call(a).slice(8,-1);if("Object"===e&&a.constructor&&(e=a.constructor.name),"Map"===e||"Set"===e)return Array.from(a);if("Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e))return A(a,i)}}function A(a,i){(null==i||i>a.length)&&(i=a.length);for(var e=0,t=Array(i);e<i;e++)t[e]=a[e];return t}var O="function"==typeof h?h:h.default,q=function(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",i=a.split(","),e=i.length>1?"one of ".concat(i.join(", ")):i[0];return{code:"file-invalid-type",message:"File type must be ".concat(e)}},P=function(a){return{code:"file-too-large",message:"File is larger than ".concat(a," ").concat(1===a?"byte":"bytes")}},N=function(a){return{code:"file-too-small",message:"File is smaller than ".concat(a," ").concat(1===a?"byte":"bytes")}},E={code:"too-many-files",message:"Too many files"};function F(a,i){var e="application/x-moz-file"===a.type||O(a,i);return[e,e?null:q(i)]}function S(a,i,e){if(C(a.size)){if(C(i)&&C(e)){if(a.size>e)return[!1,P(e)];if(a.size<i)return[!1,N(i)]}else if(C(i)&&a.size<i)return[!1,N(i)];else if(C(e)&&a.size>e)return[!1,P(e)]}return[!0,null]}function C(a){return null!=a}function R(a){return"function"==typeof a.isPropagationStopped?a.isPropagationStopped():void 0!==a.cancelBubble&&a.cancelBubble}function _(a){return a.dataTransfer?Array.prototype.some.call(a.dataTransfer.types,function(a){return"Files"===a||"application/x-moz-file"===a}):!!a.target&&!!a.target.files}function T(a){a.preventDefault()}function M(){for(var a=arguments.length,i=Array(a),e=0;e<a;e++)i[e]=arguments[e];return function(a){for(var e=arguments.length,t=Array(e>1?e-1:0),n=1;n<e;n++)t[n-1]=arguments[n];return i.some(function(i){return!R(a)&&i&&i.apply(void 0,[a].concat(t)),R(a)})}}function I(a){return"audio/*"===a||"video/*"===a||"image/*"===a||"text/*"===a||"application/*"===a||/\w+\/[-+.\w]+/g.test(a)}function B(a){return/^.*\.[\w]+$/.test(a)}var L=["children"],H=["open"],$=["refKey","role","onKeyDown","onFocus","onBlur","onClick","onDragEnter","onDragOver","onDragLeave","onDrop"],K=["refKey","onChange","onClick"];function G(a,i){return function(a){if(Array.isArray(a))return a}(a)||function(a,i){var e,t,n=null==a?null:"undefined"!=typeof Symbol&&a[Symbol.iterator]||a["@@iterator"];if(null!=n){var p=[],o=!0,l=!1;try{for(n=n.call(a);!(o=(e=n.next()).done)&&(p.push(e.value),!i||p.length!==i);o=!0);}catch(a){l=!0,t=a}finally{try{o||null==n.return||n.return()}finally{if(l)throw t}}return p}}(a,i)||U(a,i)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function U(a,i){if(a){if("string"==typeof a)return W(a,i);var e=Object.prototype.toString.call(a).slice(8,-1);if("Object"===e&&a.constructor&&(e=a.constructor.name),"Map"===e||"Set"===e)return Array.from(a);if("Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e))return W(a,i)}}function W(a,i){(null==i||i>a.length)&&(i=a.length);for(var e=0,t=Array(i);e<i;e++)t[e]=a[e];return t}function Y(a,i){var e=Object.keys(a);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(a);i&&(t=t.filter(function(i){return Object.getOwnPropertyDescriptor(a,i).enumerable})),e.push.apply(e,t)}return e}function J(a){for(var i=1;i<arguments.length;i++){var e=null!=arguments[i]?arguments[i]:{};i%2?Y(Object(e),!0).forEach(function(i){V(a,i,e[i])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(e)):Y(Object(e)).forEach(function(i){Object.defineProperty(a,i,Object.getOwnPropertyDescriptor(e,i))})}return a}function V(a,i,e){return i in a?Object.defineProperty(a,i,{value:e,enumerable:!0,configurable:!0,writable:!0}):a[i]=e,a}function X(a,i){if(null==a)return{};var e,t,n=function(a,i){if(null==a)return{};var e,t,n={},p=Object.keys(a);for(t=0;t<p.length;t++)e=p[t],i.indexOf(e)>=0||(n[e]=a[e]);return n}(a,i);if(Object.getOwnPropertySymbols){var p=Object.getOwnPropertySymbols(a);for(t=0;t<p.length;t++)e=p[t],!(i.indexOf(e)>=0)&&Object.prototype.propertyIsEnumerable.call(a,e)&&(n[e]=a[e])}return n}var Q=(0,n.forwardRef)(function(a,i){var e=a.children,t=ai(X(a,L)),p=t.open,o=X(t,H);return(0,n.useImperativeHandle)(i,function(){return{open:p}},[p]),n.createElement(n.Fragment,null,e(J(J({},o),{},{open:p})))});Q.displayName="Dropzone";var Z={disabled:!1,getFilesFromEvent:function(a){return(0,l.sH)(this,void 0,void 0,function*(){var i;if(m(a)&&m(a.dataTransfer))return function(a,i){return(0,l.sH)(this,void 0,void 0,function*(){if(a.items){let e=u(a.items).filter(a=>"file"===a.kind);return"drop"!==i?e:x(function a(i){return i.reduce((i,e)=>[...i,...Array.isArray(e)?a(e):[e]],[])}((yield Promise.all(e.map(v)))))}return x(u(a.files).map(a=>r(a)))})}(a.dataTransfer,a.type);if(m(i=a)&&m(i.target))return u(a.target.files).map(a=>r(a));return Array.isArray(a)&&a.every(a=>"getFile"in a&&"function"==typeof a.getFile)?function(a){return(0,l.sH)(this,void 0,void 0,function*(){return(yield Promise.all(a.map(a=>a.getFile()))).map(a=>r(a))})}(a):[]})},maxSize:1/0,minSize:0,multiple:!0,maxFiles:0,preventDropOnDocument:!0,noClick:!1,noKeyboard:!1,noDrag:!1,noDragEventsBubbling:!1,validator:null,useFsAccessApi:!1,autoFocus:!1};Q.defaultProps=Z,Q.propTypes={children:o.func,accept:o.objectOf(o.arrayOf(o.string)),multiple:o.bool,preventDropOnDocument:o.bool,noClick:o.bool,noKeyboard:o.bool,noDrag:o.bool,noDragEventsBubbling:o.bool,minSize:o.number,maxSize:o.number,maxFiles:o.number,disabled:o.bool,getFilesFromEvent:o.func,onFileDialogCancel:o.func,onFileDialogOpen:o.func,useFsAccessApi:o.bool,autoFocus:o.bool,onDragEnter:o.func,onDragLeave:o.func,onDragOver:o.func,onDrop:o.func,onDropAccepted:o.func,onDropRejected:o.func,onError:o.func,validator:o.func};var aa={isFocused:!1,isFileDialogActive:!1,isDragActive:!1,isDragAccept:!1,isDragReject:!1,acceptedFiles:[],fileRejections:[]};function ai(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},i=J(J({},Z),a),e=i.accept,t=i.disabled,p=i.getFilesFromEvent,o=i.maxSize,l=i.minSize,c=i.multiple,r=i.maxFiles,s=i.onDragEnter,d=i.onDragLeave,m=i.onDragOver,x=i.onDrop,u=i.onDropAccepted,v=i.onDropRejected,f=i.onFileDialogCancel,g=i.onFileDialogOpen,b=i.useFsAccessApi,h=i.autoFocus,w=i.preventDropOnDocument,D=i.noClick,A=i.noKeyboard,O=i.noDrag,q=i.noDragEventsBubbling,P=i.onError,N=i.validator,L=(0,n.useMemo)(function(){return C(e)?Object.entries(e).reduce(function(a,i){var e=z(i,2),t=e[0],n=e[1];return[].concat(y(a),[t],y(n))},[]).filter(function(a){return I(a)||B(a)}).join(","):void 0},[e]),H=(0,n.useMemo)(function(){return C(e)?[{description:"Files",accept:Object.entries(e).filter(function(a){var i=z(a,2),e=i[0],t=i[1],n=!0;return I(e)||(console.warn('Skipped "'.concat(e,'" because it is not a valid MIME type. Check https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/MIME_types/Common_types for a list of valid MIME types.')),n=!1),Array.isArray(t)&&t.every(B)||(console.warn('Skipped "'.concat(e,'" because an invalid file extension was provided.')),n=!1),n}).reduce(function(a,i){var e=z(i,2),t=e[0],n=e[1];return k(k({},a),{},j({},t,n))},{})}]:e},[e]),Y=(0,n.useMemo)(function(){return"function"==typeof g?g:at},[g]),Q=(0,n.useMemo)(function(){return"function"==typeof f?f:at},[f]),ai=(0,n.useRef)(null),an=(0,n.useRef)(null),ap=G((0,n.useReducer)(ae,aa),2),ao=ap[0],al=ap[1],ac=ao.isFocused,ar=ao.isFileDialogActive,as=(0,n.useRef)("undefined"!=typeof window&&window.isSecureContext&&b&&"showOpenFilePicker"in window),ad=function(){!as.current&&ar&&setTimeout(function(){an.current&&(an.current.files.length||(al({type:"closeDialog"}),Q()))},300)};(0,n.useEffect)(function(){return window.addEventListener("focus",ad,!1),function(){window.removeEventListener("focus",ad,!1)}},[an,ar,Q,as]);var am=(0,n.useRef)([]),ax=function(a){ai.current&&ai.current.contains(a.target)||(a.preventDefault(),am.current=[])};(0,n.useEffect)(function(){return w&&(document.addEventListener("dragover",T,!1),document.addEventListener("drop",ax,!1)),function(){w&&(document.removeEventListener("dragover",T),document.removeEventListener("drop",ax))}},[ai,w]),(0,n.useEffect)(function(){return!t&&h&&ai.current&&ai.current.focus(),function(){}},[ai,h,t]);var au=(0,n.useCallback)(function(a){P?P(a):console.error(a)},[P]),av=(0,n.useCallback)(function(a){var i;a.preventDefault(),a.persist(),aq(a),am.current=[].concat(function(a){if(Array.isArray(a))return W(a)}(i=am.current)||function(a){if("undefined"!=typeof Symbol&&null!=a[Symbol.iterator]||null!=a["@@iterator"])return Array.from(a)}(i)||U(i)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),[a.target]),_(a)&&Promise.resolve(p(a)).then(function(i){if(!R(a)||q){var e,t,n,p,d,m,x,u,v=i.length,f=v>0&&(t=(e={files:i,accept:L,minSize:l,maxSize:o,multiple:c,maxFiles:r,validator:N}).files,n=e.accept,p=e.minSize,d=e.maxSize,m=e.multiple,x=e.maxFiles,u=e.validator,(!!m||!(t.length>1))&&(!m||!(x>=1)||!(t.length>x))&&t.every(function(a){var i=z(F(a,n),1)[0],e=z(S(a,p,d),1)[0],t=u?u(a):null;return i&&e&&!t}));al({isDragAccept:f,isDragReject:v>0&&!f,isDragActive:!0,type:"setDraggedFiles"}),s&&s(a)}}).catch(function(a){return au(a)})},[p,s,au,q,L,l,o,c,r,N]),af=(0,n.useCallback)(function(a){a.preventDefault(),a.persist(),aq(a);var i=_(a);if(i&&a.dataTransfer)try{a.dataTransfer.dropEffect="copy"}catch(a){}return i&&m&&m(a),!1},[m,q]),ag=(0,n.useCallback)(function(a){a.preventDefault(),a.persist(),aq(a);var i=am.current.filter(function(a){return ai.current&&ai.current.contains(a)}),e=i.indexOf(a.target);-1!==e&&i.splice(e,1),am.current=i,!(i.length>0)&&(al({type:"setDraggedFiles",isDragActive:!1,isDragAccept:!1,isDragReject:!1}),_(a)&&d&&d(a))},[ai,d,q]),ab=(0,n.useCallback)(function(a,i){var e=[],t=[];a.forEach(function(a){var i=G(F(a,L),2),n=i[0],p=i[1],c=G(S(a,l,o),2),r=c[0],s=c[1],d=N?N(a):null;if(n&&r&&!d)e.push(a);else{var m=[p,s];d&&(m=m.concat(d)),t.push({file:a,errors:m.filter(function(a){return a})})}}),(!c&&e.length>1||c&&r>=1&&e.length>r)&&(e.forEach(function(a){t.push({file:a,errors:[E]})}),e.splice(0)),al({acceptedFiles:e,fileRejections:t,isDragReject:t.length>0,type:"setFiles"}),x&&x(e,t,i),t.length>0&&v&&v(t,i),e.length>0&&u&&u(e,i)},[al,c,L,l,o,r,x,u,v,N]),ah=(0,n.useCallback)(function(a){a.preventDefault(),a.persist(),aq(a),am.current=[],_(a)&&Promise.resolve(p(a)).then(function(i){(!R(a)||q)&&ab(i,a)}).catch(function(a){return au(a)}),al({type:"reset"})},[p,ab,au,q]),ay=(0,n.useCallback)(function(){if(as.current){al({type:"openDialog"}),Y(),window.showOpenFilePicker({multiple:c,types:H}).then(function(a){return p(a)}).then(function(a){ab(a,null),al({type:"closeDialog"})}).catch(function(a){a instanceof DOMException&&("AbortError"===a.name||a.code===a.ABORT_ERR)?(Q(a),al({type:"closeDialog"})):a instanceof DOMException&&("SecurityError"===a.name||a.code===a.SECURITY_ERR)?(as.current=!1,an.current?(an.current.value=null,an.current.click()):au(Error("Cannot open the file picker because the https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API is not supported and no <input> was provided."))):au(a)});return}an.current&&(al({type:"openDialog"}),Y(),an.current.value=null,an.current.click())},[al,Y,Q,b,ab,au,H,c]),aw=(0,n.useCallback)(function(a){ai.current&&ai.current.isEqualNode(a.target)&&(" "===a.key||"Enter"===a.key||32===a.keyCode||13===a.keyCode)&&(a.preventDefault(),ay())},[ai,ay]),ak=(0,n.useCallback)(function(){al({type:"focus"})},[]),aj=(0,n.useCallback)(function(){al({type:"blur"})},[]),az=(0,n.useCallback)(function(){D||(function(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window.navigator.userAgent;return -1!==a.indexOf("MSIE")||-1!==a.indexOf("Trident/")||-1!==a.indexOf("Edge/")}()?setTimeout(ay,0):ay())},[D,ay]),aD=function(a){return t?null:a},aA=function(a){return A?null:aD(a)},aO=function(a){return O?null:aD(a)},aq=function(a){q&&a.stopPropagation()},aP=(0,n.useMemo)(function(){return function(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},i=a.refKey,e=a.role,n=a.onKeyDown,p=a.onFocus,o=a.onBlur,l=a.onClick,c=a.onDragEnter,r=a.onDragOver,s=a.onDragLeave,d=a.onDrop,m=X(a,$);return J(J(V({onKeyDown:aA(M(n,aw)),onFocus:aA(M(p,ak)),onBlur:aA(M(o,aj)),onClick:aD(M(l,az)),onDragEnter:aO(M(c,av)),onDragOver:aO(M(r,af)),onDragLeave:aO(M(s,ag)),onDrop:aO(M(d,ah)),role:"string"==typeof e&&""!==e?e:"presentation"},void 0===i?"ref":i,ai),t||A?{}:{tabIndex:0}),m)}},[ai,aw,ak,aj,az,av,af,ag,ah,A,O,t]),aN=(0,n.useCallback)(function(a){a.stopPropagation()},[]),aE=(0,n.useMemo)(function(){return function(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},i=a.refKey,e=a.onChange,t=a.onClick,n=X(a,K);return J(J({},V({accept:L,multiple:c,type:"file",style:{border:0,clip:"rect(0, 0, 0, 0)",clipPath:"inset(50%)",height:"1px",margin:"0 -1px -1px 0",overflow:"hidden",padding:0,position:"absolute",width:"1px",whiteSpace:"nowrap"},onChange:aD(M(e,ah)),onClick:aD(M(t,aN)),tabIndex:-1},void 0===i?"ref":i,an)),n)}},[an,e,c,ah,t]);return J(J({},ao),{},{isFocused:ac&&!t,getRootProps:aP,getInputProps:aE,rootRef:ai,inputRef:an,open:aD(ay)})}function ae(a,i){switch(i.type){case"focus":return J(J({},a),{},{isFocused:!0});case"blur":return J(J({},a),{},{isFocused:!1});case"openDialog":return J(J({},aa),{},{isFileDialogActive:!0});case"closeDialog":return J(J({},a),{},{isFileDialogActive:!1});case"setDraggedFiles":return J(J({},a),{},{isDragActive:i.isDragActive,isDragAccept:i.isDragAccept,isDragReject:i.isDragReject});case"setFiles":return J(J({},a),{},{acceptedFiles:i.acceptedFiles,fileRejections:i.fileRejections,isDragReject:i.isDragReject});case"reset":return J({},aa);default:return a}}function at(){}var an=e(10022),ap=e(11860),ao=e(16023),al=e(93613);function ac({onFileSelect:a,selectedFile:i,onFileRemove:e}){let[p,o]=(0,n.useState)(null),{getRootProps:l,getInputProps:c,isDragActive:r}=ai({onDrop:(0,n.useCallback)((i,e)=>{if(o(null),e.length>0){let a=e[0];a.errors[0]?.code==="file-too-large"?o("حجم الملف كبير جداً. الحد الأقصى هو 10 ميجابايت."):a.errors[0]?.code==="file-invalid-type"?o("نوع الملف غير مدعوم. يرجى رفع ملف PDF فقط."):o("حدث خطأ في رفع الملف. يرجى المحاولة مرة أخرى.");return}i.length>0&&a(i[0])},[a]),accept:{"application/pdf":[".pdf"]},maxSize:0xa00000,multiple:!1});return i?(0,t.jsx)("div",{className:"border-2 border-gray-200 border-dashed rounded-lg p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center ml-4",children:(0,t.jsx)(an.A,{className:"w-6 h-6 text-red-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-sm font-medium text-gray-900",children:i.name}),(0,t.jsx)("p",{className:"text-sm text-gray-500",children:(a=>{if(0===a)return"0 Bytes";let i=Math.floor(Math.log(a)/Math.log(1024));return parseFloat((a/Math.pow(1024,i)).toFixed(2))+" "+["Bytes","KB","MB","GB"][i]})(i.size)})]})]}),e&&(0,t.jsx)("button",{onClick:e,className:"p-2 text-gray-400 hover:text-gray-600 transition-colors",children:(0,t.jsx)(ap.A,{className:"w-5 h-5"})})]})}):(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{...l(),className:`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${r?"border-blue-400 bg-blue-50":"border-gray-300 hover:border-gray-400"}`,children:[(0,t.jsx)("input",{...c()}),(0,t.jsxs)("div",{className:"flex flex-col items-center",children:[(0,t.jsx)("div",{className:`w-16 h-16 rounded-full flex items-center justify-center mb-4 ${r?"bg-blue-100":"bg-gray-100"}`,children:(0,t.jsx)(ao.A,{className:`w-8 h-8 ${r?"text-blue-600":"text-gray-600"}`})}),(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:r?"اتركه هنا...":"اسحب ملف PDF هنا"}),(0,t.jsxs)("p",{className:"text-gray-500 mb-4",children:["أو ",(0,t.jsx)("span",{className:"text-blue-600 font-medium",children:"انقر للتصفح"})]}),(0,t.jsxs)("div",{className:"text-sm text-gray-400",children:[(0,t.jsx)("p",{children:"الحد الأقصى: 10 ميجابايت"}),(0,t.jsx)("p",{children:"الصيغ المدعومة: PDF فقط"})]})]})]}),p&&(0,t.jsxs)("div",{className:"flex items-center p-4 bg-red-50 border border-red-200 rounded-lg",children:[(0,t.jsx)(al.A,{className:"w-5 h-5 text-red-500 ml-3"}),(0,t.jsx)("p",{className:"text-sm text-red-700",children:p})]})]})}var ar=e(45583),as=e(28947),ad=e(28559),am=e(85814),ax=e.n(am);function au(){let[a,i]=(0,n.useState)(null),[e,o]=(0,n.useState)(!1),l=(0,p.useRouter)(),c=[{icon:ar.A,title:"معالجة سريعة",description:"تحليل ذكي للمحتوى في ثوانٍ معدودة"},{icon:as.A,title:"دقة عالية",description:"استخراج المفاهيم الرئيسية بدقة متناهية"},{icon:an.A,title:"أسئلة متنوعة",description:"إنشاء أسئلة متعددة الأنواع والصعوبات"}];return e?(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,t.jsxs)("div",{className:"text-center max-w-md mx-auto p-8",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-16 w-16 border-b-4 border-blue-600 mx-auto mb-6"}),(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"جاري معالجة الملف"}),(0,t.jsx)("p",{className:"text-gray-600 mb-4",children:"نقوم بتحليل محتوى الملف وإعداد الاختبار التفاعلي..."}),(0,t.jsx)("div",{className:"bg-gray-200 rounded-full h-2 mb-4",children:(0,t.jsx)("div",{className:"bg-blue-600 h-2 rounded-full animate-pulse",style:{width:"70%"}})}),(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"هذا قد يستغرق بضع ثوانٍ"})]})}):(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsx)("div",{className:"flex items-center mb-4",children:(0,t.jsxs)(ax(),{href:"/dashboard",className:"flex items-center text-gray-600 hover:text-gray-900 transition-colors ml-4",children:[(0,t.jsx)(ad.A,{className:"w-5 h-5 ml-2"}),"العودة للوحة التحكم"]})}),(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"رفع ملف جديد"}),(0,t.jsx)("p",{className:"text-gray-600",children:"ارفع ملف PDF لتحويله إلى اختبار تفاعلي باستخدام الذكاء الاصطناعي"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,t.jsx)("div",{className:"lg:col-span-2",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-8",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsx)("div",{className:"inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-4",children:(0,t.jsx)(ao.A,{className:"w-8 h-8 text-blue-600"})}),(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"ارفع ملف PDF"}),(0,t.jsx)("p",{className:"text-gray-600",children:"اختر ملف PDF يحتوي على المحتوى التعليمي الذي تريد تحويله إلى اختبار"})]}),(0,t.jsx)(ac,{onFileSelect:a=>{i(a)}}),a&&(0,t.jsx)("div",{className:"mt-8 p-6 bg-green-50 border border-green-200 rounded-lg",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(an.A,{className:"w-8 h-8 text-green-600 ml-3"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium text-green-900",children:a.name}),(0,t.jsxs)("p",{className:"text-sm text-green-700",children:[(a.size/1048576).toFixed(2)," MB"]})]})]}),(0,t.jsx)("button",{onClick:()=>{if(!a)return;o(!0);let i={name:a.name,size:a.size,type:a.type,uploadedAt:new Date().toISOString()};localStorage.setItem("uploadedPDF",JSON.stringify(i)),setTimeout(()=>{l.push("/analyze")},2e3)},className:"px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors",children:"بدء التحليل"})]})})]})}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"ما يمكنك توقعه"}),(0,t.jsx)("div",{className:"space-y-4",children:c.map((a,i)=>(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)(a.icon,{className:"w-6 h-6 text-blue-600"})}),(0,t.jsxs)("div",{className:"mr-3",children:[(0,t.jsx)("h4",{className:"font-medium text-gray-900",children:a.title}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:a.description})]})]},i))})]}),(0,t.jsxs)("div",{className:"bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-6 border border-blue-200",children:[(0,t.jsx)("h3",{className:"font-semibold text-gray-900 mb-3",children:"نصائح للحصول على أفضل النتائج"}),(0,t.jsxs)("ul",{className:"text-sm text-gray-700 space-y-2",children:[(0,t.jsxs)("li",{className:"flex items-start",children:[(0,t.jsx)("span",{className:"w-2 h-2 bg-blue-500 rounded-full mt-2 ml-2 flex-shrink-0"}),"تأكد من وضوح النص في ملف PDF"]}),(0,t.jsxs)("li",{className:"flex items-start",children:[(0,t.jsx)("span",{className:"w-2 h-2 bg-blue-500 rounded-full mt-2 ml-2 flex-shrink-0"}),"استخدم ملفات تحتوي على محتوى تعليمي منظم"]}),(0,t.jsxs)("li",{className:"flex items-start",children:[(0,t.jsx)("span",{className:"w-2 h-2 bg-blue-500 rounded-full mt-2 ml-2 flex-shrink-0"}),"تجنب الملفات التي تحتوي على صور فقط"]})]})]}),(0,t.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:[(0,t.jsx)("h4",{className:"font-medium text-yellow-900 mb-2",children:"ملاحظة مهمة"}),(0,t.jsx)("p",{className:"text-sm text-yellow-800",children:"يدعم النظام حالياً ملفات PDF باللغتين العربية والإنجليزية بحجم أقصى 10 MB"})]})]})]})]})}},13051:(a,i,e)=>{Promise.resolve().then(e.bind(e,63144))},14217:(a,i,e)=>{Promise.resolve().then(e.bind(e,67335))},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:a=>{"use strict";a.exports=require("process")},21820:a=>{"use strict";a.exports=require("os")},27406:(a,i)=>{"use strict";i.__esModule=!0,i.default=function(a,i){if(a&&i){var e=Array.isArray(i)?i:i.split(",");if(0===e.length)return!0;var t=a.name||"",n=(a.type||"").toLowerCase(),p=n.replace(/\/.*$/,"");return e.some(function(a){var i=a.trim().toLowerCase();return"."===i.charAt(0)?t.toLowerCase().endsWith(i):i.endsWith("/*")?p===i.replace(/\/.*$/,""):n===i})}return!0}},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},28559:(a,i,e)=>{"use strict";e.d(i,{A:()=>t});let t=(0,e(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},28947:(a,i,e)=>{"use strict";e.d(i,{A:()=>t});let t=(0,e(62688).A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},34452:a=>{"use strict";a.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},34570:(a,i,e)=>{"use strict";e.d(i,{A:()=>l});var t=e(60687);e(43210);var n=e(16189),p=e(7613),o=e(9776);function l({children:a,requiredRole:i,redirectTo:e="/auth/login"}){let{user:l,userProfile:c,loading:r,isAuthenticated:s}=(0,p.A)();return((0,n.useRouter)(),r||!s||i&&c&&c.role!==i)?(0,t.jsx)(o.A,{}):(0,t.jsx)(t.Fragment,{children:a})}},34631:a=>{"use strict";a.exports=require("tls")},37366:a=>{"use strict";a.exports=require("dns")},45583:(a,i,e)=>{"use strict";e.d(i,{A:()=>t});let t=(0,e(62688).A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},55511:a=>{"use strict";a.exports=require("crypto")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63144:(a,i,e)=>{"use strict";e.r(i),e.d(i,{default:()=>t});let t=(0,e(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\templete\\\\pdf-exam-generator\\\\src\\\\app\\\\dashboard\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\layout.tsx","default")},63902:(a,i,e)=>{"use strict";e.r(i),e.d(i,{default:()=>o});var t=e(60687),n=e(34570),p=e(60149);function o({children:a}){return(0,t.jsx)(n.A,{children:(0,t.jsx)(p.N,{children:a})})}},66195:(a,i,e)=>{Promise.resolve().then(e.bind(e,63902))},67335:(a,i,e)=>{"use strict";e.r(i),e.d(i,{default:()=>t});let t=(0,e(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\templete\\\\pdf-exam-generator\\\\src\\\\app\\\\dashboard\\\\upload\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\upload\\page.tsx","default")},70440:(a,i,e)=>{"use strict";e.r(i),e.d(i,{default:()=>n});var t=e(31658);let n=async a=>[{type:"image/x-icon",sizes:"16x16",url:(0,t.fillMetadataSegment)(".",await a.params,"favicon.ico")+""}]},73496:a=>{"use strict";a.exports=require("http2")},74075:a=>{"use strict";a.exports=require("zlib")},79369:(a,i,e)=>{Promise.resolve().then(e.bind(e,12866))},79551:a=>{"use strict";a.exports=require("url")},81630:a=>{"use strict";a.exports=require("http")},82492:(a,i,e)=>{"use strict";e.r(i),e.d(i,{GlobalError:()=>o.a,__next_app__:()=>d,pages:()=>s,routeModule:()=>m,tree:()=>r});var t=e(65239),n=e(48088),p=e(88170),o=e.n(p),l=e(30893),c={};for(let a in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(a)&&(c[a]=()=>l[a]);e.d(i,c);let r={children:["",{children:["dashboard",{children:["upload",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(e.bind(e,67335)),"D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\upload\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(e.bind(e,63144)),"D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async a=>(await Promise.resolve().then(e.bind(e,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:"/manifest.webmanifest"}}]},{layout:[()=>Promise.resolve().then(e.bind(e,94431)),"D:\\templete\\pdf-exam-generator\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(e.t.bind(e,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(e.t.bind(e,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(e.t.bind(e,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async a=>(await Promise.resolve().then(e.bind(e,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:"/manifest.webmanifest"}}]}.children,s=["D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\upload\\page.tsx"],d={require:e,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/dashboard/upload/page",pathname:"/dashboard/upload",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:r}})},84031:(a,i,e)=>{"use strict";var t=e(34452);function n(){}function p(){}p.resetWarningCache=n,a.exports=function(){function a(a,i,e,n,p,o){if(o!==t){var l=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw l.name="Invariant Violation",l}}function i(){return a}a.isRequired=a;var e={array:a,bigint:a,bool:a,func:a,number:a,object:a,string:a,symbol:a,any:a,arrayOf:i,element:a,elementType:a,instanceOf:i,node:a,objectOf:i,oneOf:i,oneOfType:i,shape:i,exact:i,checkPropTypes:p,resetWarningCache:n};return e.PropTypes=e,e}},87955:(a,i,e)=>{a.exports=e(84031)()},91645:a=>{"use strict";a.exports=require("net")},93613:(a,i,e)=>{"use strict";e.d(i,{A:()=>t});let t=(0,e(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},94735:a=>{"use strict";a.exports=require("events")}};var i=require("../../../webpack-runtime.js");i.C(a);var e=a=>i(i.s=a),t=i.X(0,[447,248,658,647],()=>e(82492));module.exports=t})();