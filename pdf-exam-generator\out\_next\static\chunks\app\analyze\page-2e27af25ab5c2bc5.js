(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[545],{381:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},537:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>M});var a=t(5155),l=t(2115),r=t(5695),i=t(7434),n=t(9946);let c=(0,n.A)("file-type",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M9 13v-1h6v1",key:"1bb014"}],["path",{d:"M12 12v6",key:"3ahymv"}],["path",{d:"M11 18h2",key:"12mj7e"}]]),d=(0,n.A)("hard-drive",[["line",{x1:"22",x2:"2",y1:"12",y2:"12",key:"1y58io"}],["path",{d:"M5.45 5.11 2 12v6a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-6l-3.45-6.89A2 2 0 0 0 16.76 4H7.24a2 2 0 0 0-1.79 1.11z",key:"oot6mr"}],["line",{x1:"6",x2:"6.01",y1:"16",y2:"16",key:"sgf278"}],["line",{x1:"10",x2:"10.01",y1:"16",y2:"16",key:"1l4acy"}]]);var x=t(9074);function m(e){let{pdfInfo:s}=e;return(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden",children:[(0,a.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,a.jsxs)("h2",{className:"text-lg font-semibold text-gray-900 flex items-center",children:[(0,a.jsx)(i.A,{className:"w-5 h-5 mr-2 text-blue-600"}),"Document Preview"]})}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"bg-gray-100 rounded-lg p-8 text-center mb-6",children:[(0,a.jsx)("div",{className:"inline-flex items-center justify-center w-20 h-20 bg-red-100 rounded-lg mb-4",children:(0,a.jsx)(i.A,{className:"w-10 h-10 text-red-600"})}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:s.name}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"PDF Document Preview"}),(0,a.jsxs)("div",{className:"flex justify-center space-x-2 mb-4",children:[[1,2,3].map(e=>(0,a.jsx)("div",{className:"w-12 h-16 bg-white border border-gray-300 rounded shadow-sm flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-xs text-gray-500",children:e})},e)),(0,a.jsx)("div",{className:"w-12 h-16 bg-gray-200 border border-gray-300 rounded shadow-sm flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-xs text-gray-400",children:"..."})})]}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"Note: Full PDF preview will be available in the production version"})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-900 mb-3",children:"Document Information"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 p-3 bg-gray-50 rounded-lg",children:[(0,a.jsx)(c,{className:"w-5 h-5 text-gray-500"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"File Type"}),(0,a.jsx)("p",{className:"text-xs text-gray-600",children:s.type})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3 p-3 bg-gray-50 rounded-lg",children:[(0,a.jsx)(d,{className:"w-5 h-5 text-gray-500"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"File Size"}),(0,a.jsx)("p",{className:"text-xs text-gray-600",children:(e=>{if(0===e)return"0 Bytes";let s=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,s)).toFixed(2))+" "+["Bytes","KB","MB","GB"][s]})(s.size)})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3 p-3 bg-gray-50 rounded-lg",children:[(0,a.jsx)(x.A,{className:"w-5 h-5 text-gray-500"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"Last Modified"}),(0,a.jsx)("p",{className:"text-xs text-gray-600",children:new Date(s.lastModified).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})})]})]})]})]}),(0,a.jsxs)("div",{className:"mt-6 pt-6 border-t border-gray-200",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-900 mb-3",children:"Document Analysis"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"text-center p-3 bg-blue-50 rounded-lg",children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-blue-600",children:"12"}),(0,a.jsx)("p",{className:"text-xs text-blue-700",children:"Pages"})]}),(0,a.jsxs)("div",{className:"text-center p-3 bg-green-50 rounded-lg",children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-green-600",children:"2,847"}),(0,a.jsx)("p",{className:"text-xs text-green-700",children:"Words"})]}),(0,a.jsxs)("div",{className:"text-center p-3 bg-purple-50 rounded-lg",children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-purple-600",children:"15"}),(0,a.jsx)("p",{className:"text-xs text-purple-700",children:"Topics"})]}),(0,a.jsxs)("div",{className:"text-center p-3 bg-orange-50 rounded-lg",children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-orange-600",children:"8"}),(0,a.jsx)("p",{className:"text-xs text-orange-700",children:"Concepts"})]})]})]})]})]})}var o=t(381),h=t(4869),u=t(6785);let g=(0,n.A)("shuffle",[["path",{d:"m18 14 4 4-4 4",key:"10pe0f"}],["path",{d:"m18 2 4 4-4 4",key:"pucp1d"}],["path",{d:"M2 18h1.973a4 4 0 0 0 3.3-1.7l5.454-8.6a4 4 0 0 1 3.3-1.7H22",key:"1ailkh"}],["path",{d:"M2 6h1.972a4 4 0 0 1 3.6 2.2",key:"km57vx"}],["path",{d:"M22 18h-6.041a4 4 0 0 1-3.3-1.8l-.359-.45",key:"os18l9"}]]);var p=t(4186);let y=(0,n.A)("play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]]);function j(){let e=(0,r.useRouter)(),[s,t]=(0,l.useState)({language:"english",questionCount:20,questionTypes:{multipleChoice:!0,trueFalse:!0,shortAnswer:!1,fillInBlank:!1},difficulty:"intermediate",randomize:!0,timeLimit:30,hasTimeLimit:!1}),[i,n]=(0,l.useState)(!1),c=(e,s)=>{t(t=>({...t,questionTypes:{...t.questionTypes,[e]:s}}))},d=Object.values(s.questionTypes).filter(Boolean).length;return(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden",children:[(0,a.jsxs)("div",{className:"px-6 py-4 border-b border-gray-200",children:[(0,a.jsxs)("h2",{className:"text-lg font-semibold text-gray-900 flex items-center",children:[(0,a.jsx)(o.A,{className:"w-5 h-5 mr-2 text-blue-600"}),"Exam Configuration"]}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Customize your exam settings and preferences"})]}),(0,a.jsxs)("div",{className:"p-6 space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:"block text-sm font-medium text-gray-900 mb-3 flex items-center",children:[(0,a.jsx)(h.A,{className:"w-4 h-4 mr-2"}),"Language"]}),(0,a.jsxs)("select",{value:s.language,onChange:e=>t(s=>({...s,language:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[(0,a.jsx)("option",{value:"english",children:"English"}),(0,a.jsx)("option",{value:"arabic",children:"Arabic"}),(0,a.jsx)("option",{value:"auto",children:"Auto-detect"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:"block text-sm font-medium text-gray-900 mb-3",children:["Number of Questions: ",s.questionCount]}),(0,a.jsx)("input",{type:"range",min:"5",max:"50",value:s.questionCount,onChange:e=>t(s=>({...s,questionCount:parseInt(e.target.value)})),className:"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"}),(0,a.jsxs)("div",{className:"flex justify-between text-xs text-gray-500 mt-1",children:[(0,a.jsx)("span",{children:"5"}),(0,a.jsx)("span",{children:"25"}),(0,a.jsx)("span",{children:"50"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:"block text-sm font-medium text-gray-900 mb-3",children:["Question Types (",d," selected)"]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",checked:s.questionTypes.multipleChoice,onChange:e=>c("multipleChoice",e.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,a.jsx)("span",{className:"ml-3 text-sm text-gray-700",children:"Multiple Choice"})]}),(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",checked:s.questionTypes.trueFalse,onChange:e=>c("trueFalse",e.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,a.jsx)("span",{className:"ml-3 text-sm text-gray-700",children:"True/False"})]}),(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",checked:s.questionTypes.shortAnswer,onChange:e=>c("shortAnswer",e.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,a.jsx)("span",{className:"ml-3 text-sm text-gray-700",children:"Short Answer"})]}),(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",checked:s.questionTypes.fillInBlank,onChange:e=>c("fillInBlank",e.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,a.jsx)("span",{className:"ml-3 text-sm text-gray-700",children:"Fill in the Blank"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:"block text-sm font-medium text-gray-900 mb-3 flex items-center",children:[(0,a.jsx)(u.A,{className:"w-4 h-4 mr-2"}),"Difficulty Level"]}),(0,a.jsx)("div",{className:"grid grid-cols-3 gap-3",children:["beginner","intermediate","advanced"].map(e=>(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"radio",name:"difficulty",value:e,checked:s.difficulty===e,onChange:e=>t(s=>({...s,difficulty:e.target.value})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"}),(0,a.jsx)("span",{className:"ml-2 text-sm text-gray-700 capitalize",children:e})]},e))})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",checked:s.randomize,onChange:e=>t(s=>({...s,randomize:e.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,a.jsx)(g,{className:"w-4 h-4 ml-3 mr-2 text-gray-500"}),(0,a.jsx)("span",{className:"text-sm text-gray-700",children:"Randomize question order"})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:"flex items-center mb-3",children:[(0,a.jsx)("input",{type:"checkbox",checked:s.hasTimeLimit,onChange:e=>t(s=>({...s,hasTimeLimit:e.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,a.jsx)(p.A,{className:"w-4 h-4 ml-3 mr-2 text-gray-500"}),(0,a.jsx)("span",{className:"text-sm text-gray-700",children:"Set time limit"})]}),s.hasTimeLimit&&(0,a.jsxs)("div",{className:"ml-9",children:[(0,a.jsx)("input",{type:"number",min:"5",max:"180",value:s.timeLimit,onChange:e=>t(s=>({...s,timeLimit:parseInt(e.target.value)})),className:"w-20 px-3 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"}),(0,a.jsx)("span",{className:"ml-2 text-sm text-gray-600",children:"minutes"})]})]})]}),(0,a.jsxs)("div",{className:"pt-6 border-t border-gray-200",children:[(0,a.jsx)("button",{onClick:()=>{n(!0),localStorage.setItem("examConfig",JSON.stringify(s)),setTimeout(()=>{e.push("/exam")},2e3)},disabled:0===d||i,className:"w-full flex items-center justify-center px-6 py-3 bg-blue-600 text-white rounded-lg font-semibold hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl",children:i?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3"}),"Generating Exam..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(y,{className:"w-5 h-5 mr-2"}),"Generate Exam"]})}),0===d&&(0,a.jsx)("p",{className:"text-sm text-red-600 mt-2 text-center",children:"Please select at least one question type"})]})]})]})}var b=t(9376),f=t(646),N=t(5040);let v=(0,n.A)("tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]]);function k(){return(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden",children:[(0,a.jsxs)("div",{className:"px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-purple-50",children:[(0,a.jsxs)("h2",{className:"text-lg font-semibold text-gray-900 flex items-center",children:[(0,a.jsx)(b.A,{className:"w-5 h-5 mr-2 text-blue-600"}),"AI Analysis Summary"]}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Our AI has analyzed your document and identified the following insights"})]}),(0,a.jsxs)("div",{className:"p-6 space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 p-4 bg-green-50 rounded-lg border border-green-200",children:[(0,a.jsx)(f.A,{className:"w-6 h-6 text-green-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-green-900",children:"Analysis Complete"}),(0,a.jsx)("p",{className:"text-xs text-green-700",children:"Document successfully processed and analyzed"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-sm font-medium text-gray-900 mb-3 flex items-center",children:[(0,a.jsx)(N.A,{className:"w-4 h-4 mr-2"}),"Main Topics Identified"]}),(0,a.jsx)("div",{className:"space-y-3",children:[{name:"Machine Learning Fundamentals",confidence:95,questions:8},{name:"Neural Networks",confidence:88,questions:6},{name:"Data Preprocessing",confidence:92,questions:5},{name:"Model Evaluation",confidence:85,questions:4},{name:"Deep Learning",confidence:78,questions:3}].map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 mt-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),(0,a.jsxs)("span",{className:"text-xs text-gray-600",children:[e.confidence,"% confidence"]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(u.A,{className:"w-3 h-3 text-gray-500"}),(0,a.jsxs)("span",{className:"text-xs text-gray-600",children:[e.questions," potential questions"]})]})]})]}),(0,a.jsx)("div",{className:"ml-4",children:(0,a.jsx)("div",{className:"w-16 bg-gray-200 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:"".concat(e.confidence,"%")}})})})]},s))})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-sm font-medium text-gray-900 mb-3 flex items-center",children:[(0,a.jsx)(v,{className:"w-4 h-4 mr-2"}),"Key Terms & Concepts"]}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:["Supervised Learning","Unsupervised Learning","Regression","Classification","Feature Engineering","Cross-validation","Overfitting","Gradient Descent","Backpropagation","Convolutional Neural Networks"].map((e,s)=>(0,a.jsx)("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:e},s))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-900 mb-3",children:"Document Insights"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"p-3 bg-gray-50 rounded-lg",children:[(0,a.jsx)("p",{className:"text-xs text-gray-600",children:"Difficulty Level"}),(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"Intermediate"})]}),(0,a.jsxs)("div",{className:"p-3 bg-gray-50 rounded-lg",children:[(0,a.jsx)("p",{className:"text-xs text-gray-600",children:"Reading Level"}),(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"College Level"})]}),(0,a.jsxs)("div",{className:"p-3 bg-gray-50 rounded-lg",children:[(0,a.jsx)("p",{className:"text-xs text-gray-600",children:"Estimated Questions"}),(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:26})]}),(0,a.jsxs)("div",{className:"p-3 bg-gray-50 rounded-lg",children:[(0,a.jsx)("p",{className:"text-xs text-gray-600",children:"Content Type"}),(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"Educational"})]})]})]}),(0,a.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg border border-blue-200",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-blue-900 mb-2",children:"AI Recommendations"}),(0,a.jsxs)("ul",{className:"text-xs text-blue-800 space-y-1",children:[(0,a.jsx)("li",{children:"• Focus on Machine Learning Fundamentals for comprehensive coverage"}),(0,a.jsx)("li",{children:"• Include practical examples for Neural Networks concepts"}),(0,a.jsx)("li",{children:"• Consider mixed difficulty levels for better assessment"}),(0,a.jsx)("li",{children:"• Add scenario-based questions for real-world application"})]})]})]})]})}let w=(0,n.A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]);var A=t(7550);function M(){let e=(0,r.useRouter)(),[s,t]=(0,l.useState)(null),[i,n]=(0,l.useState)(!0),[c,d]=(0,l.useState)(!1);return((0,l.useEffect)(()=>{let s=localStorage.getItem("uploadedPDF");s?(t(JSON.parse(s)),setTimeout(()=>{n(!1),d(!0)},3e3)):e.push("/")},[e]),s)?(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,a.jsx)("div",{className:"bg-white border-b border-gray-200",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,a.jsx)("div",{className:"flex items-center justify-between",children:(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("button",{onClick:()=>{localStorage.removeItem("uploadedPDF"),e.push("/")},className:"flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors",children:[(0,a.jsx)(A.A,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:"Back to Home"})]}),(0,a.jsx)("div",{className:"h-6 w-px bg-gray-300"}),(0,a.jsx)("h1",{className:"text-xl font-semibold text-gray-900",children:"PDF Analysis & Exam Configuration"})]})})})}),(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:i?(0,a.jsxs)("div",{className:"text-center py-16",children:[(0,a.jsx)("div",{className:"inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-6",children:(0,a.jsx)(w,{className:"w-8 h-8 animate-spin text-blue-600"})}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Analyzing Your PDF"}),(0,a.jsx)("p",{className:"text-gray-600 mb-8 max-w-md mx-auto",children:"Our AI is reading through your document and identifying key topics and concepts that can be turned into exam questions."}),(0,a.jsxs)("div",{className:"max-w-md mx-auto",children:[(0,a.jsx)("div",{className:"bg-gray-200 rounded-full h-2 mb-4",children:(0,a.jsx)("div",{className:"bg-blue-600 h-2 rounded-full animate-pulse",style:{width:"75%"}})}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["Processing: ",s.name]})]})]}):(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)(m,{pdfInfo:s}),(0,a.jsx)(k,{})]}),(0,a.jsx)("div",{children:(0,a.jsx)(j,{})})]})})]}):(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(w,{className:"w-8 h-8 animate-spin mx-auto mb-4 text-blue-600"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Loading..."})]})})}},646:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},4186:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},4869:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},5040:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},5695:(e,s,t)=>{"use strict";var a=t(8999);t.o(a,"usePathname")&&t.d(s,{usePathname:function(){return a.usePathname}}),t.o(a,"useRouter")&&t.d(s,{useRouter:function(){return a.useRouter}})},6099:(e,s,t)=>{Promise.resolve().then(t.bind(t,537))},6785:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},7434:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},7550:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},9074:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},9376:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]])},9946:(e,s,t)=>{"use strict";t.d(s,{A:()=>m});var a=t(2115);let l=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),r=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,s,t)=>t?t.toUpperCase():s.toLowerCase()),i=e=>{let s=r(e);return s.charAt(0).toUpperCase()+s.slice(1)},n=function(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return s.filter((e,s,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===s).join(" ").trim()},c=e=>{for(let s in e)if(s.startsWith("aria-")||"role"===s||"title"===s)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let x=(0,a.forwardRef)((e,s)=>{let{color:t="currentColor",size:l=24,strokeWidth:r=2,absoluteStrokeWidth:i,className:x="",children:m,iconNode:o,...h}=e;return(0,a.createElement)("svg",{ref:s,...d,width:l,height:l,stroke:t,strokeWidth:i?24*Number(r)/Number(l):r,className:n("lucide",x),...!m&&!c(h)&&{"aria-hidden":"true"},...h},[...o.map(e=>{let[s,t]=e;return(0,a.createElement)(s,t)}),...Array.isArray(m)?m:[m]])}),m=(e,s)=>{let t=(0,a.forwardRef)((t,r)=>{let{className:c,...d}=t;return(0,a.createElement)(x,{ref:r,iconNode:s,className:n("lucide-".concat(l(i(e))),"lucide-".concat(e),c),...d})});return t.displayName=i(e),t}}},e=>{var s=s=>e(e.s=s);e.O(0,[441,684,358],()=>s(6099)),_N_E=e.O()}]);