'use client';

import { useRouter } from 'next/navigation';
import { useLanguage } from '@/contexts/LanguageContext';
import { 
  Play, 
  Star, 
  Users, 
  FileText, 
  Zap, 
  BarChart3,
  ArrowLeft,
  ArrowRight,
  CheckCircle
} from 'lucide-react';

export function Hero() {
  const { language, t } = useLanguage();
  const router = useRouter();

  const handleGetStarted = () => {
    router.push('/auth/register');
  };

  const handleDemo = () => {
    // Scroll to features section or show demo
    document.getElementById('features')?.scrollIntoView({ behavior: 'smooth' });
  };

  return (
    <section className="relative bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 py-20 lg:py-32 overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-32 w-80 h-80 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob"></div>
        <div className="absolute -bottom-40 -left-32 w-80 h-80 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000"></div>
        <div className="absolute top-40 left-40 w-80 h-80 bg-gradient-to-r from-yellow-400 to-red-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-4000"></div>
      </div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <div className="text-center lg:text-right rtl:lg:text-left">
            {/* Trust Badge */}
            <div className="inline-flex items-center space-x-2 rtl:space-x-reverse bg-white dark:bg-gray-800 rounded-full px-4 py-2 shadow-sm border border-gray-200 dark:border-gray-700 mb-8">
              <div className="flex items-center space-x-1 rtl:space-x-reverse">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                ))}
              </div>
              <span className="text-sm text-gray-600 dark:text-gray-300">
                {t('hero.trusted')}
              </span>
            </div>

            {/* Main Heading */}
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 dark:text-white mb-6 leading-tight">
              {t('hero.title')}
              <span className="block text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600">
                {t('hero.subtitle')}
              </span>
            </h1>

            {/* Description */}
            <p className="text-xl text-gray-600 dark:text-gray-300 mb-8 leading-relaxed max-w-2xl mx-auto lg:mx-0">
              {t('hero.description')}
            </p>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row items-center justify-center lg:justify-start rtl:lg:justify-end space-y-4 sm:space-y-0 sm:space-x-4 rtl:space-x-reverse mb-12">
              <button 
                onClick={handleGetStarted}
                className="w-full sm:w-auto bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-xl font-semibold text-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl"
              >
                {t('hero.cta.primary')}
                {language === 'ar' ? (
                  <ArrowLeft className="w-5 h-5 mr-2 inline" />
                ) : (
                  <ArrowRight className="w-5 h-5 ml-2 inline" />
                )}
              </button>
              
              <button 
                onClick={handleDemo}
                className="w-full sm:w-auto flex items-center justify-center space-x-2 rtl:space-x-reverse text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors"
              >
                <Play className="w-5 h-5" />
                <span className="font-medium">{t('hero.cta.secondary')}</span>
              </button>
            </div>

            {/* Features Preview */}
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-6">
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                  <Zap className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                </div>
                <div className="text-right rtl:text-left">
                  <div className="font-semibold text-gray-900 dark:text-white">AI-Powered</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Smart Generation</div>
                </div>
              </div>

              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <div className="w-10 h-10 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center">
                  <BarChart3 className="w-5 h-5 text-purple-600 dark:text-purple-400" />
                </div>
                <div className="text-right rtl:text-left">
                  <div className="font-semibold text-gray-900 dark:text-white">Analytics</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Deep Insights</div>
                </div>
              </div>

              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <div className="w-10 h-10 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
                  <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400" />
                </div>
                <div className="text-right rtl:text-left">
                  <div className="font-semibold text-gray-900 dark:text-white">Instant</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Real-time Results</div>
                </div>
              </div>
            </div>
          </div>

          {/* Visual */}
          <div className="relative">
            {/* Main Dashboard Preview */}
            <div className="relative bg-white dark:bg-gray-800 rounded-2xl shadow-2xl border border-gray-200 dark:border-gray-700 overflow-hidden">
              {/* Header */}
              <div className="bg-gray-50 dark:bg-gray-900 px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <div className="flex items-center space-x-3 rtl:space-x-reverse">
                  <div className="flex space-x-2 rtl:space-x-reverse">
                    <div className="w-3 h-3 bg-red-400 rounded-full"></div>
                    <div className="w-3 h-3 bg-yellow-400 rounded-full"></div>
                    <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">ExamAI Dashboard</div>
                </div>
              </div>

              {/* Content */}
              <div className="p-6">
                {/* Upload Area */}
                <div className="border-2 border-dashed border-blue-300 dark:border-blue-600 rounded-lg p-8 text-center mb-6">
                  <FileText className="w-12 h-12 text-blue-600 dark:text-blue-400 mx-auto mb-4" />
                  <div className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    Upload PDF Document
                  </div>
                  <div className="text-gray-600 dark:text-gray-400">
                    Drag & drop or click to select
                  </div>
                </div>

                {/* Stats */}
                <div className="grid grid-cols-3 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">1,234</div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">Exams Created</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">98%</div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">Accuracy</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600 dark:text-green-400">5min</div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">Avg Time</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Floating Elements */}
            <div className="absolute -top-4 -right-4 bg-gradient-to-r from-blue-500 to-purple-500 text-white px-4 py-2 rounded-lg shadow-lg">
              <div className="flex items-center space-x-2 rtl:space-x-reverse">
                <Users className="w-4 h-4" />
                <span className="text-sm font-medium">10K+ Users</span>
              </div>
            </div>

            <div className="absolute -bottom-4 -left-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 px-4 py-2 rounded-lg shadow-lg">
              <div className="flex items-center space-x-2 rtl:space-x-reverse">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <span className="text-sm text-gray-600 dark:text-gray-400">Live Processing</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
