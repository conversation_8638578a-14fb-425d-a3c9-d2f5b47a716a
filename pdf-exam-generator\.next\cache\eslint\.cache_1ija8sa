[{"D:\\templete\\pdf-exam-generator\\src\\app\\analyze\\page.tsx": "1", "D:\\templete\\pdf-exam-generator\\src\\app\\auth\\forgot-password\\page.tsx": "2", "D:\\templete\\pdf-exam-generator\\src\\app\\auth\\login\\page.tsx": "3", "D:\\templete\\pdf-exam-generator\\src\\app\\auth\\register\\page.tsx": "4", "D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\analytics\\page.tsx": "5", "D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\exams\\page.tsx": "6", "D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\history\\page.tsx": "7", "D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\page.tsx": "8", "D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\students\\page.tsx": "9", "D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\upload\\page.tsx": "10", "D:\\templete\\pdf-exam-generator\\src\\app\\exam\\page.tsx": "11", "D:\\templete\\pdf-exam-generator\\src\\app\\layout.tsx": "12", "D:\\templete\\pdf-exam-generator\\src\\app\\page.tsx": "13", "D:\\templete\\pdf-exam-generator\\src\\app\\results\\page.tsx": "14", "D:\\templete\\pdf-exam-generator\\src\\app\\settings\\page.tsx": "15", "D:\\templete\\pdf-exam-generator\\src\\components\\ai\\AIAnalysisSummary.tsx": "16", "D:\\templete\\pdf-exam-generator\\src\\components\\auth\\LoginPage.tsx": "17", "D:\\templete\\pdf-exam-generator\\src\\components\\auth\\ProtectedRoute.tsx": "18", "D:\\templete\\pdf-exam-generator\\src\\components\\exam\\ExamConfiguration.tsx": "19", "D:\\templete\\pdf-exam-generator\\src\\components\\exam\\ExamInterface.tsx": "20", "D:\\templete\\pdf-exam-generator\\src\\components\\exam\\ExamProgress.tsx": "21", "D:\\templete\\pdf-exam-generator\\src\\components\\exam\\ExamTimer.tsx": "22", "D:\\templete\\pdf-exam-generator\\src\\components\\layout\\AppContent.tsx": "23", "D:\\templete\\pdf-exam-generator\\src\\components\\layout\\DashboardLayout.tsx": "24", "D:\\templete\\pdf-exam-generator\\src\\components\\layout\\Footer.tsx": "25", "D:\\templete\\pdf-exam-generator\\src\\components\\layout\\Navbar.tsx": "26", "D:\\templete\\pdf-exam-generator\\src\\components\\pages\\LandingPage.tsx": "27", "D:\\templete\\pdf-exam-generator\\src\\components\\pdf\\PDFPreview.tsx": "28", "D:\\templete\\pdf-exam-generator\\src\\components\\results\\ExportResults.tsx": "29", "D:\\templete\\pdf-exam-generator\\src\\components\\results\\PerformanceAnalytics.tsx": "30", "D:\\templete\\pdf-exam-generator\\src\\components\\results\\QuestionReview.tsx": "31", "D:\\templete\\pdf-exam-generator\\src\\components\\results\\ScoreOverview.tsx": "32", "D:\\templete\\pdf-exam-generator\\src\\components\\sections\\CTASection.tsx": "33", "D:\\templete\\pdf-exam-generator\\src\\components\\sections\\FeaturesSection.tsx": "34", "D:\\templete\\pdf-exam-generator\\src\\components\\sections\\HeroSection.tsx": "35", "D:\\templete\\pdf-exam-generator\\src\\components\\sections\\HowItWorksSection.tsx": "36", "D:\\templete\\pdf-exam-generator\\src\\components\\ui\\FileUpload.tsx": "37", "D:\\templete\\pdf-exam-generator\\src\\components\\ui\\LoadingSpinner.tsx": "38", "D:\\templete\\pdf-exam-generator\\src\\components\\upload\\FileUpload.tsx": "39", "D:\\templete\\pdf-exam-generator\\src\\contexts\\AuthContext.tsx": "40", "D:\\templete\\pdf-exam-generator\\src\\lib\\firebase.ts": "41", "D:\\templete\\pdf-exam-generator\\src\\middleware.ts": "42", "D:\\templete\\pdf-exam-generator\\src\\app\\profile\\page.tsx": "43"}, {"size": 3933, "mtime": 1751824179989, "results": "44", "hashOfConfig": "45"}, {"size": 6468, "mtime": 1751836535258, "results": "46", "hashOfConfig": "45"}, {"size": 10584, "mtime": 1751836745010, "results": "47", "hashOfConfig": "45"}, {"size": 14173, "mtime": 1751836616731, "results": "48", "hashOfConfig": "45"}, {"size": 11668, "mtime": 1751836878761, "results": "49", "hashOfConfig": "45"}, {"size": 12714, "mtime": 1751826404711, "results": "50", "hashOfConfig": "45"}, {"size": 15943, "mtime": 1751825955884, "results": "51", "hashOfConfig": "45"}, {"size": 8118, "mtime": 1751837014821, "results": "52", "hashOfConfig": "45"}, {"size": 13198, "mtime": 1751825895760, "results": "53", "hashOfConfig": "45"}, {"size": 7393, "mtime": 1751825625878, "results": "54", "hashOfConfig": "45"}, {"size": 10615, "mtime": 1751837126808, "results": "55", "hashOfConfig": "45"}, {"size": 1111, "mtime": 1751826329741, "results": "56", "hashOfConfig": "45"}, {"size": 785, "mtime": 1751834374493, "results": "57", "hashOfConfig": "45"}, {"size": 7079, "mtime": 1751824685509, "results": "58", "hashOfConfig": "45"}, {"size": 11563, "mtime": 1751835344506, "results": "59", "hashOfConfig": "45"}, {"size": 5989, "mtime": 1751824246344, "results": "60", "hashOfConfig": "45"}, {"size": 8875, "mtime": 1751825402790, "results": "61", "hashOfConfig": "45"}, {"size": 1531, "mtime": 1751834337572, "results": "62", "hashOfConfig": "45"}, {"size": 9172, "mtime": 1751824353308, "results": "63", "hashOfConfig": "45"}, {"size": 10400, "mtime": 1751824398796, "results": "64", "hashOfConfig": "45"}, {"size": 1057, "mtime": 1751824438721, "results": "65", "hashOfConfig": "45"}, {"size": 2486, "mtime": 1751824425298, "results": "66", "hashOfConfig": "45"}, {"size": 1998, "mtime": 1751834607725, "results": "67", "hashOfConfig": "45"}, {"size": 8133, "mtime": 1751837348136, "results": "68", "hashOfConfig": "45"}, {"size": 4796, "mtime": 1751823938057, "results": "69", "hashOfConfig": "45"}, {"size": 9173, "mtime": 1751837428591, "results": "70", "hashOfConfig": "45"}, {"size": 472, "mtime": 1751825417897, "results": "71", "hashOfConfig": "45"}, {"size": 5052, "mtime": 1751824216897, "results": "72", "hashOfConfig": "45"}, {"size": 5353, "mtime": 1751824832012, "results": "73", "hashOfConfig": "45"}, {"size": 9781, "mtime": 1751824802502, "results": "74", "hashOfConfig": "45"}, {"size": 8496, "mtime": 1751824761830, "results": "75", "hashOfConfig": "45"}, {"size": 8044, "mtime": 1751824724277, "results": "76", "hashOfConfig": "45"}, {"size": 5810, "mtime": 1751825476712, "results": "77", "hashOfConfig": "45"}, {"size": 4373, "mtime": 1751825446906, "results": "78", "hashOfConfig": "45"}, {"size": 5974, "mtime": 1751834447656, "results": "79", "hashOfConfig": "45"}, {"size": 5808, "mtime": 1751824124682, "results": "80", "hashOfConfig": "45"}, {"size": 6941, "mtime": 1751824064879, "results": "81", "hashOfConfig": "45"}, {"size": 1023, "mtime": 1751834350798, "results": "82", "hashOfConfig": "45"}, {"size": 4227, "mtime": 1751826451060, "results": "83", "hashOfConfig": "45"}, {"size": 7433, "mtime": 1751834115990, "results": "84", "hashOfConfig": "45"}, {"size": 1381, "mtime": 1751833891312, "results": "85", "hashOfConfig": "45"}, {"size": 2401, "mtime": 1751834323520, "results": "86", "hashOfConfig": "45"}, {"size": 11221, "mtime": 1751837190476, "results": "87", "hashOfConfig": "45"}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "1pxi7og", {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\templete\\pdf-exam-generator\\src\\app\\analyze\\page.tsx", ["217"], [], "D:\\templete\\pdf-exam-generator\\src\\app\\auth\\forgot-password\\page.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\app\\auth\\login\\page.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\app\\auth\\register\\page.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\analytics\\page.tsx", ["218", "219", "220"], [], "D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\exams\\page.tsx", ["221", "222", "223", "224"], [], "D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\history\\page.tsx", ["225", "226"], [], "D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\page.tsx", ["227", "228"], [], "D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\students\\page.tsx", ["229", "230", "231", "232"], [], "D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\upload\\page.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\app\\exam\\page.tsx", ["233", "234", "235", "236", "237"], [], "D:\\templete\\pdf-exam-generator\\src\\app\\layout.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\app\\page.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\app\\results\\page.tsx", ["238", "239", "240", "241"], [], "D:\\templete\\pdf-exam-generator\\src\\app\\settings\\page.tsx", ["242"], [], "D:\\templete\\pdf-exam-generator\\src\\components\\ai\\AIAnalysisSummary.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\components\\auth\\LoginPage.tsx", ["243"], [], "D:\\templete\\pdf-exam-generator\\src\\components\\auth\\ProtectedRoute.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\components\\exam\\ExamConfiguration.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\components\\exam\\ExamInterface.tsx", ["244", "245", "246", "247"], [], "D:\\templete\\pdf-exam-generator\\src\\components\\exam\\ExamProgress.tsx", ["248"], [], "D:\\templete\\pdf-exam-generator\\src\\components\\exam\\ExamTimer.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\components\\layout\\AppContent.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\components\\layout\\DashboardLayout.tsx", ["249", "250", "251", "252"], [], "D:\\templete\\pdf-exam-generator\\src\\components\\layout\\Footer.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\components\\layout\\Navbar.tsx", ["253", "254"], [], "D:\\templete\\pdf-exam-generator\\src\\components\\pages\\LandingPage.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\components\\pdf\\PDFPreview.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\components\\results\\ExportResults.tsx", ["255", "256"], [], "D:\\templete\\pdf-exam-generator\\src\\components\\results\\PerformanceAnalytics.tsx", ["257", "258", "259"], [], "D:\\templete\\pdf-exam-generator\\src\\components\\results\\QuestionReview.tsx", ["260", "261", "262", "263"], [], "D:\\templete\\pdf-exam-generator\\src\\components\\results\\ScoreOverview.tsx", ["264", "265"], [], "D:\\templete\\pdf-exam-generator\\src\\components\\sections\\CTASection.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\components\\sections\\FeaturesSection.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\components\\sections\\HeroSection.tsx", ["266"], [], "D:\\templete\\pdf-exam-generator\\src\\components\\sections\\HowItWorksSection.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\components\\ui\\FileUpload.tsx", ["267"], [], "D:\\templete\\pdf-exam-generator\\src\\components\\ui\\LoadingSpinner.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\components\\upload\\FileUpload.tsx", ["268"], [], "D:\\templete\\pdf-exam-generator\\src\\contexts\\AuthContext.tsx", ["269", "270", "271", "272"], [], "D:\\templete\\pdf-exam-generator\\src\\lib\\firebase.ts", [], [], "D:\\templete\\pdf-exam-generator\\src\\middleware.ts", [], [], "D:\\templete\\pdf-exam-generator\\src\\app\\profile\\page.tsx", ["273", "274"], [], {"ruleId": "275", "severity": 1, "message": "276", "line": 21, "column": 10, "nodeType": null, "messageId": "277", "endLine": 21, "endColumn": 26}, {"ruleId": "275", "severity": 1, "message": "278", "line": 8, "column": 3, "nodeType": null, "messageId": "277", "endLine": 8, "endColumn": 8}, {"ruleId": "275", "severity": 1, "message": "279", "line": 12, "column": 3, "nodeType": null, "messageId": "277", "endLine": 12, "endColumn": 11}, {"ruleId": "275", "severity": 1, "message": "280", "line": 20, "column": 11, "nodeType": null, "messageId": "277", "endLine": 20, "endColumn": 15}, {"ruleId": "275", "severity": 1, "message": "281", "line": 8, "column": 3, "nodeType": null, "messageId": "277", "endLine": 8, "endColumn": 9}, {"ruleId": "275", "severity": 1, "message": "282", "line": 12, "column": 3, "nodeType": null, "messageId": "277", "endLine": 12, "endColumn": 8}, {"ruleId": "275", "severity": 1, "message": "283", "line": 17, "column": 3, "nodeType": null, "messageId": "277", "endLine": 17, "endColumn": 9}, {"ruleId": "275", "severity": 1, "message": "280", "line": 25, "column": 11, "nodeType": null, "messageId": "277", "endLine": 25, "endColumn": 15}, {"ruleId": "275", "severity": 1, "message": "281", "line": 11, "column": 3, "nodeType": null, "messageId": "277", "endLine": 11, "endColumn": 9}, {"ruleId": "275", "severity": 1, "message": "280", "line": 23, "column": 11, "nodeType": null, "messageId": "277", "endLine": 23, "endColumn": 15}, {"ruleId": "275", "severity": 1, "message": "284", "line": 8, "column": 3, "nodeType": null, "messageId": "277", "endLine": 8, "endColumn": 13}, {"ruleId": "275", "severity": 1, "message": "279", "line": 13, "column": 3, "nodeType": null, "messageId": "277", "endLine": 13, "endColumn": 11}, {"ruleId": "275", "severity": 1, "message": "281", "line": 8, "column": 3, "nodeType": null, "messageId": "277", "endLine": 8, "endColumn": 9}, {"ruleId": "275", "severity": 1, "message": "285", "line": 9, "column": 3, "nodeType": null, "messageId": "277", "endLine": 9, "endColumn": 7}, {"ruleId": "275", "severity": 1, "message": "279", "line": 13, "column": 3, "nodeType": null, "messageId": "277", "endLine": 13, "endColumn": 11}, {"ruleId": "275", "severity": 1, "message": "280", "line": 24, "column": 11, "nodeType": null, "messageId": "277", "endLine": 24, "endColumn": 15}, {"ruleId": "275", "severity": 1, "message": "286", "line": 8, "column": 21, "nodeType": null, "messageId": "277", "endLine": 8, "endColumn": 25}, {"ruleId": "275", "severity": 1, "message": "287", "line": 8, "column": 27, "nodeType": null, "messageId": "277", "endLine": 8, "endColumn": 31}, {"ruleId": "288", "severity": 1, "message": "289", "line": 63, "column": 48, "nodeType": "290", "messageId": "291", "endLine": 63, "endColumn": 51, "suggestions": "292"}, {"ruleId": "288", "severity": 1, "message": "289", "line": 65, "column": 57, "nodeType": "290", "messageId": "291", "endLine": 65, "endColumn": 60, "suggestions": "293"}, {"ruleId": "288", "severity": 1, "message": "289", "line": 97, "column": 59, "nodeType": "290", "messageId": "291", "endLine": 97, "endColumn": 62, "suggestions": "294"}, {"ruleId": "275", "severity": 1, "message": "295", "line": 9, "column": 32, "nodeType": null, "messageId": "277", "endLine": 9, "endColumn": 38}, {"ruleId": "288", "severity": 1, "message": "289", "line": 12, "column": 27, "nodeType": "290", "messageId": "291", "endLine": 12, "endColumn": 30, "suggestions": "296"}, {"ruleId": "288", "severity": 1, "message": "289", "line": 13, "column": 14, "nodeType": "290", "messageId": "291", "endLine": 13, "endColumn": 17, "suggestions": "297"}, {"ruleId": "288", "severity": 1, "message": "289", "line": 14, "column": 11, "nodeType": "290", "messageId": "291", "endLine": 14, "endColumn": 14, "suggestions": "298"}, {"ruleId": "275", "severity": 1, "message": "299", "line": 6, "column": 47, "nodeType": null, "messageId": "277", "endLine": 6, "endColumn": 54}, {"ruleId": "275", "severity": 1, "message": "300", "line": 31, "column": 14, "nodeType": null, "messageId": "277", "endLine": 31, "endColumn": 17}, {"ruleId": "275", "severity": 1, "message": "301", "line": 4, "column": 56, "nodeType": null, "messageId": "277", "endLine": 4, "endColumn": 62}, {"ruleId": "288", "severity": 1, "message": "289", "line": 11, "column": 18, "nodeType": "290", "messageId": "291", "endLine": 11, "endColumn": 21, "suggestions": "302"}, {"ruleId": "288", "severity": 1, "message": "289", "line": 18, "column": 27, "nodeType": "290", "messageId": "291", "endLine": 18, "endColumn": 30, "suggestions": "303"}, {"ruleId": "288", "severity": 1, "message": "289", "line": 19, "column": 48, "nodeType": "290", "messageId": "291", "endLine": 19, "endColumn": 51, "suggestions": "304"}, {"ruleId": "275", "severity": 1, "message": "305", "line": 1, "column": 10, "nodeType": null, "messageId": "277", "endLine": 1, "endColumn": 21}, {"ruleId": "275", "severity": 1, "message": "306", "line": 9, "column": 3, "nodeType": null, "messageId": "277", "endLine": 9, "endColumn": 11}, {"ruleId": "275", "severity": 1, "message": "307", "line": 22, "column": 3, "nodeType": null, "messageId": "277", "endLine": 22, "endColumn": 8}, {"ruleId": "308", "severity": 1, "message": "309", "line": 81, "column": 17, "nodeType": "310", "endLine": 81, "endColumn": 126}, {"ruleId": "308", "severity": 1, "message": "309", "line": 190, "column": 21, "nodeType": "310", "endLine": 190, "endColumn": 128}, {"ruleId": "308", "severity": 1, "message": "309", "line": 84, "column": 23, "nodeType": "310", "endLine": 84, "endColumn": 130}, {"ruleId": "308", "severity": 1, "message": "309", "line": 175, "column": 23, "nodeType": "310", "endLine": 175, "endColumn": 130}, {"ruleId": "288", "severity": 1, "message": "289", "line": 4, "column": 16, "nodeType": "290", "messageId": "291", "endLine": 4, "endColumn": 19, "suggestions": "311"}, {"ruleId": "288", "severity": 1, "message": "289", "line": 24, "column": 33, "nodeType": "290", "messageId": "291", "endLine": 24, "endColumn": 36, "suggestions": "312"}, {"ruleId": "275", "severity": 1, "message": "282", "line": 1, "column": 41, "nodeType": null, "messageId": "277", "endLine": 1, "endColumn": 46}, {"ruleId": "288", "severity": 1, "message": "289", "line": 4, "column": 14, "nodeType": "290", "messageId": "291", "endLine": 4, "endColumn": 17, "suggestions": "313"}, {"ruleId": "288", "severity": 1, "message": "289", "line": 5, "column": 27, "nodeType": "290", "messageId": "291", "endLine": 5, "endColumn": 30, "suggestions": "314"}, {"ruleId": "288", "severity": 1, "message": "289", "line": 8, "column": 18, "nodeType": "290", "messageId": "291", "endLine": 8, "endColumn": 21, "suggestions": "315"}, {"ruleId": "288", "severity": 1, "message": "289", "line": 14, "column": 27, "nodeType": "290", "messageId": "291", "endLine": 14, "endColumn": 30, "suggestions": "316"}, {"ruleId": "288", "severity": 1, "message": "289", "line": 18, "column": 54, "nodeType": "290", "messageId": "291", "endLine": 18, "endColumn": 57, "suggestions": "317"}, {"ruleId": "288", "severity": 1, "message": "289", "line": 31, "column": 61, "nodeType": "290", "messageId": "291", "endLine": 31, "endColumn": 64, "suggestions": "318"}, {"ruleId": "288", "severity": 1, "message": "289", "line": 8, "column": 11, "nodeType": "290", "messageId": "291", "endLine": 8, "endColumn": 14, "suggestions": "319"}, {"ruleId": "320", "severity": 1, "message": "321", "line": 57, "column": 51, "nodeType": "322", "messageId": "323", "suggestions": "324"}, {"ruleId": "275", "severity": 1, "message": "325", "line": 11, "column": 10, "nodeType": null, "messageId": "277", "endLine": 11, "endColumn": 22}, {"ruleId": "288", "severity": 1, "message": "289", "line": 19, "column": 69, "nodeType": "290", "messageId": "291", "endLine": 19, "endColumn": 72, "suggestions": "326"}, {"ruleId": "288", "severity": 1, "message": "289", "line": 16, "column": 69, "nodeType": "290", "messageId": "291", "endLine": 16, "endColumn": 72, "suggestions": "327"}, {"ruleId": "275", "severity": 1, "message": "328", "line": 13, "column": 3, "nodeType": null, "messageId": "277", "endLine": 13, "endColumn": 21}, {"ruleId": "275", "severity": 1, "message": "329", "line": 14, "column": 3, "nodeType": null, "messageId": "277", "endLine": 14, "endColumn": 23}, {"ruleId": "288", "severity": 1, "message": "289", "line": 72, "column": 72, "nodeType": "290", "messageId": "291", "endLine": 72, "endColumn": 75, "suggestions": "330"}, {"ruleId": "275", "severity": 1, "message": "331", "line": 142, "column": 14, "nodeType": null, "messageId": "277", "endLine": 142, "endColumn": 19}, {"ruleId": "275", "severity": 1, "message": "280", "line": 9, "column": 43, "nodeType": null, "messageId": "277", "endLine": 9, "endColumn": 47}, {"ruleId": "308", "severity": 1, "message": "309", "line": 79, "column": 23, "nodeType": "310", "endLine": 83, "endColumn": 25}, "@typescript-eslint/no-unused-vars", "'analysisComplete' is assigned a value but never used.", "unusedVar", "'Users' is defined but never used.", "'Calendar' is defined but never used.", "'user' is assigned a value but never used.", "'Filter' is defined but never used.", "'Clock' is defined but never used.", "'Trash2' is defined but never used.", "'TrendingUp' is defined but never used.", "'Plus' is defined but never used.", "'Save' is defined but never used.", "'Flag' is defined but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["332", "333"], ["334", "335"], ["336", "337"], "'Share2' is defined but never used.", ["338", "339"], ["340", "341"], ["342", "343"], "'Palette' is defined but never used.", "'err' is defined but never used.", "'Circle' is defined but never used.", ["344", "345"], ["346", "347"], ["348", "349"], "'CheckCircle' is defined but never used.", "'FileText' is defined but never used.", "'Award' is defined but never used.", "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", ["350", "351"], ["352", "353"], ["354", "355"], ["356", "357"], ["358", "359"], ["360", "361"], ["362", "363"], ["364", "365"], ["366", "367"], "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["368", "369", "370", "371"], "'uploadedFile' is assigned a value but never used.", ["372", "373"], ["374", "375"], "'GoogleAuthProvider' is defined but never used.", "'FacebookAuthProvider' is defined but never used.", ["376", "377"], "'error' is defined but never used.", {"messageId": "378", "fix": "379", "desc": "380"}, {"messageId": "381", "fix": "382", "desc": "383"}, {"messageId": "378", "fix": "384", "desc": "380"}, {"messageId": "381", "fix": "385", "desc": "383"}, {"messageId": "378", "fix": "386", "desc": "380"}, {"messageId": "381", "fix": "387", "desc": "383"}, {"messageId": "378", "fix": "388", "desc": "380"}, {"messageId": "381", "fix": "389", "desc": "383"}, {"messageId": "378", "fix": "390", "desc": "380"}, {"messageId": "381", "fix": "391", "desc": "383"}, {"messageId": "378", "fix": "392", "desc": "380"}, {"messageId": "381", "fix": "393", "desc": "383"}, {"messageId": "378", "fix": "394", "desc": "380"}, {"messageId": "381", "fix": "395", "desc": "383"}, {"messageId": "378", "fix": "396", "desc": "380"}, {"messageId": "381", "fix": "397", "desc": "383"}, {"messageId": "378", "fix": "398", "desc": "380"}, {"messageId": "381", "fix": "399", "desc": "383"}, {"messageId": "378", "fix": "400", "desc": "380"}, {"messageId": "381", "fix": "401", "desc": "383"}, {"messageId": "378", "fix": "402", "desc": "380"}, {"messageId": "381", "fix": "403", "desc": "383"}, {"messageId": "378", "fix": "404", "desc": "380"}, {"messageId": "381", "fix": "405", "desc": "383"}, {"messageId": "378", "fix": "406", "desc": "380"}, {"messageId": "381", "fix": "407", "desc": "383"}, {"messageId": "378", "fix": "408", "desc": "380"}, {"messageId": "381", "fix": "409", "desc": "383"}, {"messageId": "378", "fix": "410", "desc": "380"}, {"messageId": "381", "fix": "411", "desc": "383"}, {"messageId": "378", "fix": "412", "desc": "380"}, {"messageId": "381", "fix": "413", "desc": "383"}, {"messageId": "378", "fix": "414", "desc": "380"}, {"messageId": "381", "fix": "415", "desc": "383"}, {"messageId": "378", "fix": "416", "desc": "380"}, {"messageId": "381", "fix": "417", "desc": "383"}, {"messageId": "418", "data": "419", "fix": "420", "desc": "421"}, {"messageId": "418", "data": "422", "fix": "423", "desc": "424"}, {"messageId": "418", "data": "425", "fix": "426", "desc": "427"}, {"messageId": "418", "data": "428", "fix": "429", "desc": "430"}, {"messageId": "378", "fix": "431", "desc": "380"}, {"messageId": "381", "fix": "432", "desc": "383"}, {"messageId": "378", "fix": "433", "desc": "380"}, {"messageId": "381", "fix": "434", "desc": "383"}, {"messageId": "378", "fix": "435", "desc": "380"}, {"messageId": "381", "fix": "436", "desc": "383"}, "suggestUnknown", {"range": "437", "text": "438"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "439", "text": "440"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "441", "text": "438"}, {"range": "442", "text": "440"}, {"range": "443", "text": "438"}, {"range": "444", "text": "440"}, {"range": "445", "text": "438"}, {"range": "446", "text": "440"}, {"range": "447", "text": "438"}, {"range": "448", "text": "440"}, {"range": "449", "text": "438"}, {"range": "450", "text": "440"}, {"range": "451", "text": "438"}, {"range": "452", "text": "440"}, {"range": "453", "text": "438"}, {"range": "454", "text": "440"}, {"range": "455", "text": "438"}, {"range": "456", "text": "440"}, {"range": "457", "text": "438"}, {"range": "458", "text": "440"}, {"range": "459", "text": "438"}, {"range": "460", "text": "440"}, {"range": "461", "text": "438"}, {"range": "462", "text": "440"}, {"range": "463", "text": "438"}, {"range": "464", "text": "440"}, {"range": "465", "text": "438"}, {"range": "466", "text": "440"}, {"range": "467", "text": "438"}, {"range": "468", "text": "440"}, {"range": "469", "text": "438"}, {"range": "470", "text": "440"}, {"range": "471", "text": "438"}, {"range": "472", "text": "440"}, {"range": "473", "text": "438"}, {"range": "474", "text": "440"}, "replaceWithAlt", {"alt": "475"}, {"range": "476", "text": "477"}, "Replace with `&apos;`.", {"alt": "478"}, {"range": "479", "text": "480"}, "Replace with `&lsquo;`.", {"alt": "481"}, {"range": "482", "text": "483"}, "Replace with `&#39;`.", {"alt": "484"}, {"range": "485", "text": "486"}, "Replace with `&rsquo;`.", {"range": "487", "text": "438"}, {"range": "488", "text": "440"}, {"range": "489", "text": "438"}, {"range": "490", "text": "440"}, {"range": "491", "text": "438"}, {"range": "492", "text": "440"}, [2805, 2808], "unknown", [2805, 2808], "never", [2934, 2937], [2934, 2937], [3947, 3950], [3947, 3950], [505, 508], [505, 508], [524, 527], [524, 527], [541, 544], [541, 544], [303, 306], [303, 306], [443, 446], [443, 446], [496, 499], [496, 499], [112, 115], [112, 115], [691, 694], [691, 694], [121, 124], [121, 124], [154, 157], [154, 157], [240, 243], [240, 243], [354, 357], [354, 357], [494, 497], [494, 497], [1071, 1074], [1071, 1074], [207, 210], [207, 210], "&apos;", [2011, 2048], "Here&apos;s how you performed on your exam", "&lsquo;", [2011, 2048], "Here&lsquo;s how you performed on your exam", "&#39;", [2011, 2048], "Here&#39;s how you performed on your exam", "&rsquo;", [2011, 2048], "Here&rsquo;s how you performed on your exam", [760, 763], [760, 763], [522, 525], [522, 525], [2229, 2232], [2229, 2232]]