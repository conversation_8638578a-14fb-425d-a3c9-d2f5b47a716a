[{"D:\\templete\\pdf-exam-generator\\src\\app\\analyze\\page.tsx": "1", "D:\\templete\\pdf-exam-generator\\src\\app\\auth\\forgot-password\\page.tsx": "2", "D:\\templete\\pdf-exam-generator\\src\\app\\auth\\login\\page.tsx": "3", "D:\\templete\\pdf-exam-generator\\src\\app\\auth\\register\\page.tsx": "4", "D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\analytics\\page.tsx": "5", "D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\exams\\page.tsx": "6", "D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\history\\page.tsx": "7", "D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\page.tsx": "8", "D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\students\\page.tsx": "9", "D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\upload\\page.tsx": "10", "D:\\templete\\pdf-exam-generator\\src\\app\\exam\\page.tsx": "11", "D:\\templete\\pdf-exam-generator\\src\\app\\layout.tsx": "12", "D:\\templete\\pdf-exam-generator\\src\\app\\page.tsx": "13", "D:\\templete\\pdf-exam-generator\\src\\app\\results\\page.tsx": "14", "D:\\templete\\pdf-exam-generator\\src\\app\\settings\\page.tsx": "15", "D:\\templete\\pdf-exam-generator\\src\\components\\ai\\AIAnalysisSummary.tsx": "16", "D:\\templete\\pdf-exam-generator\\src\\components\\auth\\LoginPage.tsx": "17", "D:\\templete\\pdf-exam-generator\\src\\components\\auth\\ProtectedRoute.tsx": "18", "D:\\templete\\pdf-exam-generator\\src\\components\\exam\\ExamConfiguration.tsx": "19", "D:\\templete\\pdf-exam-generator\\src\\components\\exam\\ExamInterface.tsx": "20", "D:\\templete\\pdf-exam-generator\\src\\components\\exam\\ExamProgress.tsx": "21", "D:\\templete\\pdf-exam-generator\\src\\components\\exam\\ExamTimer.tsx": "22", "D:\\templete\\pdf-exam-generator\\src\\components\\layout\\AppContent.tsx": "23", "D:\\templete\\pdf-exam-generator\\src\\components\\layout\\DashboardLayout.tsx": "24", "D:\\templete\\pdf-exam-generator\\src\\components\\layout\\Footer.tsx": "25", "D:\\templete\\pdf-exam-generator\\src\\components\\layout\\Navbar.tsx": "26", "D:\\templete\\pdf-exam-generator\\src\\components\\pages\\LandingPage.tsx": "27", "D:\\templete\\pdf-exam-generator\\src\\components\\pdf\\PDFPreview.tsx": "28", "D:\\templete\\pdf-exam-generator\\src\\components\\results\\ExportResults.tsx": "29", "D:\\templete\\pdf-exam-generator\\src\\components\\results\\PerformanceAnalytics.tsx": "30", "D:\\templete\\pdf-exam-generator\\src\\components\\results\\QuestionReview.tsx": "31", "D:\\templete\\pdf-exam-generator\\src\\components\\results\\ScoreOverview.tsx": "32", "D:\\templete\\pdf-exam-generator\\src\\components\\sections\\CTASection.tsx": "33", "D:\\templete\\pdf-exam-generator\\src\\components\\sections\\FeaturesSection.tsx": "34", "D:\\templete\\pdf-exam-generator\\src\\components\\sections\\HeroSection.tsx": "35", "D:\\templete\\pdf-exam-generator\\src\\components\\sections\\HowItWorksSection.tsx": "36", "D:\\templete\\pdf-exam-generator\\src\\components\\ui\\FileUpload.tsx": "37", "D:\\templete\\pdf-exam-generator\\src\\components\\ui\\LoadingSpinner.tsx": "38", "D:\\templete\\pdf-exam-generator\\src\\components\\upload\\FileUpload.tsx": "39", "D:\\templete\\pdf-exam-generator\\src\\contexts\\AuthContext.tsx": "40", "D:\\templete\\pdf-exam-generator\\src\\lib\\firebase.ts": "41", "D:\\templete\\pdf-exam-generator\\src\\app\\profile\\page.tsx": "42", "D:\\templete\\pdf-exam-generator\\src\\components\\landing\\Features.tsx": "43", "D:\\templete\\pdf-exam-generator\\src\\components\\landing\\Footer.tsx": "44", "D:\\templete\\pdf-exam-generator\\src\\components\\landing\\Header.tsx": "45", "D:\\templete\\pdf-exam-generator\\src\\components\\landing\\Hero.tsx": "46", "D:\\templete\\pdf-exam-generator\\src\\components\\landing\\Pricing.tsx": "47", "D:\\templete\\pdf-exam-generator\\src\\contexts\\LanguageContext.tsx": "48", "D:\\templete\\pdf-exam-generator\\src\\app\\manifest.ts": "49", "D:\\templete\\pdf-exam-generator\\src\\app\\sitemap.ts": "50"}, {"size": 3933, "mtime": 1751824179989, "results": "51", "hashOfConfig": "52"}, {"size": 6468, "mtime": 1751836535258, "results": "53", "hashOfConfig": "52"}, {"size": 10584, "mtime": 1751836745010, "results": "54", "hashOfConfig": "52"}, {"size": 14260, "mtime": 1751840579468, "results": "55", "hashOfConfig": "52"}, {"size": 11668, "mtime": 1751836878761, "results": "56", "hashOfConfig": "52"}, {"size": 12714, "mtime": 1751826404711, "results": "57", "hashOfConfig": "52"}, {"size": 15943, "mtime": 1751825955884, "results": "58", "hashOfConfig": "52"}, {"size": 8961, "mtime": 1751840406854, "results": "59", "hashOfConfig": "52"}, {"size": 13198, "mtime": 1751825895760, "results": "60", "hashOfConfig": "52"}, {"size": 7393, "mtime": 1751825625878, "results": "61", "hashOfConfig": "52"}, {"size": 10615, "mtime": 1751837126808, "results": "62", "hashOfConfig": "52"}, {"size": 1628, "mtime": 1751840855299, "results": "63", "hashOfConfig": "52"}, {"size": 785, "mtime": 1751834374493, "results": "64", "hashOfConfig": "52"}, {"size": 7079, "mtime": 1751824685509, "results": "65", "hashOfConfig": "52"}, {"size": 11563, "mtime": 1751835344506, "results": "66", "hashOfConfig": "52"}, {"size": 5989, "mtime": 1751824246344, "results": "67", "hashOfConfig": "52"}, {"size": 8940, "mtime": 1751840524949, "results": "68", "hashOfConfig": "52"}, {"size": 1531, "mtime": 1751834337572, "results": "69", "hashOfConfig": "52"}, {"size": 9172, "mtime": 1751824353308, "results": "70", "hashOfConfig": "52"}, {"size": 10400, "mtime": 1751824398796, "results": "71", "hashOfConfig": "52"}, {"size": 1057, "mtime": 1751824438721, "results": "72", "hashOfConfig": "52"}, {"size": 2486, "mtime": 1751824425298, "results": "73", "hashOfConfig": "52"}, {"size": 2309, "mtime": 1751840138331, "results": "74", "hashOfConfig": "52"}, {"size": 9670, "mtime": 1751840451975, "results": "75", "hashOfConfig": "52"}, {"size": 4796, "mtime": 1751823938057, "results": "76", "hashOfConfig": "52"}, {"size": 9173, "mtime": 1751837428591, "results": "77", "hashOfConfig": "52"}, {"size": 1203, "mtime": 1751840924135, "results": "78", "hashOfConfig": "52"}, {"size": 5052, "mtime": 1751824216897, "results": "79", "hashOfConfig": "52"}, {"size": 5353, "mtime": 1751824832012, "results": "80", "hashOfConfig": "52"}, {"size": 9781, "mtime": 1751824802502, "results": "81", "hashOfConfig": "52"}, {"size": 8496, "mtime": 1751824761830, "results": "82", "hashOfConfig": "52"}, {"size": 8044, "mtime": 1751824724277, "results": "83", "hashOfConfig": "52"}, {"size": 5810, "mtime": 1751825476712, "results": "84", "hashOfConfig": "52"}, {"size": 4373, "mtime": 1751825446906, "results": "85", "hashOfConfig": "52"}, {"size": 5974, "mtime": 1751834447656, "results": "86", "hashOfConfig": "52"}, {"size": 5808, "mtime": 1751824124682, "results": "87", "hashOfConfig": "52"}, {"size": 6941, "mtime": 1751824064879, "results": "88", "hashOfConfig": "52"}, {"size": 1023, "mtime": 1751834350798, "results": "89", "hashOfConfig": "52"}, {"size": 4227, "mtime": 1751826451060, "results": "90", "hashOfConfig": "52"}, {"size": 7433, "mtime": 1751834115990, "results": "91", "hashOfConfig": "52"}, {"size": 1618, "mtime": 1751838604089, "results": "92", "hashOfConfig": "52"}, {"size": 11221, "mtime": 1751837190476, "results": "93", "hashOfConfig": "52"}, {"size": 8629, "mtime": 1751839354562, "results": "94", "hashOfConfig": "52"}, {"size": 7964, "mtime": 1751839439398, "results": "95", "hashOfConfig": "52"}, {"size": 8430, "mtime": 1751839264013, "results": "96", "hashOfConfig": "52"}, {"size": 9687, "mtime": 1751839311600, "results": "97", "hashOfConfig": "52"}, {"size": 9068, "mtime": 1751839399622, "results": "98", "hashOfConfig": "52"}, {"size": 13216, "mtime": 1751840284505, "results": "99", "hashOfConfig": "52"}, {"size": 877, "mtime": 1751841063512, "results": "100", "hashOfConfig": "52"}, {"size": 626, "mtime": 1751841075850, "results": "101", "hashOfConfig": "52"}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "1pxi7og", {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\templete\\pdf-exam-generator\\src\\app\\analyze\\page.tsx", ["252"], [], "D:\\templete\\pdf-exam-generator\\src\\app\\auth\\forgot-password\\page.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\app\\auth\\login\\page.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\app\\auth\\register\\page.tsx", ["253"], [], "D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\analytics\\page.tsx", ["254", "255", "256"], [], "D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\exams\\page.tsx", ["257", "258", "259", "260"], [], "D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\history\\page.tsx", ["261", "262"], [], "D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\page.tsx", ["263", "264"], [], "D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\students\\page.tsx", ["265", "266", "267", "268"], [], "D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\upload\\page.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\app\\exam\\page.tsx", ["269", "270", "271", "272", "273"], [], "D:\\templete\\pdf-exam-generator\\src\\app\\layout.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\app\\page.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\app\\results\\page.tsx", ["274", "275", "276", "277"], [], "D:\\templete\\pdf-exam-generator\\src\\app\\settings\\page.tsx", ["278"], [], "D:\\templete\\pdf-exam-generator\\src\\components\\ai\\AIAnalysisSummary.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\components\\auth\\LoginPage.tsx", ["279"], [], "D:\\templete\\pdf-exam-generator\\src\\components\\auth\\ProtectedRoute.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\components\\exam\\ExamConfiguration.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\components\\exam\\ExamInterface.tsx", ["280", "281", "282", "283"], [], "D:\\templete\\pdf-exam-generator\\src\\components\\exam\\ExamProgress.tsx", ["284"], [], "D:\\templete\\pdf-exam-generator\\src\\components\\exam\\ExamTimer.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\components\\layout\\AppContent.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\components\\layout\\DashboardLayout.tsx", ["285", "286"], [], "D:\\templete\\pdf-exam-generator\\src\\components\\layout\\Footer.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\components\\layout\\Navbar.tsx", ["287", "288"], [], "D:\\templete\\pdf-exam-generator\\src\\components\\pages\\LandingPage.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\components\\pdf\\PDFPreview.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\components\\results\\ExportResults.tsx", ["289", "290"], [], "D:\\templete\\pdf-exam-generator\\src\\components\\results\\PerformanceAnalytics.tsx", ["291", "292", "293"], [], "D:\\templete\\pdf-exam-generator\\src\\components\\results\\QuestionReview.tsx", ["294", "295", "296", "297"], [], "D:\\templete\\pdf-exam-generator\\src\\components\\results\\ScoreOverview.tsx", ["298", "299"], [], "D:\\templete\\pdf-exam-generator\\src\\components\\sections\\CTASection.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\components\\sections\\FeaturesSection.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\components\\sections\\HeroSection.tsx", ["300"], [], "D:\\templete\\pdf-exam-generator\\src\\components\\sections\\HowItWorksSection.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\components\\ui\\FileUpload.tsx", ["301"], [], "D:\\templete\\pdf-exam-generator\\src\\components\\ui\\LoadingSpinner.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\components\\upload\\FileUpload.tsx", ["302"], [], "D:\\templete\\pdf-exam-generator\\src\\contexts\\AuthContext.tsx", ["303", "304", "305", "306"], [], "D:\\templete\\pdf-exam-generator\\src\\lib\\firebase.ts", [], [], "D:\\templete\\pdf-exam-generator\\src\\app\\profile\\page.tsx", ["307", "308"], [], "D:\\templete\\pdf-exam-generator\\src\\components\\landing\\Features.tsx", ["309", "310"], [], "D:\\templete\\pdf-exam-generator\\src\\components\\landing\\Footer.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\components\\landing\\Header.tsx", ["311"], [], "D:\\templete\\pdf-exam-generator\\src\\components\\landing\\Hero.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\components\\landing\\Pricing.tsx", ["312"], [], "D:\\templete\\pdf-exam-generator\\src\\contexts\\LanguageContext.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\app\\manifest.ts", [], [], "D:\\templete\\pdf-exam-generator\\src\\app\\sitemap.ts", [], [], {"ruleId": "313", "severity": 1, "message": "314", "line": 21, "column": 10, "nodeType": null, "messageId": "315", "endLine": 21, "endColumn": 26}, {"ruleId": "313", "severity": 1, "message": "316", "line": 24, "column": 11, "nodeType": null, "messageId": "315", "endLine": 24, "endColumn": 12}, {"ruleId": "313", "severity": 1, "message": "317", "line": 8, "column": 3, "nodeType": null, "messageId": "315", "endLine": 8, "endColumn": 8}, {"ruleId": "313", "severity": 1, "message": "318", "line": 12, "column": 3, "nodeType": null, "messageId": "315", "endLine": 12, "endColumn": 11}, {"ruleId": "313", "severity": 1, "message": "319", "line": 20, "column": 11, "nodeType": null, "messageId": "315", "endLine": 20, "endColumn": 15}, {"ruleId": "313", "severity": 1, "message": "320", "line": 8, "column": 3, "nodeType": null, "messageId": "315", "endLine": 8, "endColumn": 9}, {"ruleId": "313", "severity": 1, "message": "321", "line": 12, "column": 3, "nodeType": null, "messageId": "315", "endLine": 12, "endColumn": 8}, {"ruleId": "313", "severity": 1, "message": "322", "line": 17, "column": 3, "nodeType": null, "messageId": "315", "endLine": 17, "endColumn": 9}, {"ruleId": "313", "severity": 1, "message": "319", "line": 25, "column": 11, "nodeType": null, "messageId": "315", "endLine": 25, "endColumn": 15}, {"ruleId": "313", "severity": 1, "message": "320", "line": 11, "column": 3, "nodeType": null, "messageId": "315", "endLine": 11, "endColumn": 9}, {"ruleId": "313", "severity": 1, "message": "319", "line": 23, "column": 11, "nodeType": null, "messageId": "315", "endLine": 23, "endColumn": 15}, {"ruleId": "313", "severity": 1, "message": "323", "line": 9, "column": 3, "nodeType": null, "messageId": "315", "endLine": 9, "endColumn": 13}, {"ruleId": "313", "severity": 1, "message": "318", "line": 14, "column": 3, "nodeType": null, "messageId": "315", "endLine": 14, "endColumn": 11}, {"ruleId": "313", "severity": 1, "message": "320", "line": 8, "column": 3, "nodeType": null, "messageId": "315", "endLine": 8, "endColumn": 9}, {"ruleId": "313", "severity": 1, "message": "324", "line": 9, "column": 3, "nodeType": null, "messageId": "315", "endLine": 9, "endColumn": 7}, {"ruleId": "313", "severity": 1, "message": "318", "line": 13, "column": 3, "nodeType": null, "messageId": "315", "endLine": 13, "endColumn": 11}, {"ruleId": "313", "severity": 1, "message": "319", "line": 24, "column": 11, "nodeType": null, "messageId": "315", "endLine": 24, "endColumn": 15}, {"ruleId": "313", "severity": 1, "message": "325", "line": 8, "column": 21, "nodeType": null, "messageId": "315", "endLine": 8, "endColumn": 25}, {"ruleId": "313", "severity": 1, "message": "326", "line": 8, "column": 27, "nodeType": null, "messageId": "315", "endLine": 8, "endColumn": 31}, {"ruleId": "327", "severity": 1, "message": "328", "line": 63, "column": 48, "nodeType": "329", "messageId": "330", "endLine": 63, "endColumn": 51, "suggestions": "331"}, {"ruleId": "327", "severity": 1, "message": "328", "line": 65, "column": 57, "nodeType": "329", "messageId": "330", "endLine": 65, "endColumn": 60, "suggestions": "332"}, {"ruleId": "327", "severity": 1, "message": "328", "line": 97, "column": 59, "nodeType": "329", "messageId": "330", "endLine": 97, "endColumn": 62, "suggestions": "333"}, {"ruleId": "313", "severity": 1, "message": "334", "line": 9, "column": 32, "nodeType": null, "messageId": "315", "endLine": 9, "endColumn": 38}, {"ruleId": "327", "severity": 1, "message": "328", "line": 12, "column": 27, "nodeType": "329", "messageId": "330", "endLine": 12, "endColumn": 30, "suggestions": "335"}, {"ruleId": "327", "severity": 1, "message": "328", "line": 13, "column": 14, "nodeType": "329", "messageId": "330", "endLine": 13, "endColumn": 17, "suggestions": "336"}, {"ruleId": "327", "severity": 1, "message": "328", "line": 14, "column": 11, "nodeType": "329", "messageId": "330", "endLine": 14, "endColumn": 14, "suggestions": "337"}, {"ruleId": "313", "severity": 1, "message": "338", "line": 6, "column": 47, "nodeType": null, "messageId": "315", "endLine": 6, "endColumn": 54}, {"ruleId": "313", "severity": 1, "message": "339", "line": 33, "column": 14, "nodeType": null, "messageId": "315", "endLine": 33, "endColumn": 17}, {"ruleId": "313", "severity": 1, "message": "340", "line": 4, "column": 56, "nodeType": null, "messageId": "315", "endLine": 4, "endColumn": 62}, {"ruleId": "327", "severity": 1, "message": "328", "line": 11, "column": 18, "nodeType": "329", "messageId": "330", "endLine": 11, "endColumn": 21, "suggestions": "341"}, {"ruleId": "327", "severity": 1, "message": "328", "line": 18, "column": 27, "nodeType": "329", "messageId": "330", "endLine": 18, "endColumn": 30, "suggestions": "342"}, {"ruleId": "327", "severity": 1, "message": "328", "line": 19, "column": 48, "nodeType": "329", "messageId": "330", "endLine": 19, "endColumn": 51, "suggestions": "343"}, {"ruleId": "313", "severity": 1, "message": "344", "line": 1, "column": 10, "nodeType": null, "messageId": "315", "endLine": 1, "endColumn": 21}, {"ruleId": "345", "severity": 1, "message": "346", "line": 87, "column": 17, "nodeType": "347", "endLine": 87, "endColumn": 126}, {"ruleId": "345", "severity": 1, "message": "346", "line": 208, "column": 21, "nodeType": "347", "endLine": 208, "endColumn": 128}, {"ruleId": "345", "severity": 1, "message": "346", "line": 84, "column": 23, "nodeType": "347", "endLine": 84, "endColumn": 130}, {"ruleId": "345", "severity": 1, "message": "346", "line": 175, "column": 23, "nodeType": "347", "endLine": 175, "endColumn": 130}, {"ruleId": "327", "severity": 1, "message": "328", "line": 4, "column": 16, "nodeType": "329", "messageId": "330", "endLine": 4, "endColumn": 19, "suggestions": "348"}, {"ruleId": "327", "severity": 1, "message": "328", "line": 24, "column": 33, "nodeType": "329", "messageId": "330", "endLine": 24, "endColumn": 36, "suggestions": "349"}, {"ruleId": "313", "severity": 1, "message": "321", "line": 1, "column": 41, "nodeType": null, "messageId": "315", "endLine": 1, "endColumn": 46}, {"ruleId": "327", "severity": 1, "message": "328", "line": 4, "column": 14, "nodeType": "329", "messageId": "330", "endLine": 4, "endColumn": 17, "suggestions": "350"}, {"ruleId": "327", "severity": 1, "message": "328", "line": 5, "column": 27, "nodeType": "329", "messageId": "330", "endLine": 5, "endColumn": 30, "suggestions": "351"}, {"ruleId": "327", "severity": 1, "message": "328", "line": 8, "column": 18, "nodeType": "329", "messageId": "330", "endLine": 8, "endColumn": 21, "suggestions": "352"}, {"ruleId": "327", "severity": 1, "message": "328", "line": 14, "column": 27, "nodeType": "329", "messageId": "330", "endLine": 14, "endColumn": 30, "suggestions": "353"}, {"ruleId": "327", "severity": 1, "message": "328", "line": 18, "column": 54, "nodeType": "329", "messageId": "330", "endLine": 18, "endColumn": 57, "suggestions": "354"}, {"ruleId": "327", "severity": 1, "message": "328", "line": 31, "column": 61, "nodeType": "329", "messageId": "330", "endLine": 31, "endColumn": 64, "suggestions": "355"}, {"ruleId": "327", "severity": 1, "message": "328", "line": 8, "column": 11, "nodeType": "329", "messageId": "330", "endLine": 8, "endColumn": 14, "suggestions": "356"}, {"ruleId": "357", "severity": 1, "message": "358", "line": 57, "column": 51, "nodeType": "359", "messageId": "360", "suggestions": "361"}, {"ruleId": "313", "severity": 1, "message": "362", "line": 11, "column": 10, "nodeType": null, "messageId": "315", "endLine": 11, "endColumn": 22}, {"ruleId": "327", "severity": 1, "message": "328", "line": 19, "column": 69, "nodeType": "329", "messageId": "330", "endLine": 19, "endColumn": 72, "suggestions": "363"}, {"ruleId": "327", "severity": 1, "message": "328", "line": 16, "column": 69, "nodeType": "329", "messageId": "330", "endLine": 16, "endColumn": 72, "suggestions": "364"}, {"ruleId": "313", "severity": 1, "message": "365", "line": 13, "column": 3, "nodeType": null, "messageId": "315", "endLine": 13, "endColumn": 21}, {"ruleId": "313", "severity": 1, "message": "366", "line": 14, "column": 3, "nodeType": null, "messageId": "315", "endLine": 14, "endColumn": 23}, {"ruleId": "327", "severity": 1, "message": "328", "line": 72, "column": 72, "nodeType": "329", "messageId": "330", "endLine": 72, "endColumn": 75, "suggestions": "367"}, {"ruleId": "313", "severity": 1, "message": "368", "line": 142, "column": 14, "nodeType": null, "messageId": "315", "endLine": 142, "endColumn": 19}, {"ruleId": "313", "severity": 1, "message": "319", "line": 9, "column": 43, "nodeType": null, "messageId": "315", "endLine": 9, "endColumn": 47}, {"ruleId": "345", "severity": 1, "message": "346", "line": 79, "column": 23, "nodeType": "347", "endLine": 83, "endColumn": 25}, {"ruleId": "313", "severity": 1, "message": "321", "line": 13, "column": 3, "nodeType": null, "messageId": "315", "endLine": 13, "endColumn": 8}, {"ruleId": "313", "severity": 1, "message": "369", "line": 15, "column": 3, "nodeType": null, "messageId": "315", "endLine": 15, "endColumn": 11}, {"ruleId": "313", "severity": 1, "message": "370", "line": 13, "column": 3, "nodeType": null, "messageId": "315", "endLine": 13, "endColumn": 6}, {"ruleId": "313", "severity": 1, "message": "371", "line": 10, "column": 3, "nodeType": null, "messageId": "315", "endLine": 10, "endColumn": 8}, "@typescript-eslint/no-unused-vars", "'analysisComplete' is assigned a value but never used.", "unusedVar", "'t' is assigned a value but never used.", "'Users' is defined but never used.", "'Calendar' is defined but never used.", "'user' is assigned a value but never used.", "'Filter' is defined but never used.", "'Clock' is defined but never used.", "'Trash2' is defined but never used.", "'TrendingUp' is defined but never used.", "'Plus' is defined but never used.", "'Save' is defined but never used.", "'Flag' is defined but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["372", "373"], ["374", "375"], ["376", "377"], "'Share2' is defined but never used.", ["378", "379"], ["380", "381"], ["382", "383"], "'Palette' is defined but never used.", "'err' is defined but never used.", "'Circle' is defined but never used.", ["384", "385"], ["386", "387"], ["388", "389"], "'CheckCircle' is defined but never used.", "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", ["390", "391"], ["392", "393"], ["394", "395"], ["396", "397"], ["398", "399"], ["400", "401"], ["402", "403"], ["404", "405"], ["406", "407"], "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["408", "409", "410", "411"], "'uploadedFile' is assigned a value but never used.", ["412", "413"], ["414", "415"], "'GoogleAuthProvider' is defined but never used.", "'FacebookAuthProvider' is defined but never used.", ["416", "417"], "'error' is defined but never used.", "'Sparkles' is defined but never used.", "'Zap' is defined but never used.", "'Crown' is defined but never used.", {"messageId": "418", "fix": "419", "desc": "420"}, {"messageId": "421", "fix": "422", "desc": "423"}, {"messageId": "418", "fix": "424", "desc": "420"}, {"messageId": "421", "fix": "425", "desc": "423"}, {"messageId": "418", "fix": "426", "desc": "420"}, {"messageId": "421", "fix": "427", "desc": "423"}, {"messageId": "418", "fix": "428", "desc": "420"}, {"messageId": "421", "fix": "429", "desc": "423"}, {"messageId": "418", "fix": "430", "desc": "420"}, {"messageId": "421", "fix": "431", "desc": "423"}, {"messageId": "418", "fix": "432", "desc": "420"}, {"messageId": "421", "fix": "433", "desc": "423"}, {"messageId": "418", "fix": "434", "desc": "420"}, {"messageId": "421", "fix": "435", "desc": "423"}, {"messageId": "418", "fix": "436", "desc": "420"}, {"messageId": "421", "fix": "437", "desc": "423"}, {"messageId": "418", "fix": "438", "desc": "420"}, {"messageId": "421", "fix": "439", "desc": "423"}, {"messageId": "418", "fix": "440", "desc": "420"}, {"messageId": "421", "fix": "441", "desc": "423"}, {"messageId": "418", "fix": "442", "desc": "420"}, {"messageId": "421", "fix": "443", "desc": "423"}, {"messageId": "418", "fix": "444", "desc": "420"}, {"messageId": "421", "fix": "445", "desc": "423"}, {"messageId": "418", "fix": "446", "desc": "420"}, {"messageId": "421", "fix": "447", "desc": "423"}, {"messageId": "418", "fix": "448", "desc": "420"}, {"messageId": "421", "fix": "449", "desc": "423"}, {"messageId": "418", "fix": "450", "desc": "420"}, {"messageId": "421", "fix": "451", "desc": "423"}, {"messageId": "418", "fix": "452", "desc": "420"}, {"messageId": "421", "fix": "453", "desc": "423"}, {"messageId": "418", "fix": "454", "desc": "420"}, {"messageId": "421", "fix": "455", "desc": "423"}, {"messageId": "418", "fix": "456", "desc": "420"}, {"messageId": "421", "fix": "457", "desc": "423"}, {"messageId": "458", "data": "459", "fix": "460", "desc": "461"}, {"messageId": "458", "data": "462", "fix": "463", "desc": "464"}, {"messageId": "458", "data": "465", "fix": "466", "desc": "467"}, {"messageId": "458", "data": "468", "fix": "469", "desc": "470"}, {"messageId": "418", "fix": "471", "desc": "420"}, {"messageId": "421", "fix": "472", "desc": "423"}, {"messageId": "418", "fix": "473", "desc": "420"}, {"messageId": "421", "fix": "474", "desc": "423"}, {"messageId": "418", "fix": "475", "desc": "420"}, {"messageId": "421", "fix": "476", "desc": "423"}, "suggestUnknown", {"range": "477", "text": "478"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "479", "text": "480"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "481", "text": "478"}, {"range": "482", "text": "480"}, {"range": "483", "text": "478"}, {"range": "484", "text": "480"}, {"range": "485", "text": "478"}, {"range": "486", "text": "480"}, {"range": "487", "text": "478"}, {"range": "488", "text": "480"}, {"range": "489", "text": "478"}, {"range": "490", "text": "480"}, {"range": "491", "text": "478"}, {"range": "492", "text": "480"}, {"range": "493", "text": "478"}, {"range": "494", "text": "480"}, {"range": "495", "text": "478"}, {"range": "496", "text": "480"}, {"range": "497", "text": "478"}, {"range": "498", "text": "480"}, {"range": "499", "text": "478"}, {"range": "500", "text": "480"}, {"range": "501", "text": "478"}, {"range": "502", "text": "480"}, {"range": "503", "text": "478"}, {"range": "504", "text": "480"}, {"range": "505", "text": "478"}, {"range": "506", "text": "480"}, {"range": "507", "text": "478"}, {"range": "508", "text": "480"}, {"range": "509", "text": "478"}, {"range": "510", "text": "480"}, {"range": "511", "text": "478"}, {"range": "512", "text": "480"}, {"range": "513", "text": "478"}, {"range": "514", "text": "480"}, "replaceWithAlt", {"alt": "515"}, {"range": "516", "text": "517"}, "Replace with `&apos;`.", {"alt": "518"}, {"range": "519", "text": "520"}, "Replace with `&lsquo;`.", {"alt": "521"}, {"range": "522", "text": "523"}, "Replace with `&#39;`.", {"alt": "524"}, {"range": "525", "text": "526"}, "Replace with `&rsquo;`.", {"range": "527", "text": "478"}, {"range": "528", "text": "480"}, {"range": "529", "text": "478"}, {"range": "530", "text": "480"}, {"range": "531", "text": "478"}, {"range": "532", "text": "480"}, [2805, 2808], "unknown", [2805, 2808], "never", [2934, 2937], [2934, 2937], [3947, 3950], [3947, 3950], [505, 508], [505, 508], [524, 527], [524, 527], [541, 544], [541, 544], [303, 306], [303, 306], [443, 446], [443, 446], [496, 499], [496, 499], [112, 115], [112, 115], [691, 694], [691, 694], [121, 124], [121, 124], [154, 157], [154, 157], [240, 243], [240, 243], [354, 357], [354, 357], [494, 497], [494, 497], [1071, 1074], [1071, 1074], [207, 210], [207, 210], "&apos;", [2011, 2048], "Here&apos;s how you performed on your exam", "&lsquo;", [2011, 2048], "Here&lsquo;s how you performed on your exam", "&#39;", [2011, 2048], "Here&#39;s how you performed on your exam", "&rsquo;", [2011, 2048], "Here&rsquo;s how you performed on your exam", [760, 763], [760, 763], [522, 525], [522, 525], [2229, 2232], [2229, 2232]]