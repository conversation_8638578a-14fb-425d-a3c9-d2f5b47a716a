[{"D:\\templete\\pdf-exam-generator\\src\\app\\analyze\\page.tsx": "1", "D:\\templete\\pdf-exam-generator\\src\\app\\auth\\forgot-password\\page.tsx": "2", "D:\\templete\\pdf-exam-generator\\src\\app\\auth\\login\\page.tsx": "3", "D:\\templete\\pdf-exam-generator\\src\\app\\auth\\register\\page.tsx": "4", "D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\analytics\\page.tsx": "5", "D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\exams\\page.tsx": "6", "D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\history\\page.tsx": "7", "D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\page.tsx": "8", "D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\students\\page.tsx": "9", "D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\upload\\page.tsx": "10", "D:\\templete\\pdf-exam-generator\\src\\app\\exam\\page.tsx": "11", "D:\\templete\\pdf-exam-generator\\src\\app\\layout.tsx": "12", "D:\\templete\\pdf-exam-generator\\src\\app\\page.tsx": "13", "D:\\templete\\pdf-exam-generator\\src\\app\\results\\page.tsx": "14", "D:\\templete\\pdf-exam-generator\\src\\app\\settings\\page.tsx": "15", "D:\\templete\\pdf-exam-generator\\src\\components\\ai\\AIAnalysisSummary.tsx": "16", "D:\\templete\\pdf-exam-generator\\src\\components\\auth\\LoginPage.tsx": "17", "D:\\templete\\pdf-exam-generator\\src\\components\\auth\\ProtectedRoute.tsx": "18", "D:\\templete\\pdf-exam-generator\\src\\components\\exam\\ExamConfiguration.tsx": "19", "D:\\templete\\pdf-exam-generator\\src\\components\\exam\\ExamInterface.tsx": "20", "D:\\templete\\pdf-exam-generator\\src\\components\\exam\\ExamProgress.tsx": "21", "D:\\templete\\pdf-exam-generator\\src\\components\\exam\\ExamTimer.tsx": "22", "D:\\templete\\pdf-exam-generator\\src\\components\\layout\\AppContent.tsx": "23", "D:\\templete\\pdf-exam-generator\\src\\components\\layout\\DashboardLayout.tsx": "24", "D:\\templete\\pdf-exam-generator\\src\\components\\layout\\Footer.tsx": "25", "D:\\templete\\pdf-exam-generator\\src\\components\\layout\\Navbar.tsx": "26", "D:\\templete\\pdf-exam-generator\\src\\components\\pages\\LandingPage.tsx": "27", "D:\\templete\\pdf-exam-generator\\src\\components\\pdf\\PDFPreview.tsx": "28", "D:\\templete\\pdf-exam-generator\\src\\components\\results\\ExportResults.tsx": "29", "D:\\templete\\pdf-exam-generator\\src\\components\\results\\PerformanceAnalytics.tsx": "30", "D:\\templete\\pdf-exam-generator\\src\\components\\results\\QuestionReview.tsx": "31", "D:\\templete\\pdf-exam-generator\\src\\components\\results\\ScoreOverview.tsx": "32", "D:\\templete\\pdf-exam-generator\\src\\components\\sections\\CTASection.tsx": "33", "D:\\templete\\pdf-exam-generator\\src\\components\\sections\\FeaturesSection.tsx": "34", "D:\\templete\\pdf-exam-generator\\src\\components\\sections\\HeroSection.tsx": "35", "D:\\templete\\pdf-exam-generator\\src\\components\\sections\\HowItWorksSection.tsx": "36", "D:\\templete\\pdf-exam-generator\\src\\components\\ui\\FileUpload.tsx": "37", "D:\\templete\\pdf-exam-generator\\src\\components\\ui\\LoadingSpinner.tsx": "38", "D:\\templete\\pdf-exam-generator\\src\\components\\upload\\FileUpload.tsx": "39", "D:\\templete\\pdf-exam-generator\\src\\contexts\\AuthContext.tsx": "40", "D:\\templete\\pdf-exam-generator\\src\\lib\\firebase.ts": "41", "D:\\templete\\pdf-exam-generator\\src\\app\\profile\\page.tsx": "42", "D:\\templete\\pdf-exam-generator\\src\\components\\landing\\Features.tsx": "43", "D:\\templete\\pdf-exam-generator\\src\\components\\landing\\Footer.tsx": "44", "D:\\templete\\pdf-exam-generator\\src\\components\\landing\\Header.tsx": "45", "D:\\templete\\pdf-exam-generator\\src\\components\\landing\\Hero.tsx": "46", "D:\\templete\\pdf-exam-generator\\src\\components\\landing\\Pricing.tsx": "47", "D:\\templete\\pdf-exam-generator\\src\\contexts\\LanguageContext.tsx": "48"}, {"size": 3933, "mtime": 1751824179989, "results": "49", "hashOfConfig": "50"}, {"size": 6468, "mtime": 1751836535258, "results": "51", "hashOfConfig": "50"}, {"size": 10584, "mtime": 1751836745010, "results": "52", "hashOfConfig": "50"}, {"size": 14260, "mtime": 1751840579468, "results": "53", "hashOfConfig": "50"}, {"size": 11668, "mtime": 1751836878761, "results": "54", "hashOfConfig": "50"}, {"size": 12714, "mtime": 1751826404711, "results": "55", "hashOfConfig": "50"}, {"size": 15943, "mtime": 1751825955884, "results": "56", "hashOfConfig": "50"}, {"size": 8961, "mtime": 1751840406854, "results": "57", "hashOfConfig": "50"}, {"size": 13198, "mtime": 1751825895760, "results": "58", "hashOfConfig": "50"}, {"size": 7393, "mtime": 1751825625878, "results": "59", "hashOfConfig": "50"}, {"size": 10615, "mtime": 1751837126808, "results": "60", "hashOfConfig": "50"}, {"size": 1239, "mtime": 1751840074965, "results": "61", "hashOfConfig": "50"}, {"size": 785, "mtime": 1751834374493, "results": "62", "hashOfConfig": "50"}, {"size": 7079, "mtime": 1751824685509, "results": "63", "hashOfConfig": "50"}, {"size": 11563, "mtime": 1751835344506, "results": "64", "hashOfConfig": "50"}, {"size": 5989, "mtime": 1751824246344, "results": "65", "hashOfConfig": "50"}, {"size": 8940, "mtime": 1751840524949, "results": "66", "hashOfConfig": "50"}, {"size": 1531, "mtime": 1751834337572, "results": "67", "hashOfConfig": "50"}, {"size": 9172, "mtime": 1751824353308, "results": "68", "hashOfConfig": "50"}, {"size": 10400, "mtime": 1751824398796, "results": "69", "hashOfConfig": "50"}, {"size": 1057, "mtime": 1751824438721, "results": "70", "hashOfConfig": "50"}, {"size": 2486, "mtime": 1751824425298, "results": "71", "hashOfConfig": "50"}, {"size": 2309, "mtime": 1751840138331, "results": "72", "hashOfConfig": "50"}, {"size": 9670, "mtime": 1751840451975, "results": "73", "hashOfConfig": "50"}, {"size": 4796, "mtime": 1751823938057, "results": "74", "hashOfConfig": "50"}, {"size": 9173, "mtime": 1751837428591, "results": "75", "hashOfConfig": "50"}, {"size": 657, "mtime": 1751839213860, "results": "76", "hashOfConfig": "50"}, {"size": 5052, "mtime": 1751824216897, "results": "77", "hashOfConfig": "50"}, {"size": 5353, "mtime": 1751824832012, "results": "78", "hashOfConfig": "50"}, {"size": 9781, "mtime": 1751824802502, "results": "79", "hashOfConfig": "50"}, {"size": 8496, "mtime": 1751824761830, "results": "80", "hashOfConfig": "50"}, {"size": 8044, "mtime": 1751824724277, "results": "81", "hashOfConfig": "50"}, {"size": 5810, "mtime": 1751825476712, "results": "82", "hashOfConfig": "50"}, {"size": 4373, "mtime": 1751825446906, "results": "83", "hashOfConfig": "50"}, {"size": 5974, "mtime": 1751834447656, "results": "84", "hashOfConfig": "50"}, {"size": 5808, "mtime": 1751824124682, "results": "85", "hashOfConfig": "50"}, {"size": 6941, "mtime": 1751824064879, "results": "86", "hashOfConfig": "50"}, {"size": 1023, "mtime": 1751834350798, "results": "87", "hashOfConfig": "50"}, {"size": 4227, "mtime": 1751826451060, "results": "88", "hashOfConfig": "50"}, {"size": 7433, "mtime": 1751834115990, "results": "89", "hashOfConfig": "50"}, {"size": 1618, "mtime": 1751838604089, "results": "90", "hashOfConfig": "50"}, {"size": 11221, "mtime": 1751837190476, "results": "91", "hashOfConfig": "50"}, {"size": 8629, "mtime": 1751839354562, "results": "92", "hashOfConfig": "50"}, {"size": 7964, "mtime": 1751839439398, "results": "93", "hashOfConfig": "50"}, {"size": 8430, "mtime": 1751839264013, "results": "94", "hashOfConfig": "50"}, {"size": 9687, "mtime": 1751839311600, "results": "95", "hashOfConfig": "50"}, {"size": 9068, "mtime": 1751839399622, "results": "96", "hashOfConfig": "50"}, {"size": 13216, "mtime": 1751840284505, "results": "97", "hashOfConfig": "50"}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "1pxi7og", {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\templete\\pdf-exam-generator\\src\\app\\analyze\\page.tsx", ["242"], [], "D:\\templete\\pdf-exam-generator\\src\\app\\auth\\forgot-password\\page.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\app\\auth\\login\\page.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\app\\auth\\register\\page.tsx", ["243"], [], "D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\analytics\\page.tsx", ["244", "245", "246"], [], "D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\exams\\page.tsx", ["247", "248", "249", "250"], [], "D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\history\\page.tsx", ["251", "252"], [], "D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\page.tsx", ["253", "254"], [], "D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\students\\page.tsx", ["255", "256", "257", "258"], [], "D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\upload\\page.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\app\\exam\\page.tsx", ["259", "260", "261", "262", "263"], [], "D:\\templete\\pdf-exam-generator\\src\\app\\layout.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\app\\page.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\app\\results\\page.tsx", ["264", "265", "266", "267"], [], "D:\\templete\\pdf-exam-generator\\src\\app\\settings\\page.tsx", ["268"], [], "D:\\templete\\pdf-exam-generator\\src\\components\\ai\\AIAnalysisSummary.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\components\\auth\\LoginPage.tsx", ["269"], [], "D:\\templete\\pdf-exam-generator\\src\\components\\auth\\ProtectedRoute.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\components\\exam\\ExamConfiguration.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\components\\exam\\ExamInterface.tsx", ["270", "271", "272", "273"], [], "D:\\templete\\pdf-exam-generator\\src\\components\\exam\\ExamProgress.tsx", ["274"], [], "D:\\templete\\pdf-exam-generator\\src\\components\\exam\\ExamTimer.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\components\\layout\\AppContent.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\components\\layout\\DashboardLayout.tsx", ["275", "276"], [], "D:\\templete\\pdf-exam-generator\\src\\components\\layout\\Footer.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\components\\layout\\Navbar.tsx", ["277", "278"], [], "D:\\templete\\pdf-exam-generator\\src\\components\\pages\\LandingPage.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\components\\pdf\\PDFPreview.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\components\\results\\ExportResults.tsx", ["279", "280"], [], "D:\\templete\\pdf-exam-generator\\src\\components\\results\\PerformanceAnalytics.tsx", ["281", "282", "283"], [], "D:\\templete\\pdf-exam-generator\\src\\components\\results\\QuestionReview.tsx", ["284", "285", "286", "287"], [], "D:\\templete\\pdf-exam-generator\\src\\components\\results\\ScoreOverview.tsx", ["288", "289"], [], "D:\\templete\\pdf-exam-generator\\src\\components\\sections\\CTASection.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\components\\sections\\FeaturesSection.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\components\\sections\\HeroSection.tsx", ["290"], [], "D:\\templete\\pdf-exam-generator\\src\\components\\sections\\HowItWorksSection.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\components\\ui\\FileUpload.tsx", ["291"], [], "D:\\templete\\pdf-exam-generator\\src\\components\\ui\\LoadingSpinner.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\components\\upload\\FileUpload.tsx", ["292"], [], "D:\\templete\\pdf-exam-generator\\src\\contexts\\AuthContext.tsx", ["293", "294", "295", "296"], [], "D:\\templete\\pdf-exam-generator\\src\\lib\\firebase.ts", [], [], "D:\\templete\\pdf-exam-generator\\src\\app\\profile\\page.tsx", ["297", "298"], [], "D:\\templete\\pdf-exam-generator\\src\\components\\landing\\Features.tsx", ["299", "300"], [], "D:\\templete\\pdf-exam-generator\\src\\components\\landing\\Footer.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\components\\landing\\Header.tsx", ["301"], [], "D:\\templete\\pdf-exam-generator\\src\\components\\landing\\Hero.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\components\\landing\\Pricing.tsx", ["302"], [], "D:\\templete\\pdf-exam-generator\\src\\contexts\\LanguageContext.tsx", [], [], {"ruleId": "303", "severity": 1, "message": "304", "line": 21, "column": 10, "nodeType": null, "messageId": "305", "endLine": 21, "endColumn": 26}, {"ruleId": "303", "severity": 1, "message": "306", "line": 24, "column": 11, "nodeType": null, "messageId": "305", "endLine": 24, "endColumn": 12}, {"ruleId": "303", "severity": 1, "message": "307", "line": 8, "column": 3, "nodeType": null, "messageId": "305", "endLine": 8, "endColumn": 8}, {"ruleId": "303", "severity": 1, "message": "308", "line": 12, "column": 3, "nodeType": null, "messageId": "305", "endLine": 12, "endColumn": 11}, {"ruleId": "303", "severity": 1, "message": "309", "line": 20, "column": 11, "nodeType": null, "messageId": "305", "endLine": 20, "endColumn": 15}, {"ruleId": "303", "severity": 1, "message": "310", "line": 8, "column": 3, "nodeType": null, "messageId": "305", "endLine": 8, "endColumn": 9}, {"ruleId": "303", "severity": 1, "message": "311", "line": 12, "column": 3, "nodeType": null, "messageId": "305", "endLine": 12, "endColumn": 8}, {"ruleId": "303", "severity": 1, "message": "312", "line": 17, "column": 3, "nodeType": null, "messageId": "305", "endLine": 17, "endColumn": 9}, {"ruleId": "303", "severity": 1, "message": "309", "line": 25, "column": 11, "nodeType": null, "messageId": "305", "endLine": 25, "endColumn": 15}, {"ruleId": "303", "severity": 1, "message": "310", "line": 11, "column": 3, "nodeType": null, "messageId": "305", "endLine": 11, "endColumn": 9}, {"ruleId": "303", "severity": 1, "message": "309", "line": 23, "column": 11, "nodeType": null, "messageId": "305", "endLine": 23, "endColumn": 15}, {"ruleId": "303", "severity": 1, "message": "313", "line": 9, "column": 3, "nodeType": null, "messageId": "305", "endLine": 9, "endColumn": 13}, {"ruleId": "303", "severity": 1, "message": "308", "line": 14, "column": 3, "nodeType": null, "messageId": "305", "endLine": 14, "endColumn": 11}, {"ruleId": "303", "severity": 1, "message": "310", "line": 8, "column": 3, "nodeType": null, "messageId": "305", "endLine": 8, "endColumn": 9}, {"ruleId": "303", "severity": 1, "message": "314", "line": 9, "column": 3, "nodeType": null, "messageId": "305", "endLine": 9, "endColumn": 7}, {"ruleId": "303", "severity": 1, "message": "308", "line": 13, "column": 3, "nodeType": null, "messageId": "305", "endLine": 13, "endColumn": 11}, {"ruleId": "303", "severity": 1, "message": "309", "line": 24, "column": 11, "nodeType": null, "messageId": "305", "endLine": 24, "endColumn": 15}, {"ruleId": "303", "severity": 1, "message": "315", "line": 8, "column": 21, "nodeType": null, "messageId": "305", "endLine": 8, "endColumn": 25}, {"ruleId": "303", "severity": 1, "message": "316", "line": 8, "column": 27, "nodeType": null, "messageId": "305", "endLine": 8, "endColumn": 31}, {"ruleId": "317", "severity": 1, "message": "318", "line": 63, "column": 48, "nodeType": "319", "messageId": "320", "endLine": 63, "endColumn": 51, "suggestions": "321"}, {"ruleId": "317", "severity": 1, "message": "318", "line": 65, "column": 57, "nodeType": "319", "messageId": "320", "endLine": 65, "endColumn": 60, "suggestions": "322"}, {"ruleId": "317", "severity": 1, "message": "318", "line": 97, "column": 59, "nodeType": "319", "messageId": "320", "endLine": 97, "endColumn": 62, "suggestions": "323"}, {"ruleId": "303", "severity": 1, "message": "324", "line": 9, "column": 32, "nodeType": null, "messageId": "305", "endLine": 9, "endColumn": 38}, {"ruleId": "317", "severity": 1, "message": "318", "line": 12, "column": 27, "nodeType": "319", "messageId": "320", "endLine": 12, "endColumn": 30, "suggestions": "325"}, {"ruleId": "317", "severity": 1, "message": "318", "line": 13, "column": 14, "nodeType": "319", "messageId": "320", "endLine": 13, "endColumn": 17, "suggestions": "326"}, {"ruleId": "317", "severity": 1, "message": "318", "line": 14, "column": 11, "nodeType": "319", "messageId": "320", "endLine": 14, "endColumn": 14, "suggestions": "327"}, {"ruleId": "303", "severity": 1, "message": "328", "line": 6, "column": 47, "nodeType": null, "messageId": "305", "endLine": 6, "endColumn": 54}, {"ruleId": "303", "severity": 1, "message": "329", "line": 33, "column": 14, "nodeType": null, "messageId": "305", "endLine": 33, "endColumn": 17}, {"ruleId": "303", "severity": 1, "message": "330", "line": 4, "column": 56, "nodeType": null, "messageId": "305", "endLine": 4, "endColumn": 62}, {"ruleId": "317", "severity": 1, "message": "318", "line": 11, "column": 18, "nodeType": "319", "messageId": "320", "endLine": 11, "endColumn": 21, "suggestions": "331"}, {"ruleId": "317", "severity": 1, "message": "318", "line": 18, "column": 27, "nodeType": "319", "messageId": "320", "endLine": 18, "endColumn": 30, "suggestions": "332"}, {"ruleId": "317", "severity": 1, "message": "318", "line": 19, "column": 48, "nodeType": "319", "messageId": "320", "endLine": 19, "endColumn": 51, "suggestions": "333"}, {"ruleId": "303", "severity": 1, "message": "334", "line": 1, "column": 10, "nodeType": null, "messageId": "305", "endLine": 1, "endColumn": 21}, {"ruleId": "335", "severity": 1, "message": "336", "line": 87, "column": 17, "nodeType": "337", "endLine": 87, "endColumn": 126}, {"ruleId": "335", "severity": 1, "message": "336", "line": 208, "column": 21, "nodeType": "337", "endLine": 208, "endColumn": 128}, {"ruleId": "335", "severity": 1, "message": "336", "line": 84, "column": 23, "nodeType": "337", "endLine": 84, "endColumn": 130}, {"ruleId": "335", "severity": 1, "message": "336", "line": 175, "column": 23, "nodeType": "337", "endLine": 175, "endColumn": 130}, {"ruleId": "317", "severity": 1, "message": "318", "line": 4, "column": 16, "nodeType": "319", "messageId": "320", "endLine": 4, "endColumn": 19, "suggestions": "338"}, {"ruleId": "317", "severity": 1, "message": "318", "line": 24, "column": 33, "nodeType": "319", "messageId": "320", "endLine": 24, "endColumn": 36, "suggestions": "339"}, {"ruleId": "303", "severity": 1, "message": "311", "line": 1, "column": 41, "nodeType": null, "messageId": "305", "endLine": 1, "endColumn": 46}, {"ruleId": "317", "severity": 1, "message": "318", "line": 4, "column": 14, "nodeType": "319", "messageId": "320", "endLine": 4, "endColumn": 17, "suggestions": "340"}, {"ruleId": "317", "severity": 1, "message": "318", "line": 5, "column": 27, "nodeType": "319", "messageId": "320", "endLine": 5, "endColumn": 30, "suggestions": "341"}, {"ruleId": "317", "severity": 1, "message": "318", "line": 8, "column": 18, "nodeType": "319", "messageId": "320", "endLine": 8, "endColumn": 21, "suggestions": "342"}, {"ruleId": "317", "severity": 1, "message": "318", "line": 14, "column": 27, "nodeType": "319", "messageId": "320", "endLine": 14, "endColumn": 30, "suggestions": "343"}, {"ruleId": "317", "severity": 1, "message": "318", "line": 18, "column": 54, "nodeType": "319", "messageId": "320", "endLine": 18, "endColumn": 57, "suggestions": "344"}, {"ruleId": "317", "severity": 1, "message": "318", "line": 31, "column": 61, "nodeType": "319", "messageId": "320", "endLine": 31, "endColumn": 64, "suggestions": "345"}, {"ruleId": "317", "severity": 1, "message": "318", "line": 8, "column": 11, "nodeType": "319", "messageId": "320", "endLine": 8, "endColumn": 14, "suggestions": "346"}, {"ruleId": "347", "severity": 1, "message": "348", "line": 57, "column": 51, "nodeType": "349", "messageId": "350", "suggestions": "351"}, {"ruleId": "303", "severity": 1, "message": "352", "line": 11, "column": 10, "nodeType": null, "messageId": "305", "endLine": 11, "endColumn": 22}, {"ruleId": "317", "severity": 1, "message": "318", "line": 19, "column": 69, "nodeType": "319", "messageId": "320", "endLine": 19, "endColumn": 72, "suggestions": "353"}, {"ruleId": "317", "severity": 1, "message": "318", "line": 16, "column": 69, "nodeType": "319", "messageId": "320", "endLine": 16, "endColumn": 72, "suggestions": "354"}, {"ruleId": "303", "severity": 1, "message": "355", "line": 13, "column": 3, "nodeType": null, "messageId": "305", "endLine": 13, "endColumn": 21}, {"ruleId": "303", "severity": 1, "message": "356", "line": 14, "column": 3, "nodeType": null, "messageId": "305", "endLine": 14, "endColumn": 23}, {"ruleId": "317", "severity": 1, "message": "318", "line": 72, "column": 72, "nodeType": "319", "messageId": "320", "endLine": 72, "endColumn": 75, "suggestions": "357"}, {"ruleId": "303", "severity": 1, "message": "358", "line": 142, "column": 14, "nodeType": null, "messageId": "305", "endLine": 142, "endColumn": 19}, {"ruleId": "303", "severity": 1, "message": "309", "line": 9, "column": 43, "nodeType": null, "messageId": "305", "endLine": 9, "endColumn": 47}, {"ruleId": "335", "severity": 1, "message": "336", "line": 79, "column": 23, "nodeType": "337", "endLine": 83, "endColumn": 25}, {"ruleId": "303", "severity": 1, "message": "311", "line": 13, "column": 3, "nodeType": null, "messageId": "305", "endLine": 13, "endColumn": 8}, {"ruleId": "303", "severity": 1, "message": "359", "line": 15, "column": 3, "nodeType": null, "messageId": "305", "endLine": 15, "endColumn": 11}, {"ruleId": "303", "severity": 1, "message": "360", "line": 13, "column": 3, "nodeType": null, "messageId": "305", "endLine": 13, "endColumn": 6}, {"ruleId": "303", "severity": 1, "message": "361", "line": 10, "column": 3, "nodeType": null, "messageId": "305", "endLine": 10, "endColumn": 8}, "@typescript-eslint/no-unused-vars", "'analysisComplete' is assigned a value but never used.", "unusedVar", "'t' is assigned a value but never used.", "'Users' is defined but never used.", "'Calendar' is defined but never used.", "'user' is assigned a value but never used.", "'Filter' is defined but never used.", "'Clock' is defined but never used.", "'Trash2' is defined but never used.", "'TrendingUp' is defined but never used.", "'Plus' is defined but never used.", "'Save' is defined but never used.", "'Flag' is defined but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["362", "363"], ["364", "365"], ["366", "367"], "'Share2' is defined but never used.", ["368", "369"], ["370", "371"], ["372", "373"], "'Palette' is defined but never used.", "'err' is defined but never used.", "'Circle' is defined but never used.", ["374", "375"], ["376", "377"], ["378", "379"], "'CheckCircle' is defined but never used.", "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", ["380", "381"], ["382", "383"], ["384", "385"], ["386", "387"], ["388", "389"], ["390", "391"], ["392", "393"], ["394", "395"], ["396", "397"], "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["398", "399", "400", "401"], "'uploadedFile' is assigned a value but never used.", ["402", "403"], ["404", "405"], "'GoogleAuthProvider' is defined but never used.", "'FacebookAuthProvider' is defined but never used.", ["406", "407"], "'error' is defined but never used.", "'Sparkles' is defined but never used.", "'Zap' is defined but never used.", "'Crown' is defined but never used.", {"messageId": "408", "fix": "409", "desc": "410"}, {"messageId": "411", "fix": "412", "desc": "413"}, {"messageId": "408", "fix": "414", "desc": "410"}, {"messageId": "411", "fix": "415", "desc": "413"}, {"messageId": "408", "fix": "416", "desc": "410"}, {"messageId": "411", "fix": "417", "desc": "413"}, {"messageId": "408", "fix": "418", "desc": "410"}, {"messageId": "411", "fix": "419", "desc": "413"}, {"messageId": "408", "fix": "420", "desc": "410"}, {"messageId": "411", "fix": "421", "desc": "413"}, {"messageId": "408", "fix": "422", "desc": "410"}, {"messageId": "411", "fix": "423", "desc": "413"}, {"messageId": "408", "fix": "424", "desc": "410"}, {"messageId": "411", "fix": "425", "desc": "413"}, {"messageId": "408", "fix": "426", "desc": "410"}, {"messageId": "411", "fix": "427", "desc": "413"}, {"messageId": "408", "fix": "428", "desc": "410"}, {"messageId": "411", "fix": "429", "desc": "413"}, {"messageId": "408", "fix": "430", "desc": "410"}, {"messageId": "411", "fix": "431", "desc": "413"}, {"messageId": "408", "fix": "432", "desc": "410"}, {"messageId": "411", "fix": "433", "desc": "413"}, {"messageId": "408", "fix": "434", "desc": "410"}, {"messageId": "411", "fix": "435", "desc": "413"}, {"messageId": "408", "fix": "436", "desc": "410"}, {"messageId": "411", "fix": "437", "desc": "413"}, {"messageId": "408", "fix": "438", "desc": "410"}, {"messageId": "411", "fix": "439", "desc": "413"}, {"messageId": "408", "fix": "440", "desc": "410"}, {"messageId": "411", "fix": "441", "desc": "413"}, {"messageId": "408", "fix": "442", "desc": "410"}, {"messageId": "411", "fix": "443", "desc": "413"}, {"messageId": "408", "fix": "444", "desc": "410"}, {"messageId": "411", "fix": "445", "desc": "413"}, {"messageId": "408", "fix": "446", "desc": "410"}, {"messageId": "411", "fix": "447", "desc": "413"}, {"messageId": "448", "data": "449", "fix": "450", "desc": "451"}, {"messageId": "448", "data": "452", "fix": "453", "desc": "454"}, {"messageId": "448", "data": "455", "fix": "456", "desc": "457"}, {"messageId": "448", "data": "458", "fix": "459", "desc": "460"}, {"messageId": "408", "fix": "461", "desc": "410"}, {"messageId": "411", "fix": "462", "desc": "413"}, {"messageId": "408", "fix": "463", "desc": "410"}, {"messageId": "411", "fix": "464", "desc": "413"}, {"messageId": "408", "fix": "465", "desc": "410"}, {"messageId": "411", "fix": "466", "desc": "413"}, "suggestUnknown", {"range": "467", "text": "468"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "469", "text": "470"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "471", "text": "468"}, {"range": "472", "text": "470"}, {"range": "473", "text": "468"}, {"range": "474", "text": "470"}, {"range": "475", "text": "468"}, {"range": "476", "text": "470"}, {"range": "477", "text": "468"}, {"range": "478", "text": "470"}, {"range": "479", "text": "468"}, {"range": "480", "text": "470"}, {"range": "481", "text": "468"}, {"range": "482", "text": "470"}, {"range": "483", "text": "468"}, {"range": "484", "text": "470"}, {"range": "485", "text": "468"}, {"range": "486", "text": "470"}, {"range": "487", "text": "468"}, {"range": "488", "text": "470"}, {"range": "489", "text": "468"}, {"range": "490", "text": "470"}, {"range": "491", "text": "468"}, {"range": "492", "text": "470"}, {"range": "493", "text": "468"}, {"range": "494", "text": "470"}, {"range": "495", "text": "468"}, {"range": "496", "text": "470"}, {"range": "497", "text": "468"}, {"range": "498", "text": "470"}, {"range": "499", "text": "468"}, {"range": "500", "text": "470"}, {"range": "501", "text": "468"}, {"range": "502", "text": "470"}, {"range": "503", "text": "468"}, {"range": "504", "text": "470"}, "replaceWithAlt", {"alt": "505"}, {"range": "506", "text": "507"}, "Replace with `&apos;`.", {"alt": "508"}, {"range": "509", "text": "510"}, "Replace with `&lsquo;`.", {"alt": "511"}, {"range": "512", "text": "513"}, "Replace with `&#39;`.", {"alt": "514"}, {"range": "515", "text": "516"}, "Replace with `&rsquo;`.", {"range": "517", "text": "468"}, {"range": "518", "text": "470"}, {"range": "519", "text": "468"}, {"range": "520", "text": "470"}, {"range": "521", "text": "468"}, {"range": "522", "text": "470"}, [2805, 2808], "unknown", [2805, 2808], "never", [2934, 2937], [2934, 2937], [3947, 3950], [3947, 3950], [505, 508], [505, 508], [524, 527], [524, 527], [541, 544], [541, 544], [303, 306], [303, 306], [443, 446], [443, 446], [496, 499], [496, 499], [112, 115], [112, 115], [691, 694], [691, 694], [121, 124], [121, 124], [154, 157], [154, 157], [240, 243], [240, 243], [354, 357], [354, 357], [494, 497], [494, 497], [1071, 1074], [1071, 1074], [207, 210], [207, 210], "&apos;", [2011, 2048], "Here&apos;s how you performed on your exam", "&lsquo;", [2011, 2048], "Here&lsquo;s how you performed on your exam", "&#39;", [2011, 2048], "Here&#39;s how you performed on your exam", "&rsquo;", [2011, 2048], "Here&rsquo;s how you performed on your exam", [760, 763], [760, 763], [522, 525], [522, 525], [2229, 2232], [2229, 2232]]