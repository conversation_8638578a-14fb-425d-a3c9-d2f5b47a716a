[{"D:\\templete\\pdf-exam-generator\\src\\app\\analyze\\page.tsx": "1", "D:\\templete\\pdf-exam-generator\\src\\app\\auth\\forgot-password\\page.tsx": "2", "D:\\templete\\pdf-exam-generator\\src\\app\\auth\\login\\page.tsx": "3", "D:\\templete\\pdf-exam-generator\\src\\app\\auth\\register\\page.tsx": "4", "D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\analytics\\page.tsx": "5", "D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\exams\\page.tsx": "6", "D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\history\\page.tsx": "7", "D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\page.tsx": "8", "D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\students\\page.tsx": "9", "D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\upload\\page.tsx": "10", "D:\\templete\\pdf-exam-generator\\src\\app\\exam\\page.tsx": "11", "D:\\templete\\pdf-exam-generator\\src\\app\\layout.tsx": "12", "D:\\templete\\pdf-exam-generator\\src\\app\\page.tsx": "13", "D:\\templete\\pdf-exam-generator\\src\\app\\results\\page.tsx": "14", "D:\\templete\\pdf-exam-generator\\src\\app\\settings\\page.tsx": "15", "D:\\templete\\pdf-exam-generator\\src\\components\\ai\\AIAnalysisSummary.tsx": "16", "D:\\templete\\pdf-exam-generator\\src\\components\\auth\\LoginPage.tsx": "17", "D:\\templete\\pdf-exam-generator\\src\\components\\auth\\ProtectedRoute.tsx": "18", "D:\\templete\\pdf-exam-generator\\src\\components\\exam\\ExamConfiguration.tsx": "19", "D:\\templete\\pdf-exam-generator\\src\\components\\exam\\ExamInterface.tsx": "20", "D:\\templete\\pdf-exam-generator\\src\\components\\exam\\ExamProgress.tsx": "21", "D:\\templete\\pdf-exam-generator\\src\\components\\exam\\ExamTimer.tsx": "22", "D:\\templete\\pdf-exam-generator\\src\\components\\layout\\AppContent.tsx": "23", "D:\\templete\\pdf-exam-generator\\src\\components\\layout\\DashboardLayout.tsx": "24", "D:\\templete\\pdf-exam-generator\\src\\components\\layout\\Footer.tsx": "25", "D:\\templete\\pdf-exam-generator\\src\\components\\layout\\Navbar.tsx": "26", "D:\\templete\\pdf-exam-generator\\src\\components\\pages\\LandingPage.tsx": "27", "D:\\templete\\pdf-exam-generator\\src\\components\\pdf\\PDFPreview.tsx": "28", "D:\\templete\\pdf-exam-generator\\src\\components\\results\\ExportResults.tsx": "29", "D:\\templete\\pdf-exam-generator\\src\\components\\results\\PerformanceAnalytics.tsx": "30", "D:\\templete\\pdf-exam-generator\\src\\components\\results\\QuestionReview.tsx": "31", "D:\\templete\\pdf-exam-generator\\src\\components\\results\\ScoreOverview.tsx": "32", "D:\\templete\\pdf-exam-generator\\src\\components\\sections\\CTASection.tsx": "33", "D:\\templete\\pdf-exam-generator\\src\\components\\sections\\FeaturesSection.tsx": "34", "D:\\templete\\pdf-exam-generator\\src\\components\\sections\\HeroSection.tsx": "35", "D:\\templete\\pdf-exam-generator\\src\\components\\sections\\HowItWorksSection.tsx": "36", "D:\\templete\\pdf-exam-generator\\src\\components\\ui\\FileUpload.tsx": "37", "D:\\templete\\pdf-exam-generator\\src\\components\\ui\\LoadingSpinner.tsx": "38", "D:\\templete\\pdf-exam-generator\\src\\components\\upload\\FileUpload.tsx": "39", "D:\\templete\\pdf-exam-generator\\src\\contexts\\AuthContext.tsx": "40", "D:\\templete\\pdf-exam-generator\\src\\lib\\firebase.ts": "41", "D:\\templete\\pdf-exam-generator\\src\\app\\profile\\page.tsx": "42", "D:\\templete\\pdf-exam-generator\\src\\components\\landing\\Features.tsx": "43", "D:\\templete\\pdf-exam-generator\\src\\components\\landing\\Footer.tsx": "44", "D:\\templete\\pdf-exam-generator\\src\\components\\landing\\Header.tsx": "45", "D:\\templete\\pdf-exam-generator\\src\\components\\landing\\Hero.tsx": "46", "D:\\templete\\pdf-exam-generator\\src\\components\\landing\\Pricing.tsx": "47", "D:\\templete\\pdf-exam-generator\\src\\contexts\\LanguageContext.tsx": "48", "D:\\templete\\pdf-exam-generator\\src\\app\\manifest.ts": "49", "D:\\templete\\pdf-exam-generator\\src\\app\\sitemap.ts": "50", "D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\layout.tsx": "51"}, {"size": 3933, "mtime": 1751824179989, "results": "52", "hashOfConfig": "53"}, {"size": 6468, "mtime": 1751836535258, "results": "54", "hashOfConfig": "53"}, {"size": 10584, "mtime": 1751836745010, "results": "55", "hashOfConfig": "53"}, {"size": 14642, "mtime": 1751842603600, "results": "56", "hashOfConfig": "53"}, {"size": 11668, "mtime": 1751836878761, "results": "57", "hashOfConfig": "53"}, {"size": 12714, "mtime": 1751826404711, "results": "58", "hashOfConfig": "53"}, {"size": 15943, "mtime": 1751825955884, "results": "59", "hashOfConfig": "53"}, {"size": 8961, "mtime": 1751840406854, "results": "60", "hashOfConfig": "53"}, {"size": 13198, "mtime": 1751825895760, "results": "61", "hashOfConfig": "53"}, {"size": 7393, "mtime": 1751825625878, "results": "62", "hashOfConfig": "53"}, {"size": 10615, "mtime": 1751837126808, "results": "63", "hashOfConfig": "53"}, {"size": 1628, "mtime": 1751840855299, "results": "64", "hashOfConfig": "53"}, {"size": 785, "mtime": 1751834374493, "results": "65", "hashOfConfig": "53"}, {"size": 7079, "mtime": 1751824685509, "results": "66", "hashOfConfig": "53"}, {"size": 11563, "mtime": 1751835344506, "results": "67", "hashOfConfig": "53"}, {"size": 5989, "mtime": 1751824246344, "results": "68", "hashOfConfig": "53"}, {"size": 12835, "mtime": 1751842573323, "results": "69", "hashOfConfig": "53"}, {"size": 1531, "mtime": 1751834337572, "results": "70", "hashOfConfig": "53"}, {"size": 9172, "mtime": 1751824353308, "results": "71", "hashOfConfig": "53"}, {"size": 10400, "mtime": 1751824398796, "results": "72", "hashOfConfig": "53"}, {"size": 1057, "mtime": 1751824438721, "results": "73", "hashOfConfig": "53"}, {"size": 2486, "mtime": 1751824425298, "results": "74", "hashOfConfig": "53"}, {"size": 2309, "mtime": 1751840138331, "results": "75", "hashOfConfig": "53"}, {"size": 9670, "mtime": 1751840451975, "results": "76", "hashOfConfig": "53"}, {"size": 4796, "mtime": 1751823938057, "results": "77", "hashOfConfig": "53"}, {"size": 9173, "mtime": 1751837428591, "results": "78", "hashOfConfig": "53"}, {"size": 1203, "mtime": 1751840924135, "results": "79", "hashOfConfig": "53"}, {"size": 5052, "mtime": 1751824216897, "results": "80", "hashOfConfig": "53"}, {"size": 5353, "mtime": 1751824832012, "results": "81", "hashOfConfig": "53"}, {"size": 9781, "mtime": 1751824802502, "results": "82", "hashOfConfig": "53"}, {"size": 8496, "mtime": 1751824761830, "results": "83", "hashOfConfig": "53"}, {"size": 8044, "mtime": 1751824724277, "results": "84", "hashOfConfig": "53"}, {"size": 5810, "mtime": 1751825476712, "results": "85", "hashOfConfig": "53"}, {"size": 4373, "mtime": 1751825446906, "results": "86", "hashOfConfig": "53"}, {"size": 5974, "mtime": 1751834447656, "results": "87", "hashOfConfig": "53"}, {"size": 5808, "mtime": 1751824124682, "results": "88", "hashOfConfig": "53"}, {"size": 6941, "mtime": 1751824064879, "results": "89", "hashOfConfig": "53"}, {"size": 1023, "mtime": 1751834350798, "results": "90", "hashOfConfig": "53"}, {"size": 4227, "mtime": 1751826451060, "results": "91", "hashOfConfig": "53"}, {"size": 8593, "mtime": 1751842504545, "results": "92", "hashOfConfig": "53"}, {"size": 1618, "mtime": 1751838604089, "results": "93", "hashOfConfig": "53"}, {"size": 11221, "mtime": 1751837190476, "results": "94", "hashOfConfig": "53"}, {"size": 8629, "mtime": 1751839354562, "results": "95", "hashOfConfig": "53"}, {"size": 7964, "mtime": 1751839439398, "results": "96", "hashOfConfig": "53"}, {"size": 8430, "mtime": 1751839264013, "results": "97", "hashOfConfig": "53"}, {"size": 9687, "mtime": 1751839311600, "results": "98", "hashOfConfig": "53"}, {"size": 9068, "mtime": 1751839399622, "results": "99", "hashOfConfig": "53"}, {"size": 13216, "mtime": 1751840284505, "results": "100", "hashOfConfig": "53"}, {"size": 877, "mtime": 1751841063512, "results": "101", "hashOfConfig": "53"}, {"size": 626, "mtime": 1751841075850, "results": "102", "hashOfConfig": "53"}, {"size": 379, "mtime": 1751842678047, "results": "103", "hashOfConfig": "53"}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "1pxi7og", {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\templete\\pdf-exam-generator\\src\\app\\analyze\\page.tsx", ["257"], [], "D:\\templete\\pdf-exam-generator\\src\\app\\auth\\forgot-password\\page.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\app\\auth\\login\\page.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\app\\auth\\register\\page.tsx", ["258", "259", "260"], [], "D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\analytics\\page.tsx", ["261", "262", "263"], [], "D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\exams\\page.tsx", ["264", "265", "266", "267"], [], "D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\history\\page.tsx", ["268", "269"], [], "D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\page.tsx", ["270", "271"], [], "D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\students\\page.tsx", ["272", "273", "274", "275"], [], "D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\upload\\page.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\app\\exam\\page.tsx", ["276", "277", "278", "279", "280"], [], "D:\\templete\\pdf-exam-generator\\src\\app\\layout.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\app\\page.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\app\\results\\page.tsx", ["281", "282", "283", "284"], [], "D:\\templete\\pdf-exam-generator\\src\\app\\settings\\page.tsx", ["285"], [], "D:\\templete\\pdf-exam-generator\\src\\components\\ai\\AIAnalysisSummary.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\components\\auth\\LoginPage.tsx", ["286", "287", "288"], [], "D:\\templete\\pdf-exam-generator\\src\\components\\auth\\ProtectedRoute.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\components\\exam\\ExamConfiguration.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\components\\exam\\ExamInterface.tsx", ["289", "290", "291", "292"], [], "D:\\templete\\pdf-exam-generator\\src\\components\\exam\\ExamProgress.tsx", ["293"], [], "D:\\templete\\pdf-exam-generator\\src\\components\\exam\\ExamTimer.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\components\\layout\\AppContent.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\components\\layout\\DashboardLayout.tsx", ["294", "295"], [], "D:\\templete\\pdf-exam-generator\\src\\components\\layout\\Footer.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\components\\layout\\Navbar.tsx", ["296", "297"], [], "D:\\templete\\pdf-exam-generator\\src\\components\\pages\\LandingPage.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\components\\pdf\\PDFPreview.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\components\\results\\ExportResults.tsx", ["298", "299"], [], "D:\\templete\\pdf-exam-generator\\src\\components\\results\\PerformanceAnalytics.tsx", ["300", "301", "302"], [], "D:\\templete\\pdf-exam-generator\\src\\components\\results\\QuestionReview.tsx", ["303", "304", "305", "306"], [], "D:\\templete\\pdf-exam-generator\\src\\components\\results\\ScoreOverview.tsx", ["307", "308"], [], "D:\\templete\\pdf-exam-generator\\src\\components\\sections\\CTASection.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\components\\sections\\FeaturesSection.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\components\\sections\\HeroSection.tsx", ["309"], [], "D:\\templete\\pdf-exam-generator\\src\\components\\sections\\HowItWorksSection.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\components\\ui\\FileUpload.tsx", ["310"], [], "D:\\templete\\pdf-exam-generator\\src\\components\\ui\\LoadingSpinner.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\components\\upload\\FileUpload.tsx", ["311"], [], "D:\\templete\\pdf-exam-generator\\src\\contexts\\AuthContext.tsx", ["312", "313", "314", "315", "316", "317"], [], "D:\\templete\\pdf-exam-generator\\src\\lib\\firebase.ts", [], [], "D:\\templete\\pdf-exam-generator\\src\\app\\profile\\page.tsx", ["318", "319"], [], "D:\\templete\\pdf-exam-generator\\src\\components\\landing\\Features.tsx", ["320", "321"], [], "D:\\templete\\pdf-exam-generator\\src\\components\\landing\\Footer.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\components\\landing\\Header.tsx", ["322"], [], "D:\\templete\\pdf-exam-generator\\src\\components\\landing\\Hero.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\components\\landing\\Pricing.tsx", ["323"], [], "D:\\templete\\pdf-exam-generator\\src\\contexts\\LanguageContext.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\app\\manifest.ts", [], [], "D:\\templete\\pdf-exam-generator\\src\\app\\sitemap.ts", [], [], "D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\layout.tsx", [], [], {"ruleId": "324", "severity": 1, "message": "325", "line": 21, "column": 10, "nodeType": null, "messageId": "326", "endLine": 21, "endColumn": 26}, {"ruleId": "324", "severity": 1, "message": "327", "line": 24, "column": 11, "nodeType": null, "messageId": "326", "endLine": 24, "endColumn": 12}, {"ruleId": "328", "severity": 1, "message": "329", "line": 73, "column": 21, "nodeType": "330", "messageId": "331", "endLine": 73, "endColumn": 24, "suggestions": "332"}, {"ruleId": "328", "severity": 1, "message": "329", "line": 92, "column": 21, "nodeType": "330", "messageId": "331", "endLine": 92, "endColumn": 24, "suggestions": "333"}, {"ruleId": "324", "severity": 1, "message": "334", "line": 8, "column": 3, "nodeType": null, "messageId": "326", "endLine": 8, "endColumn": 8}, {"ruleId": "324", "severity": 1, "message": "335", "line": 12, "column": 3, "nodeType": null, "messageId": "326", "endLine": 12, "endColumn": 11}, {"ruleId": "324", "severity": 1, "message": "336", "line": 20, "column": 11, "nodeType": null, "messageId": "326", "endLine": 20, "endColumn": 15}, {"ruleId": "324", "severity": 1, "message": "337", "line": 8, "column": 3, "nodeType": null, "messageId": "326", "endLine": 8, "endColumn": 9}, {"ruleId": "324", "severity": 1, "message": "338", "line": 12, "column": 3, "nodeType": null, "messageId": "326", "endLine": 12, "endColumn": 8}, {"ruleId": "324", "severity": 1, "message": "339", "line": 17, "column": 3, "nodeType": null, "messageId": "326", "endLine": 17, "endColumn": 9}, {"ruleId": "324", "severity": 1, "message": "336", "line": 25, "column": 11, "nodeType": null, "messageId": "326", "endLine": 25, "endColumn": 15}, {"ruleId": "324", "severity": 1, "message": "337", "line": 11, "column": 3, "nodeType": null, "messageId": "326", "endLine": 11, "endColumn": 9}, {"ruleId": "324", "severity": 1, "message": "336", "line": 23, "column": 11, "nodeType": null, "messageId": "326", "endLine": 23, "endColumn": 15}, {"ruleId": "324", "severity": 1, "message": "340", "line": 9, "column": 3, "nodeType": null, "messageId": "326", "endLine": 9, "endColumn": 13}, {"ruleId": "324", "severity": 1, "message": "335", "line": 14, "column": 3, "nodeType": null, "messageId": "326", "endLine": 14, "endColumn": 11}, {"ruleId": "324", "severity": 1, "message": "337", "line": 8, "column": 3, "nodeType": null, "messageId": "326", "endLine": 8, "endColumn": 9}, {"ruleId": "324", "severity": 1, "message": "341", "line": 9, "column": 3, "nodeType": null, "messageId": "326", "endLine": 9, "endColumn": 7}, {"ruleId": "324", "severity": 1, "message": "335", "line": 13, "column": 3, "nodeType": null, "messageId": "326", "endLine": 13, "endColumn": 11}, {"ruleId": "324", "severity": 1, "message": "336", "line": 24, "column": 11, "nodeType": null, "messageId": "326", "endLine": 24, "endColumn": 15}, {"ruleId": "324", "severity": 1, "message": "342", "line": 8, "column": 21, "nodeType": null, "messageId": "326", "endLine": 8, "endColumn": 25}, {"ruleId": "324", "severity": 1, "message": "343", "line": 8, "column": 27, "nodeType": null, "messageId": "326", "endLine": 8, "endColumn": 31}, {"ruleId": "328", "severity": 1, "message": "329", "line": 63, "column": 48, "nodeType": "330", "messageId": "331", "endLine": 63, "endColumn": 51, "suggestions": "344"}, {"ruleId": "328", "severity": 1, "message": "329", "line": 65, "column": 57, "nodeType": "330", "messageId": "331", "endLine": 65, "endColumn": 60, "suggestions": "345"}, {"ruleId": "328", "severity": 1, "message": "329", "line": 97, "column": 59, "nodeType": "330", "messageId": "331", "endLine": 97, "endColumn": 62, "suggestions": "346"}, {"ruleId": "324", "severity": 1, "message": "347", "line": 9, "column": 32, "nodeType": null, "messageId": "326", "endLine": 9, "endColumn": 38}, {"ruleId": "328", "severity": 1, "message": "329", "line": 12, "column": 27, "nodeType": "330", "messageId": "331", "endLine": 12, "endColumn": 30, "suggestions": "348"}, {"ruleId": "328", "severity": 1, "message": "329", "line": 13, "column": 14, "nodeType": "330", "messageId": "331", "endLine": 13, "endColumn": 17, "suggestions": "349"}, {"ruleId": "328", "severity": 1, "message": "329", "line": 14, "column": 11, "nodeType": "330", "messageId": "331", "endLine": 14, "endColumn": 14, "suggestions": "350"}, {"ruleId": "324", "severity": 1, "message": "351", "line": 6, "column": 47, "nodeType": null, "messageId": "326", "endLine": 6, "endColumn": 54}, {"ruleId": "324", "severity": 1, "message": "352", "line": 33, "column": 14, "nodeType": null, "messageId": "326", "endLine": 33, "endColumn": 17}, {"ruleId": "328", "severity": 1, "message": "329", "line": 50, "column": 19, "nodeType": "330", "messageId": "331", "endLine": 50, "endColumn": 22, "suggestions": "353"}, {"ruleId": "328", "severity": 1, "message": "329", "line": 68, "column": 19, "nodeType": "330", "messageId": "331", "endLine": 68, "endColumn": 22, "suggestions": "354"}, {"ruleId": "324", "severity": 1, "message": "355", "line": 4, "column": 56, "nodeType": null, "messageId": "326", "endLine": 4, "endColumn": 62}, {"ruleId": "328", "severity": 1, "message": "329", "line": 11, "column": 18, "nodeType": "330", "messageId": "331", "endLine": 11, "endColumn": 21, "suggestions": "356"}, {"ruleId": "328", "severity": 1, "message": "329", "line": 18, "column": 27, "nodeType": "330", "messageId": "331", "endLine": 18, "endColumn": 30, "suggestions": "357"}, {"ruleId": "328", "severity": 1, "message": "329", "line": 19, "column": 48, "nodeType": "330", "messageId": "331", "endLine": 19, "endColumn": 51, "suggestions": "358"}, {"ruleId": "324", "severity": 1, "message": "359", "line": 1, "column": 10, "nodeType": null, "messageId": "326", "endLine": 1, "endColumn": 21}, {"ruleId": "360", "severity": 1, "message": "361", "line": 87, "column": 17, "nodeType": "362", "endLine": 87, "endColumn": 126}, {"ruleId": "360", "severity": 1, "message": "361", "line": 208, "column": 21, "nodeType": "362", "endLine": 208, "endColumn": 128}, {"ruleId": "360", "severity": 1, "message": "361", "line": 84, "column": 23, "nodeType": "362", "endLine": 84, "endColumn": 130}, {"ruleId": "360", "severity": 1, "message": "361", "line": 175, "column": 23, "nodeType": "362", "endLine": 175, "endColumn": 130}, {"ruleId": "328", "severity": 1, "message": "329", "line": 4, "column": 16, "nodeType": "330", "messageId": "331", "endLine": 4, "endColumn": 19, "suggestions": "363"}, {"ruleId": "328", "severity": 1, "message": "329", "line": 24, "column": 33, "nodeType": "330", "messageId": "331", "endLine": 24, "endColumn": 36, "suggestions": "364"}, {"ruleId": "324", "severity": 1, "message": "338", "line": 1, "column": 41, "nodeType": null, "messageId": "326", "endLine": 1, "endColumn": 46}, {"ruleId": "328", "severity": 1, "message": "329", "line": 4, "column": 14, "nodeType": "330", "messageId": "331", "endLine": 4, "endColumn": 17, "suggestions": "365"}, {"ruleId": "328", "severity": 1, "message": "329", "line": 5, "column": 27, "nodeType": "330", "messageId": "331", "endLine": 5, "endColumn": 30, "suggestions": "366"}, {"ruleId": "328", "severity": 1, "message": "329", "line": 8, "column": 18, "nodeType": "330", "messageId": "331", "endLine": 8, "endColumn": 21, "suggestions": "367"}, {"ruleId": "328", "severity": 1, "message": "329", "line": 14, "column": 27, "nodeType": "330", "messageId": "331", "endLine": 14, "endColumn": 30, "suggestions": "368"}, {"ruleId": "328", "severity": 1, "message": "329", "line": 18, "column": 54, "nodeType": "330", "messageId": "331", "endLine": 18, "endColumn": 57, "suggestions": "369"}, {"ruleId": "328", "severity": 1, "message": "329", "line": 31, "column": 61, "nodeType": "330", "messageId": "331", "endLine": 31, "endColumn": 64, "suggestions": "370"}, {"ruleId": "328", "severity": 1, "message": "329", "line": 8, "column": 11, "nodeType": "330", "messageId": "331", "endLine": 8, "endColumn": 14, "suggestions": "371"}, {"ruleId": "372", "severity": 1, "message": "373", "line": 57, "column": 51, "nodeType": "374", "messageId": "375", "suggestions": "376"}, {"ruleId": "324", "severity": 1, "message": "377", "line": 11, "column": 10, "nodeType": null, "messageId": "326", "endLine": 11, "endColumn": 22}, {"ruleId": "328", "severity": 1, "message": "329", "line": 19, "column": 69, "nodeType": "330", "messageId": "331", "endLine": 19, "endColumn": 72, "suggestions": "378"}, {"ruleId": "328", "severity": 1, "message": "329", "line": 16, "column": 69, "nodeType": "330", "messageId": "331", "endLine": 16, "endColumn": 72, "suggestions": "379"}, {"ruleId": "324", "severity": 1, "message": "380", "line": 13, "column": 3, "nodeType": null, "messageId": "326", "endLine": 13, "endColumn": 21}, {"ruleId": "324", "severity": 1, "message": "381", "line": 14, "column": 3, "nodeType": null, "messageId": "326", "endLine": 14, "endColumn": 23}, {"ruleId": "328", "severity": 1, "message": "329", "line": 72, "column": 72, "nodeType": "330", "messageId": "331", "endLine": 72, "endColumn": 75, "suggestions": "382"}, {"ruleId": "324", "severity": 1, "message": "383", "line": 142, "column": 14, "nodeType": null, "messageId": "326", "endLine": 142, "endColumn": 19}, {"ruleId": "328", "severity": 1, "message": "329", "line": 173, "column": 21, "nodeType": "330", "messageId": "331", "endLine": 173, "endColumn": 24, "suggestions": "384"}, {"ruleId": "328", "severity": 1, "message": "329", "line": 196, "column": 21, "nodeType": "330", "messageId": "331", "endLine": 196, "endColumn": 24, "suggestions": "385"}, {"ruleId": "324", "severity": 1, "message": "336", "line": 9, "column": 43, "nodeType": null, "messageId": "326", "endLine": 9, "endColumn": 47}, {"ruleId": "360", "severity": 1, "message": "361", "line": 79, "column": 23, "nodeType": "362", "endLine": 83, "endColumn": 25}, {"ruleId": "324", "severity": 1, "message": "338", "line": 13, "column": 3, "nodeType": null, "messageId": "326", "endLine": 13, "endColumn": 8}, {"ruleId": "324", "severity": 1, "message": "386", "line": 15, "column": 3, "nodeType": null, "messageId": "326", "endLine": 15, "endColumn": 11}, {"ruleId": "324", "severity": 1, "message": "387", "line": 13, "column": 3, "nodeType": null, "messageId": "326", "endLine": 13, "endColumn": 6}, {"ruleId": "324", "severity": 1, "message": "388", "line": 10, "column": 3, "nodeType": null, "messageId": "326", "endLine": 10, "endColumn": 8}, "@typescript-eslint/no-unused-vars", "'analysisComplete' is assigned a value but never used.", "unusedVar", "'t' is assigned a value but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["389", "390"], ["391", "392"], "'Users' is defined but never used.", "'Calendar' is defined but never used.", "'user' is assigned a value but never used.", "'Filter' is defined but never used.", "'Clock' is defined but never used.", "'Trash2' is defined but never used.", "'TrendingUp' is defined but never used.", "'Plus' is defined but never used.", "'Save' is defined but never used.", "'Flag' is defined but never used.", ["393", "394"], ["395", "396"], ["397", "398"], "'Share2' is defined but never used.", ["399", "400"], ["401", "402"], ["403", "404"], "'Palette' is defined but never used.", "'err' is defined but never used.", ["405", "406"], ["407", "408"], "'Circle' is defined but never used.", ["409", "410"], ["411", "412"], ["413", "414"], "'CheckCircle' is defined but never used.", "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", ["415", "416"], ["417", "418"], ["419", "420"], ["421", "422"], ["423", "424"], ["425", "426"], ["427", "428"], ["429", "430"], ["431", "432"], "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["433", "434", "435", "436"], "'uploadedFile' is assigned a value but never used.", ["437", "438"], ["439", "440"], "'GoogleAuthProvider' is defined but never used.", "'FacebookAuthProvider' is defined but never used.", ["441", "442"], "'error' is defined but never used.", ["443", "444"], ["445", "446"], "'Sparkles' is defined but never used.", "'Zap' is defined but never used.", "'Crown' is defined but never used.", {"messageId": "447", "fix": "448", "desc": "449"}, {"messageId": "450", "fix": "451", "desc": "452"}, {"messageId": "447", "fix": "453", "desc": "449"}, {"messageId": "450", "fix": "454", "desc": "452"}, {"messageId": "447", "fix": "455", "desc": "449"}, {"messageId": "450", "fix": "456", "desc": "452"}, {"messageId": "447", "fix": "457", "desc": "449"}, {"messageId": "450", "fix": "458", "desc": "452"}, {"messageId": "447", "fix": "459", "desc": "449"}, {"messageId": "450", "fix": "460", "desc": "452"}, {"messageId": "447", "fix": "461", "desc": "449"}, {"messageId": "450", "fix": "462", "desc": "452"}, {"messageId": "447", "fix": "463", "desc": "449"}, {"messageId": "450", "fix": "464", "desc": "452"}, {"messageId": "447", "fix": "465", "desc": "449"}, {"messageId": "450", "fix": "466", "desc": "452"}, {"messageId": "447", "fix": "467", "desc": "449"}, {"messageId": "450", "fix": "468", "desc": "452"}, {"messageId": "447", "fix": "469", "desc": "449"}, {"messageId": "450", "fix": "470", "desc": "452"}, {"messageId": "447", "fix": "471", "desc": "449"}, {"messageId": "450", "fix": "472", "desc": "452"}, {"messageId": "447", "fix": "473", "desc": "449"}, {"messageId": "450", "fix": "474", "desc": "452"}, {"messageId": "447", "fix": "475", "desc": "449"}, {"messageId": "450", "fix": "476", "desc": "452"}, {"messageId": "447", "fix": "477", "desc": "449"}, {"messageId": "450", "fix": "478", "desc": "452"}, {"messageId": "447", "fix": "479", "desc": "449"}, {"messageId": "450", "fix": "480", "desc": "452"}, {"messageId": "447", "fix": "481", "desc": "449"}, {"messageId": "450", "fix": "482", "desc": "452"}, {"messageId": "447", "fix": "483", "desc": "449"}, {"messageId": "450", "fix": "484", "desc": "452"}, {"messageId": "447", "fix": "485", "desc": "449"}, {"messageId": "450", "fix": "486", "desc": "452"}, {"messageId": "447", "fix": "487", "desc": "449"}, {"messageId": "450", "fix": "488", "desc": "452"}, {"messageId": "447", "fix": "489", "desc": "449"}, {"messageId": "450", "fix": "490", "desc": "452"}, {"messageId": "447", "fix": "491", "desc": "449"}, {"messageId": "450", "fix": "492", "desc": "452"}, {"messageId": "447", "fix": "493", "desc": "449"}, {"messageId": "450", "fix": "494", "desc": "452"}, {"messageId": "495", "data": "496", "fix": "497", "desc": "498"}, {"messageId": "495", "data": "499", "fix": "500", "desc": "501"}, {"messageId": "495", "data": "502", "fix": "503", "desc": "504"}, {"messageId": "495", "data": "505", "fix": "506", "desc": "507"}, {"messageId": "447", "fix": "508", "desc": "449"}, {"messageId": "450", "fix": "509", "desc": "452"}, {"messageId": "447", "fix": "510", "desc": "449"}, {"messageId": "450", "fix": "511", "desc": "452"}, {"messageId": "447", "fix": "512", "desc": "449"}, {"messageId": "450", "fix": "513", "desc": "452"}, {"messageId": "447", "fix": "514", "desc": "449"}, {"messageId": "450", "fix": "515", "desc": "452"}, {"messageId": "447", "fix": "516", "desc": "449"}, {"messageId": "450", "fix": "517", "desc": "452"}, "suggestUnknown", {"range": "518", "text": "519"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "520", "text": "521"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "522", "text": "519"}, {"range": "523", "text": "521"}, {"range": "524", "text": "519"}, {"range": "525", "text": "521"}, {"range": "526", "text": "519"}, {"range": "527", "text": "521"}, {"range": "528", "text": "519"}, {"range": "529", "text": "521"}, {"range": "530", "text": "519"}, {"range": "531", "text": "521"}, {"range": "532", "text": "519"}, {"range": "533", "text": "521"}, {"range": "534", "text": "519"}, {"range": "535", "text": "521"}, {"range": "536", "text": "519"}, {"range": "537", "text": "521"}, {"range": "538", "text": "519"}, {"range": "539", "text": "521"}, {"range": "540", "text": "519"}, {"range": "541", "text": "521"}, {"range": "542", "text": "519"}, {"range": "543", "text": "521"}, {"range": "544", "text": "519"}, {"range": "545", "text": "521"}, {"range": "546", "text": "519"}, {"range": "547", "text": "521"}, {"range": "548", "text": "519"}, {"range": "549", "text": "521"}, {"range": "550", "text": "519"}, {"range": "551", "text": "521"}, {"range": "552", "text": "519"}, {"range": "553", "text": "521"}, {"range": "554", "text": "519"}, {"range": "555", "text": "521"}, {"range": "556", "text": "519"}, {"range": "557", "text": "521"}, {"range": "558", "text": "519"}, {"range": "559", "text": "521"}, {"range": "560", "text": "519"}, {"range": "561", "text": "521"}, {"range": "562", "text": "519"}, {"range": "563", "text": "521"}, "replaceWithAlt", {"alt": "564"}, {"range": "565", "text": "566"}, "Replace with `&apos;`.", {"alt": "567"}, {"range": "568", "text": "569"}, "Replace with `&lsquo;`.", {"alt": "570"}, {"range": "571", "text": "572"}, "Replace with `&#39;`.", {"alt": "573"}, {"range": "574", "text": "575"}, "Replace with `&rsquo;`.", {"range": "576", "text": "519"}, {"range": "577", "text": "521"}, {"range": "578", "text": "519"}, {"range": "579", "text": "521"}, {"range": "580", "text": "519"}, {"range": "581", "text": "521"}, {"range": "582", "text": "519"}, {"range": "583", "text": "521"}, {"range": "584", "text": "519"}, {"range": "585", "text": "521"}, [2156, 2159], "unknown", [2156, 2159], "never", [2726, 2729], [2726, 2729], [2805, 2808], [2805, 2808], [2934, 2937], [2934, 2937], [3947, 3950], [3947, 3950], [505, 508], [505, 508], [524, 527], [524, 527], [541, 544], [541, 544], [1551, 1554], [1551, 1554], [2114, 2117], [2114, 2117], [303, 306], [303, 306], [443, 446], [443, 446], [496, 499], [496, 499], [112, 115], [112, 115], [691, 694], [691, 694], [121, 124], [121, 124], [154, 157], [154, 157], [240, 243], [240, 243], [354, 357], [354, 357], [494, 497], [494, 497], [1071, 1074], [1071, 1074], [207, 210], [207, 210], "&apos;", [2011, 2048], "Here&apos;s how you performed on your exam", "&lsquo;", [2011, 2048], "Here&lsquo;s how you performed on your exam", "&#39;", [2011, 2048], "Here&#39;s how you performed on your exam", "&rsquo;", [2011, 2048], "Here&rsquo;s how you performed on your exam", [760, 763], [760, 763], [522, 525], [522, 525], [2229, 2232], [2229, 2232], [5079, 5082], [5079, 5082], [5939, 5942], [5939, 5942]]