[{"D:\\templete\\pdf-exam-generator\\src\\app\\analyze\\page.tsx": "1", "D:\\templete\\pdf-exam-generator\\src\\app\\auth\\forgot-password\\page.tsx": "2", "D:\\templete\\pdf-exam-generator\\src\\app\\auth\\login\\page.tsx": "3", "D:\\templete\\pdf-exam-generator\\src\\app\\auth\\register\\page.tsx": "4", "D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\analytics\\page.tsx": "5", "D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\exams\\page.tsx": "6", "D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\history\\page.tsx": "7", "D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\page.tsx": "8", "D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\students\\page.tsx": "9", "D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\upload\\page.tsx": "10", "D:\\templete\\pdf-exam-generator\\src\\app\\exam\\page.tsx": "11", "D:\\templete\\pdf-exam-generator\\src\\app\\layout.tsx": "12", "D:\\templete\\pdf-exam-generator\\src\\app\\page.tsx": "13", "D:\\templete\\pdf-exam-generator\\src\\app\\results\\page.tsx": "14", "D:\\templete\\pdf-exam-generator\\src\\app\\settings\\page.tsx": "15", "D:\\templete\\pdf-exam-generator\\src\\components\\ai\\AIAnalysisSummary.tsx": "16", "D:\\templete\\pdf-exam-generator\\src\\components\\auth\\LoginPage.tsx": "17", "D:\\templete\\pdf-exam-generator\\src\\components\\auth\\ProtectedRoute.tsx": "18", "D:\\templete\\pdf-exam-generator\\src\\components\\exam\\ExamConfiguration.tsx": "19", "D:\\templete\\pdf-exam-generator\\src\\components\\exam\\ExamInterface.tsx": "20", "D:\\templete\\pdf-exam-generator\\src\\components\\exam\\ExamProgress.tsx": "21", "D:\\templete\\pdf-exam-generator\\src\\components\\exam\\ExamTimer.tsx": "22", "D:\\templete\\pdf-exam-generator\\src\\components\\layout\\AppContent.tsx": "23", "D:\\templete\\pdf-exam-generator\\src\\components\\layout\\DashboardLayout.tsx": "24", "D:\\templete\\pdf-exam-generator\\src\\components\\layout\\Footer.tsx": "25", "D:\\templete\\pdf-exam-generator\\src\\components\\layout\\Navbar.tsx": "26", "D:\\templete\\pdf-exam-generator\\src\\components\\pages\\LandingPage.tsx": "27", "D:\\templete\\pdf-exam-generator\\src\\components\\pdf\\PDFPreview.tsx": "28", "D:\\templete\\pdf-exam-generator\\src\\components\\results\\ExportResults.tsx": "29", "D:\\templete\\pdf-exam-generator\\src\\components\\results\\PerformanceAnalytics.tsx": "30", "D:\\templete\\pdf-exam-generator\\src\\components\\results\\QuestionReview.tsx": "31", "D:\\templete\\pdf-exam-generator\\src\\components\\results\\ScoreOverview.tsx": "32", "D:\\templete\\pdf-exam-generator\\src\\components\\sections\\CTASection.tsx": "33", "D:\\templete\\pdf-exam-generator\\src\\components\\sections\\FeaturesSection.tsx": "34", "D:\\templete\\pdf-exam-generator\\src\\components\\sections\\HeroSection.tsx": "35", "D:\\templete\\pdf-exam-generator\\src\\components\\sections\\HowItWorksSection.tsx": "36", "D:\\templete\\pdf-exam-generator\\src\\components\\ui\\FileUpload.tsx": "37", "D:\\templete\\pdf-exam-generator\\src\\components\\ui\\LoadingSpinner.tsx": "38", "D:\\templete\\pdf-exam-generator\\src\\components\\upload\\FileUpload.tsx": "39", "D:\\templete\\pdf-exam-generator\\src\\contexts\\AuthContext.tsx": "40", "D:\\templete\\pdf-exam-generator\\src\\lib\\firebase.ts": "41", "D:\\templete\\pdf-exam-generator\\src\\app\\profile\\page.tsx": "42"}, {"size": 3933, "mtime": 1751824179989, "results": "43", "hashOfConfig": "44"}, {"size": 6468, "mtime": 1751836535258, "results": "45", "hashOfConfig": "44"}, {"size": 10584, "mtime": 1751836745010, "results": "46", "hashOfConfig": "44"}, {"size": 14173, "mtime": 1751836616731, "results": "47", "hashOfConfig": "44"}, {"size": 11668, "mtime": 1751836878761, "results": "48", "hashOfConfig": "44"}, {"size": 12714, "mtime": 1751826404711, "results": "49", "hashOfConfig": "44"}, {"size": 15943, "mtime": 1751825955884, "results": "50", "hashOfConfig": "44"}, {"size": 8118, "mtime": 1751837014821, "results": "51", "hashOfConfig": "44"}, {"size": 13198, "mtime": 1751825895760, "results": "52", "hashOfConfig": "44"}, {"size": 7393, "mtime": 1751825625878, "results": "53", "hashOfConfig": "44"}, {"size": 10615, "mtime": 1751837126808, "results": "54", "hashOfConfig": "44"}, {"size": 1111, "mtime": 1751826329741, "results": "55", "hashOfConfig": "44"}, {"size": 785, "mtime": 1751834374493, "results": "56", "hashOfConfig": "44"}, {"size": 7079, "mtime": 1751824685509, "results": "57", "hashOfConfig": "44"}, {"size": 11563, "mtime": 1751835344506, "results": "58", "hashOfConfig": "44"}, {"size": 5989, "mtime": 1751824246344, "results": "59", "hashOfConfig": "44"}, {"size": 8875, "mtime": 1751825402790, "results": "60", "hashOfConfig": "44"}, {"size": 1531, "mtime": 1751834337572, "results": "61", "hashOfConfig": "44"}, {"size": 9172, "mtime": 1751824353308, "results": "62", "hashOfConfig": "44"}, {"size": 10400, "mtime": 1751824398796, "results": "63", "hashOfConfig": "44"}, {"size": 1057, "mtime": 1751824438721, "results": "64", "hashOfConfig": "44"}, {"size": 2486, "mtime": 1751824425298, "results": "65", "hashOfConfig": "44"}, {"size": 1998, "mtime": 1751834607725, "results": "66", "hashOfConfig": "44"}, {"size": 8133, "mtime": 1751837348136, "results": "67", "hashOfConfig": "44"}, {"size": 4796, "mtime": 1751823938057, "results": "68", "hashOfConfig": "44"}, {"size": 9173, "mtime": 1751837428591, "results": "69", "hashOfConfig": "44"}, {"size": 472, "mtime": 1751825417897, "results": "70", "hashOfConfig": "44"}, {"size": 5052, "mtime": 1751824216897, "results": "71", "hashOfConfig": "44"}, {"size": 5353, "mtime": 1751824832012, "results": "72", "hashOfConfig": "44"}, {"size": 9781, "mtime": 1751824802502, "results": "73", "hashOfConfig": "44"}, {"size": 8496, "mtime": 1751824761830, "results": "74", "hashOfConfig": "44"}, {"size": 8044, "mtime": 1751824724277, "results": "75", "hashOfConfig": "44"}, {"size": 5810, "mtime": 1751825476712, "results": "76", "hashOfConfig": "44"}, {"size": 4373, "mtime": 1751825446906, "results": "77", "hashOfConfig": "44"}, {"size": 5974, "mtime": 1751834447656, "results": "78", "hashOfConfig": "44"}, {"size": 5808, "mtime": 1751824124682, "results": "79", "hashOfConfig": "44"}, {"size": 6941, "mtime": 1751824064879, "results": "80", "hashOfConfig": "44"}, {"size": 1023, "mtime": 1751834350798, "results": "81", "hashOfConfig": "44"}, {"size": 4227, "mtime": 1751826451060, "results": "82", "hashOfConfig": "44"}, {"size": 7433, "mtime": 1751834115990, "results": "83", "hashOfConfig": "44"}, {"size": 1618, "mtime": 1751838604089, "results": "84", "hashOfConfig": "44"}, {"size": 11221, "mtime": 1751837190476, "results": "85", "hashOfConfig": "44"}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "1pxi7og", {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\templete\\pdf-exam-generator\\src\\app\\analyze\\page.tsx", ["212"], [], "D:\\templete\\pdf-exam-generator\\src\\app\\auth\\forgot-password\\page.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\app\\auth\\login\\page.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\app\\auth\\register\\page.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\analytics\\page.tsx", ["213", "214", "215"], [], "D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\exams\\page.tsx", ["216", "217", "218", "219"], [], "D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\history\\page.tsx", ["220", "221"], [], "D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\page.tsx", ["222", "223"], [], "D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\students\\page.tsx", ["224", "225", "226", "227"], [], "D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\upload\\page.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\app\\exam\\page.tsx", ["228", "229", "230", "231", "232"], [], "D:\\templete\\pdf-exam-generator\\src\\app\\layout.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\app\\page.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\app\\results\\page.tsx", ["233", "234", "235", "236"], [], "D:\\templete\\pdf-exam-generator\\src\\app\\settings\\page.tsx", ["237"], [], "D:\\templete\\pdf-exam-generator\\src\\components\\ai\\AIAnalysisSummary.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\components\\auth\\LoginPage.tsx", ["238"], [], "D:\\templete\\pdf-exam-generator\\src\\components\\auth\\ProtectedRoute.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\components\\exam\\ExamConfiguration.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\components\\exam\\ExamInterface.tsx", ["239", "240", "241", "242"], [], "D:\\templete\\pdf-exam-generator\\src\\components\\exam\\ExamProgress.tsx", ["243"], [], "D:\\templete\\pdf-exam-generator\\src\\components\\exam\\ExamTimer.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\components\\layout\\AppContent.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\components\\layout\\DashboardLayout.tsx", ["244", "245", "246", "247"], [], "D:\\templete\\pdf-exam-generator\\src\\components\\layout\\Footer.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\components\\layout\\Navbar.tsx", ["248", "249"], [], "D:\\templete\\pdf-exam-generator\\src\\components\\pages\\LandingPage.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\components\\pdf\\PDFPreview.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\components\\results\\ExportResults.tsx", ["250", "251"], [], "D:\\templete\\pdf-exam-generator\\src\\components\\results\\PerformanceAnalytics.tsx", ["252", "253", "254"], [], "D:\\templete\\pdf-exam-generator\\src\\components\\results\\QuestionReview.tsx", ["255", "256", "257", "258"], [], "D:\\templete\\pdf-exam-generator\\src\\components\\results\\ScoreOverview.tsx", ["259", "260"], [], "D:\\templete\\pdf-exam-generator\\src\\components\\sections\\CTASection.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\components\\sections\\FeaturesSection.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\components\\sections\\HeroSection.tsx", ["261"], [], "D:\\templete\\pdf-exam-generator\\src\\components\\sections\\HowItWorksSection.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\components\\ui\\FileUpload.tsx", ["262"], [], "D:\\templete\\pdf-exam-generator\\src\\components\\ui\\LoadingSpinner.tsx", [], [], "D:\\templete\\pdf-exam-generator\\src\\components\\upload\\FileUpload.tsx", ["263"], [], "D:\\templete\\pdf-exam-generator\\src\\contexts\\AuthContext.tsx", ["264", "265", "266", "267"], [], "D:\\templete\\pdf-exam-generator\\src\\lib\\firebase.ts", [], [], "D:\\templete\\pdf-exam-generator\\src\\app\\profile\\page.tsx", ["268", "269"], [], {"ruleId": "270", "severity": 1, "message": "271", "line": 21, "column": 10, "nodeType": null, "messageId": "272", "endLine": 21, "endColumn": 26}, {"ruleId": "270", "severity": 1, "message": "273", "line": 8, "column": 3, "nodeType": null, "messageId": "272", "endLine": 8, "endColumn": 8}, {"ruleId": "270", "severity": 1, "message": "274", "line": 12, "column": 3, "nodeType": null, "messageId": "272", "endLine": 12, "endColumn": 11}, {"ruleId": "270", "severity": 1, "message": "275", "line": 20, "column": 11, "nodeType": null, "messageId": "272", "endLine": 20, "endColumn": 15}, {"ruleId": "270", "severity": 1, "message": "276", "line": 8, "column": 3, "nodeType": null, "messageId": "272", "endLine": 8, "endColumn": 9}, {"ruleId": "270", "severity": 1, "message": "277", "line": 12, "column": 3, "nodeType": null, "messageId": "272", "endLine": 12, "endColumn": 8}, {"ruleId": "270", "severity": 1, "message": "278", "line": 17, "column": 3, "nodeType": null, "messageId": "272", "endLine": 17, "endColumn": 9}, {"ruleId": "270", "severity": 1, "message": "275", "line": 25, "column": 11, "nodeType": null, "messageId": "272", "endLine": 25, "endColumn": 15}, {"ruleId": "270", "severity": 1, "message": "276", "line": 11, "column": 3, "nodeType": null, "messageId": "272", "endLine": 11, "endColumn": 9}, {"ruleId": "270", "severity": 1, "message": "275", "line": 23, "column": 11, "nodeType": null, "messageId": "272", "endLine": 23, "endColumn": 15}, {"ruleId": "270", "severity": 1, "message": "279", "line": 8, "column": 3, "nodeType": null, "messageId": "272", "endLine": 8, "endColumn": 13}, {"ruleId": "270", "severity": 1, "message": "274", "line": 13, "column": 3, "nodeType": null, "messageId": "272", "endLine": 13, "endColumn": 11}, {"ruleId": "270", "severity": 1, "message": "276", "line": 8, "column": 3, "nodeType": null, "messageId": "272", "endLine": 8, "endColumn": 9}, {"ruleId": "270", "severity": 1, "message": "280", "line": 9, "column": 3, "nodeType": null, "messageId": "272", "endLine": 9, "endColumn": 7}, {"ruleId": "270", "severity": 1, "message": "274", "line": 13, "column": 3, "nodeType": null, "messageId": "272", "endLine": 13, "endColumn": 11}, {"ruleId": "270", "severity": 1, "message": "275", "line": 24, "column": 11, "nodeType": null, "messageId": "272", "endLine": 24, "endColumn": 15}, {"ruleId": "270", "severity": 1, "message": "281", "line": 8, "column": 21, "nodeType": null, "messageId": "272", "endLine": 8, "endColumn": 25}, {"ruleId": "270", "severity": 1, "message": "282", "line": 8, "column": 27, "nodeType": null, "messageId": "272", "endLine": 8, "endColumn": 31}, {"ruleId": "283", "severity": 1, "message": "284", "line": 63, "column": 48, "nodeType": "285", "messageId": "286", "endLine": 63, "endColumn": 51, "suggestions": "287"}, {"ruleId": "283", "severity": 1, "message": "284", "line": 65, "column": 57, "nodeType": "285", "messageId": "286", "endLine": 65, "endColumn": 60, "suggestions": "288"}, {"ruleId": "283", "severity": 1, "message": "284", "line": 97, "column": 59, "nodeType": "285", "messageId": "286", "endLine": 97, "endColumn": 62, "suggestions": "289"}, {"ruleId": "270", "severity": 1, "message": "290", "line": 9, "column": 32, "nodeType": null, "messageId": "272", "endLine": 9, "endColumn": 38}, {"ruleId": "283", "severity": 1, "message": "284", "line": 12, "column": 27, "nodeType": "285", "messageId": "286", "endLine": 12, "endColumn": 30, "suggestions": "291"}, {"ruleId": "283", "severity": 1, "message": "284", "line": 13, "column": 14, "nodeType": "285", "messageId": "286", "endLine": 13, "endColumn": 17, "suggestions": "292"}, {"ruleId": "283", "severity": 1, "message": "284", "line": 14, "column": 11, "nodeType": "285", "messageId": "286", "endLine": 14, "endColumn": 14, "suggestions": "293"}, {"ruleId": "270", "severity": 1, "message": "294", "line": 6, "column": 47, "nodeType": null, "messageId": "272", "endLine": 6, "endColumn": 54}, {"ruleId": "270", "severity": 1, "message": "295", "line": 31, "column": 14, "nodeType": null, "messageId": "272", "endLine": 31, "endColumn": 17}, {"ruleId": "270", "severity": 1, "message": "296", "line": 4, "column": 56, "nodeType": null, "messageId": "272", "endLine": 4, "endColumn": 62}, {"ruleId": "283", "severity": 1, "message": "284", "line": 11, "column": 18, "nodeType": "285", "messageId": "286", "endLine": 11, "endColumn": 21, "suggestions": "297"}, {"ruleId": "283", "severity": 1, "message": "284", "line": 18, "column": 27, "nodeType": "285", "messageId": "286", "endLine": 18, "endColumn": 30, "suggestions": "298"}, {"ruleId": "283", "severity": 1, "message": "284", "line": 19, "column": 48, "nodeType": "285", "messageId": "286", "endLine": 19, "endColumn": 51, "suggestions": "299"}, {"ruleId": "270", "severity": 1, "message": "300", "line": 1, "column": 10, "nodeType": null, "messageId": "272", "endLine": 1, "endColumn": 21}, {"ruleId": "270", "severity": 1, "message": "301", "line": 9, "column": 3, "nodeType": null, "messageId": "272", "endLine": 9, "endColumn": 11}, {"ruleId": "270", "severity": 1, "message": "302", "line": 22, "column": 3, "nodeType": null, "messageId": "272", "endLine": 22, "endColumn": 8}, {"ruleId": "303", "severity": 1, "message": "304", "line": 81, "column": 17, "nodeType": "305", "endLine": 81, "endColumn": 126}, {"ruleId": "303", "severity": 1, "message": "304", "line": 190, "column": 21, "nodeType": "305", "endLine": 190, "endColumn": 128}, {"ruleId": "303", "severity": 1, "message": "304", "line": 84, "column": 23, "nodeType": "305", "endLine": 84, "endColumn": 130}, {"ruleId": "303", "severity": 1, "message": "304", "line": 175, "column": 23, "nodeType": "305", "endLine": 175, "endColumn": 130}, {"ruleId": "283", "severity": 1, "message": "284", "line": 4, "column": 16, "nodeType": "285", "messageId": "286", "endLine": 4, "endColumn": 19, "suggestions": "306"}, {"ruleId": "283", "severity": 1, "message": "284", "line": 24, "column": 33, "nodeType": "285", "messageId": "286", "endLine": 24, "endColumn": 36, "suggestions": "307"}, {"ruleId": "270", "severity": 1, "message": "277", "line": 1, "column": 41, "nodeType": null, "messageId": "272", "endLine": 1, "endColumn": 46}, {"ruleId": "283", "severity": 1, "message": "284", "line": 4, "column": 14, "nodeType": "285", "messageId": "286", "endLine": 4, "endColumn": 17, "suggestions": "308"}, {"ruleId": "283", "severity": 1, "message": "284", "line": 5, "column": 27, "nodeType": "285", "messageId": "286", "endLine": 5, "endColumn": 30, "suggestions": "309"}, {"ruleId": "283", "severity": 1, "message": "284", "line": 8, "column": 18, "nodeType": "285", "messageId": "286", "endLine": 8, "endColumn": 21, "suggestions": "310"}, {"ruleId": "283", "severity": 1, "message": "284", "line": 14, "column": 27, "nodeType": "285", "messageId": "286", "endLine": 14, "endColumn": 30, "suggestions": "311"}, {"ruleId": "283", "severity": 1, "message": "284", "line": 18, "column": 54, "nodeType": "285", "messageId": "286", "endLine": 18, "endColumn": 57, "suggestions": "312"}, {"ruleId": "283", "severity": 1, "message": "284", "line": 31, "column": 61, "nodeType": "285", "messageId": "286", "endLine": 31, "endColumn": 64, "suggestions": "313"}, {"ruleId": "283", "severity": 1, "message": "284", "line": 8, "column": 11, "nodeType": "285", "messageId": "286", "endLine": 8, "endColumn": 14, "suggestions": "314"}, {"ruleId": "315", "severity": 1, "message": "316", "line": 57, "column": 51, "nodeType": "317", "messageId": "318", "suggestions": "319"}, {"ruleId": "270", "severity": 1, "message": "320", "line": 11, "column": 10, "nodeType": null, "messageId": "272", "endLine": 11, "endColumn": 22}, {"ruleId": "283", "severity": 1, "message": "284", "line": 19, "column": 69, "nodeType": "285", "messageId": "286", "endLine": 19, "endColumn": 72, "suggestions": "321"}, {"ruleId": "283", "severity": 1, "message": "284", "line": 16, "column": 69, "nodeType": "285", "messageId": "286", "endLine": 16, "endColumn": 72, "suggestions": "322"}, {"ruleId": "270", "severity": 1, "message": "323", "line": 13, "column": 3, "nodeType": null, "messageId": "272", "endLine": 13, "endColumn": 21}, {"ruleId": "270", "severity": 1, "message": "324", "line": 14, "column": 3, "nodeType": null, "messageId": "272", "endLine": 14, "endColumn": 23}, {"ruleId": "283", "severity": 1, "message": "284", "line": 72, "column": 72, "nodeType": "285", "messageId": "286", "endLine": 72, "endColumn": 75, "suggestions": "325"}, {"ruleId": "270", "severity": 1, "message": "326", "line": 142, "column": 14, "nodeType": null, "messageId": "272", "endLine": 142, "endColumn": 19}, {"ruleId": "270", "severity": 1, "message": "275", "line": 9, "column": 43, "nodeType": null, "messageId": "272", "endLine": 9, "endColumn": 47}, {"ruleId": "303", "severity": 1, "message": "304", "line": 79, "column": 23, "nodeType": "305", "endLine": 83, "endColumn": 25}, "@typescript-eslint/no-unused-vars", "'analysisComplete' is assigned a value but never used.", "unusedVar", "'Users' is defined but never used.", "'Calendar' is defined but never used.", "'user' is assigned a value but never used.", "'Filter' is defined but never used.", "'Clock' is defined but never used.", "'Trash2' is defined but never used.", "'TrendingUp' is defined but never used.", "'Plus' is defined but never used.", "'Save' is defined but never used.", "'Flag' is defined but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["327", "328"], ["329", "330"], ["331", "332"], "'Share2' is defined but never used.", ["333", "334"], ["335", "336"], ["337", "338"], "'Palette' is defined but never used.", "'err' is defined but never used.", "'Circle' is defined but never used.", ["339", "340"], ["341", "342"], ["343", "344"], "'CheckCircle' is defined but never used.", "'FileText' is defined but never used.", "'Award' is defined but never used.", "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", ["345", "346"], ["347", "348"], ["349", "350"], ["351", "352"], ["353", "354"], ["355", "356"], ["357", "358"], ["359", "360"], ["361", "362"], "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["363", "364", "365", "366"], "'uploadedFile' is assigned a value but never used.", ["367", "368"], ["369", "370"], "'GoogleAuthProvider' is defined but never used.", "'FacebookAuthProvider' is defined but never used.", ["371", "372"], "'error' is defined but never used.", {"messageId": "373", "fix": "374", "desc": "375"}, {"messageId": "376", "fix": "377", "desc": "378"}, {"messageId": "373", "fix": "379", "desc": "375"}, {"messageId": "376", "fix": "380", "desc": "378"}, {"messageId": "373", "fix": "381", "desc": "375"}, {"messageId": "376", "fix": "382", "desc": "378"}, {"messageId": "373", "fix": "383", "desc": "375"}, {"messageId": "376", "fix": "384", "desc": "378"}, {"messageId": "373", "fix": "385", "desc": "375"}, {"messageId": "376", "fix": "386", "desc": "378"}, {"messageId": "373", "fix": "387", "desc": "375"}, {"messageId": "376", "fix": "388", "desc": "378"}, {"messageId": "373", "fix": "389", "desc": "375"}, {"messageId": "376", "fix": "390", "desc": "378"}, {"messageId": "373", "fix": "391", "desc": "375"}, {"messageId": "376", "fix": "392", "desc": "378"}, {"messageId": "373", "fix": "393", "desc": "375"}, {"messageId": "376", "fix": "394", "desc": "378"}, {"messageId": "373", "fix": "395", "desc": "375"}, {"messageId": "376", "fix": "396", "desc": "378"}, {"messageId": "373", "fix": "397", "desc": "375"}, {"messageId": "376", "fix": "398", "desc": "378"}, {"messageId": "373", "fix": "399", "desc": "375"}, {"messageId": "376", "fix": "400", "desc": "378"}, {"messageId": "373", "fix": "401", "desc": "375"}, {"messageId": "376", "fix": "402", "desc": "378"}, {"messageId": "373", "fix": "403", "desc": "375"}, {"messageId": "376", "fix": "404", "desc": "378"}, {"messageId": "373", "fix": "405", "desc": "375"}, {"messageId": "376", "fix": "406", "desc": "378"}, {"messageId": "373", "fix": "407", "desc": "375"}, {"messageId": "376", "fix": "408", "desc": "378"}, {"messageId": "373", "fix": "409", "desc": "375"}, {"messageId": "376", "fix": "410", "desc": "378"}, {"messageId": "373", "fix": "411", "desc": "375"}, {"messageId": "376", "fix": "412", "desc": "378"}, {"messageId": "413", "data": "414", "fix": "415", "desc": "416"}, {"messageId": "413", "data": "417", "fix": "418", "desc": "419"}, {"messageId": "413", "data": "420", "fix": "421", "desc": "422"}, {"messageId": "413", "data": "423", "fix": "424", "desc": "425"}, {"messageId": "373", "fix": "426", "desc": "375"}, {"messageId": "376", "fix": "427", "desc": "378"}, {"messageId": "373", "fix": "428", "desc": "375"}, {"messageId": "376", "fix": "429", "desc": "378"}, {"messageId": "373", "fix": "430", "desc": "375"}, {"messageId": "376", "fix": "431", "desc": "378"}, "suggestUnknown", {"range": "432", "text": "433"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "434", "text": "435"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "436", "text": "433"}, {"range": "437", "text": "435"}, {"range": "438", "text": "433"}, {"range": "439", "text": "435"}, {"range": "440", "text": "433"}, {"range": "441", "text": "435"}, {"range": "442", "text": "433"}, {"range": "443", "text": "435"}, {"range": "444", "text": "433"}, {"range": "445", "text": "435"}, {"range": "446", "text": "433"}, {"range": "447", "text": "435"}, {"range": "448", "text": "433"}, {"range": "449", "text": "435"}, {"range": "450", "text": "433"}, {"range": "451", "text": "435"}, {"range": "452", "text": "433"}, {"range": "453", "text": "435"}, {"range": "454", "text": "433"}, {"range": "455", "text": "435"}, {"range": "456", "text": "433"}, {"range": "457", "text": "435"}, {"range": "458", "text": "433"}, {"range": "459", "text": "435"}, {"range": "460", "text": "433"}, {"range": "461", "text": "435"}, {"range": "462", "text": "433"}, {"range": "463", "text": "435"}, {"range": "464", "text": "433"}, {"range": "465", "text": "435"}, {"range": "466", "text": "433"}, {"range": "467", "text": "435"}, {"range": "468", "text": "433"}, {"range": "469", "text": "435"}, "replaceWithAlt", {"alt": "470"}, {"range": "471", "text": "472"}, "Replace with `&apos;`.", {"alt": "473"}, {"range": "474", "text": "475"}, "Replace with `&lsquo;`.", {"alt": "476"}, {"range": "477", "text": "478"}, "Replace with `&#39;`.", {"alt": "479"}, {"range": "480", "text": "481"}, "Replace with `&rsquo;`.", {"range": "482", "text": "433"}, {"range": "483", "text": "435"}, {"range": "484", "text": "433"}, {"range": "485", "text": "435"}, {"range": "486", "text": "433"}, {"range": "487", "text": "435"}, [2805, 2808], "unknown", [2805, 2808], "never", [2934, 2937], [2934, 2937], [3947, 3950], [3947, 3950], [505, 508], [505, 508], [524, 527], [524, 527], [541, 544], [541, 544], [303, 306], [303, 306], [443, 446], [443, 446], [496, 499], [496, 499], [112, 115], [112, 115], [691, 694], [691, 694], [121, 124], [121, 124], [154, 157], [154, 157], [240, 243], [240, 243], [354, 357], [354, 357], [494, 497], [494, 497], [1071, 1074], [1071, 1074], [207, 210], [207, 210], "&apos;", [2011, 2048], "Here&apos;s how you performed on your exam", "&lsquo;", [2011, 2048], "Here&lsquo;s how you performed on your exam", "&#39;", [2011, 2048], "Here&#39;s how you performed on your exam", "&rsquo;", [2011, 2048], "Here&rsquo;s how you performed on your exam", [760, 763], [760, 763], [522, 525], [522, 525], [2229, 2232], [2229, 2232]]