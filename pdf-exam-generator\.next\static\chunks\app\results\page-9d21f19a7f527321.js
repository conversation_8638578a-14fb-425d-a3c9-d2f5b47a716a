(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[139],{133:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("rotate-ccw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]])},646:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},785:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>C});var r=s(5155),a=s(2115),c=s(5695);let l=(0,s(9946).A)("trophy",[["path",{d:"M10 14.66v1.626a2 2 0 0 1-.976 1.696A5 5 0 0 0 7 21.978",key:"1n3hpd"}],["path",{d:"M14 14.66v1.626a2 2 0 0 0 .976 1.696A5 5 0 0 1 17 21.978",key:"rfe1zi"}],["path",{d:"M18 9h1.5a1 1 0 0 0 0-5H18",key:"7xy6bh"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M6 9a6 6 0 0 0 12 0V3a1 1 0 0 0-1-1H7a1 1 0 0 0-1 1z",key:"1mhfuq"}],["path",{d:"M6 9H4.5a1 1 0 0 1 0-5H6",key:"tex48p"}]]);var n=s(9074),i=s(6785),o=s(4186),d=s(646),m=s(4861);function x(e){let{score:t,total:s,percentage:a,completedAt:c,config:x}=e,h=e=>e>=90?"text-green-600":e>=80?"text-blue-600":e>=70?"text-yellow-600":e>=60?"text-orange-600":"text-red-600";return(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,r.jsx)("div",{className:"lg:col-span-2",children:(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border-2 p-8 ".concat(a>=90?"bg-green-50 border-green-200":a>=80?"bg-blue-50 border-blue-200":a>=70?"bg-yellow-50 border-yellow-200":a>=60?"bg-orange-50 border-orange-200":"bg-red-50 border-red-200"),children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"flex items-center justify-center mb-6",children:(0,r.jsx)(l,{className:"w-16 h-16 ".concat(h(a))})}),(0,r.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Exam Complete!"}),(0,r.jsx)("p",{className:"text-gray-600 mb-8",children:"Here's how you performed on your exam"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-6xl font-bold ".concat(h(a)," mb-2"),children:a>=90?"A":a>=80?"B":a>=70?"C":a>=60?"D":"F"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Grade"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("div",{className:"text-6xl font-bold ".concat(h(a)," mb-2"),children:[a,"%"]}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Score"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("div",{className:"text-6xl font-bold text-gray-900 mb-2",children:[t,"/",s]}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Correct"})]})]}),(0,r.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-4 mb-6",children:(0,r.jsx)("div",{className:"h-4 rounded-full transition-all duration-1000 ".concat(a>=90?"bg-green-500":a>=80?"bg-blue-500":a>=70?"bg-yellow-500":a>=60?"bg-orange-500":"bg-red-500"),style:{width:"".concat(a,"%")}})}),(0,r.jsxs)("div",{className:"text-center",children:[a>=90&&(0,r.jsx)("p",{className:"text-green-700 font-medium",children:"Excellent work! Outstanding performance!"}),a>=80&&a<90&&(0,r.jsx)("p",{className:"text-blue-700 font-medium",children:"Great job! Very good performance!"}),a>=70&&a<80&&(0,r.jsx)("p",{className:"text-yellow-700 font-medium",children:"Good work! Room for improvement."}),a>=60&&a<70&&(0,r.jsx)("p",{className:"text-orange-700 font-medium",children:"Fair performance. Consider reviewing the material."}),a<60&&(0,r.jsx)("p",{className:"text-red-700 font-medium",children:"Needs improvement. Review the material and try again."})]})]})})}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Exam Details"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(n.A,{className:"w-5 h-5 text-gray-500"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"Completed"}),(0,r.jsx)("p",{className:"text-xs text-gray-600",children:new Date(c).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(i.A,{className:"w-5 h-5 text-gray-500"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"Difficulty"}),(0,r.jsx)("p",{className:"text-xs text-gray-600 capitalize",children:(null==x?void 0:x.difficulty)||"Intermediate"})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(o.A,{className:"w-5 h-5 text-gray-500"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"Time Limit"}),(0,r.jsx)("p",{className:"text-xs text-gray-600",children:(null==x?void 0:x.hasTimeLimit)?"".concat(x.timeLimit," minutes"):"No limit"})]})]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Quick Stats"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(d.A,{className:"w-4 h-4 text-green-500"}),(0,r.jsx)("span",{className:"text-sm text-gray-700",children:"Correct Answers"})]}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-900",children:t})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(m.A,{className:"w-4 h-4 text-red-500"}),(0,r.jsx)("span",{className:"text-sm text-gray-700",children:"Incorrect Answers"})]}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-900",children:s-t})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(i.A,{className:"w-4 h-4 text-blue-500"}),(0,r.jsx)("span",{className:"text-sm text-gray-700",children:"Accuracy Rate"})]}),(0,r.jsxs)("span",{className:"text-sm font-medium text-gray-900",children:[a,"%"]})]})]})]}),a>=80&&(0,r.jsxs)("div",{className:"bg-gradient-to-r from-yellow-50 to-yellow-100 border border-yellow-200 rounded-lg p-6 text-center",children:[(0,r.jsx)(l,{className:"w-12 h-12 text-yellow-600 mx-auto mb-3"}),(0,r.jsx)("h4",{className:"text-lg font-semibold text-yellow-800 mb-2",children:"Achievement Unlocked!"}),(0,r.jsx)("p",{className:"text-sm text-yellow-700",children:a>=90?"Perfect Score Master":"High Achiever"})]})]})]})}var h=s(2657),u=s(5339);function g(e){let{questions:t,answers:s}=e,a=(e,t)=>{if(void 0===t)return!1;if("multiple-choice"===e.type||"true-false"===e.type)return t===e.correctAnswer;if("short-answer"===e.type||"fill-blank"===e.type){let s=t.toLowerCase().trim(),r=e.correctAnswer.toLowerCase().trim();return s.includes(r)||r.includes(s)}return!1},c=(e,t)=>{if(void 0===t)return(0,r.jsx)("span",{className:"text-gray-500 italic",children:"No answer provided"});switch(e.type){case"multiple-choice":var s;return(null==(s=e.options)?void 0:s[t])||"Invalid answer";case"true-false":return t?"True":"False";default:return t}},l=e=>{switch(e.type){case"multiple-choice":var t;return(null==(t=e.options)?void 0:t[e.correctAnswer])||"Invalid answer";case"true-false":return e.correctAnswer?"True":"False";default:return e.correctAnswer}};return(0,r.jsx)("div",{className:"space-y-6",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 mb-6 flex items-center",children:[(0,r.jsx)(h.A,{className:"w-5 h-5 mr-2"}),"Question by Question Review"]}),(0,r.jsx)("div",{className:"space-y-8",children:t.map((e,t)=>{let n=s[e.id],i=a(e,n),o=void 0!==n;return(0,r.jsxs)("div",{className:"border border-gray-200 rounded-lg p-6",children:[(0,r.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("span",{className:"flex items-center justify-center w-8 h-8 bg-gray-100 rounded-full text-sm font-medium text-gray-700",children:t+1}),(0,r.jsx)("span",{className:"px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded-full capitalize",children:e.type.replace("-"," ")})]}),(0,r.jsx)("div",{className:"flex items-center space-x-2",children:o?i?(0,r.jsxs)("div",{className:"flex items-center space-x-1 text-green-600",children:[(0,r.jsx)(d.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{className:"text-sm font-medium",children:"Correct"})]}):(0,r.jsxs)("div",{className:"flex items-center space-x-1 text-red-600",children:[(0,r.jsx)(m.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{className:"text-sm font-medium",children:"Incorrect"})]}):(0,r.jsxs)("div",{className:"flex items-center space-x-1 text-gray-500",children:[(0,r.jsx)(u.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{className:"text-sm",children:"Not Answered"})]})})]}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-6 leading-relaxed",children:e.question}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,r.jsxs)("div",{className:"p-4 rounded-lg border-2 ".concat(o?i?"border-green-200 bg-green-50":"border-red-200 bg-red-50":"border-gray-200 bg-gray-50"),children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"Your Answer"}),(0,r.jsx)("p",{className:"text-sm ".concat(o?i?"text-green-800":"text-red-800":"text-gray-500"),children:c(e,n)})]}),(0,r.jsxs)("div",{className:"p-4 rounded-lg border-2 border-green-200 bg-green-50",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"Correct Answer"}),(0,r.jsx)("p",{className:"text-sm text-green-800 font-medium",children:l(e)})]})]}),"multiple-choice"===e.type&&e.options&&(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-3",children:"All Options"}),(0,r.jsx)("div",{className:"space-y-2",children:e.options.map((t,s)=>{let a=n===s,c=e.correctAnswer===s;return(0,r.jsx)("div",{className:"p-3 rounded-lg border ".concat(c?"border-green-300 bg-green-50":a&&!c?"border-red-300 bg-red-50":"border-gray-200 bg-gray-50"),children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsxs)("span",{className:"text-sm font-medium text-gray-600",children:[String.fromCharCode(65+s),"."]}),(0,r.jsx)("span",{className:"text-sm ".concat(c?"text-green-800 font-medium":a&&!c?"text-red-800":"text-gray-700"),children:t}),c&&(0,r.jsx)(d.A,{className:"w-4 h-4 text-green-600"}),a&&!c&&(0,r.jsx)(m.A,{className:"w-4 h-4 text-red-600"})]})},s)})})]}),(0,r.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-blue-900 mb-2",children:"Explanation"}),(0,r.jsx)("p",{className:"text-sm text-blue-800 leading-relaxed",children:e.explanation})]})]},e.id)})})]})})}var p=s(2713),y=s(3109);function b(e){let{questions:t,answers:s,scoreData:a}=e,c=(()=>{let e={};return t.forEach(t=>{let r=t.type;e[r]||(e[r]={correct:0,total:0}),e[r].total++;let a=s[t.id];if(void 0!==a){let s=!1;if("multiple-choice"===t.type||"true-false"===t.type)s=a===t.correctAnswer;else if("short-answer"===t.type||"fill-blank"===t.type){let e=a.toLowerCase().trim(),r=t.correctAnswer.toLowerCase().trim();s=e.includes(r)||r.includes(e)}s&&e[r].correct++}}),e})(),l=[{topic:"Machine Learning Fundamentals",correct:4,total:5,percentage:80},{topic:"Neural Networks",correct:2,total:3,percentage:67},{topic:"Data Processing",correct:3,total:3,percentage:100},{topic:"Model Evaluation",correct:1,total:2,percentage:50}],n=e=>e.split("-").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" "),o=e=>e>=80?"text-green-600":e>=60?"text-yellow-600":"text-red-600",d=e=>e>=80?"bg-green-500":e>=60?"bg-yellow-500":"bg-red-500";return(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 mb-6 flex items-center",children:[(0,r.jsx)(p.A,{className:"w-5 h-5 mr-2"}),"Performance Analytics"]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[(0,r.jsxs)("div",{className:"text-center p-4 bg-blue-50 rounded-lg",children:[(0,r.jsxs)("div",{className:"text-3xl font-bold text-blue-600 mb-2",children:[a.percentage,"%"]}),(0,r.jsx)("p",{className:"text-sm text-blue-700",children:"Overall Score"})]}),(0,r.jsxs)("div",{className:"text-center p-4 bg-green-50 rounded-lg",children:[(0,r.jsx)("div",{className:"text-3xl font-bold text-green-600 mb-2",children:a.score}),(0,r.jsx)("p",{className:"text-sm text-green-700",children:"Correct Answers"})]}),(0,r.jsxs)("div",{className:"text-center p-4 bg-purple-50 rounded-lg",children:[(0,r.jsx)("div",{className:"text-3xl font-bold text-purple-600 mb-2",children:Object.keys(s).length}),(0,r.jsx)("p",{className:"text-sm text-purple-700",children:"Questions Attempted"})]}),(0,r.jsxs)("div",{className:"text-center p-4 bg-orange-50 rounded-lg",children:[(0,r.jsxs)("div",{className:"text-3xl font-bold text-orange-600 mb-2",children:[Math.round(Object.keys(s).length/t.length*100),"%"]}),(0,r.jsx)("p",{className:"text-sm text-orange-700",children:"Completion Rate"})]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-6 flex items-center",children:[(0,r.jsx)(i.A,{className:"w-5 h-5 mr-2"}),"Performance by Question Type"]}),(0,r.jsx)("div",{className:"space-y-4",children:Object.entries(c).map(e=>{let[t,s]=e,a=s.total>0?Math.round(s.correct/s.total*100):0;return(0,r.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900",children:n(t)}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("span",{className:"text-sm text-gray-600",children:[s.correct,"/",s.total," correct"]}),(0,r.jsxs)("span",{className:"text-sm font-medium ".concat(o(a)),children:[a,"%"]})]})]}),(0,r.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,r.jsx)("div",{className:"h-2 rounded-full transition-all duration-500 ".concat(d(a)),style:{width:"".concat(a,"%")}})})]},t)})})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-6 flex items-center",children:[(0,r.jsx)(y.A,{className:"w-5 h-5 mr-2"}),"Performance by Topic"]}),(0,r.jsx)("div",{className:"space-y-4",children:l.map((e,t)=>(0,r.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900",children:e.topic}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("span",{className:"text-sm text-gray-600",children:[e.correct,"/",e.total," correct"]}),(0,r.jsxs)("span",{className:"text-sm font-medium ".concat(o(e.percentage)),children:[e.percentage,"%"]})]})]}),(0,r.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,r.jsx)("div",{className:"h-2 rounded-full transition-all duration-500 ".concat(d(e.percentage)),style:{width:"".concat(e.percentage,"%")}})})]},t))})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-6",children:"Recommendations for Improvement"}),(0,r.jsxs)("div",{className:"space-y-4",children:[a.percentage<60&&(0,r.jsxs)("div",{className:"p-4 bg-red-50 border border-red-200 rounded-lg",children:[(0,r.jsx)("h4",{className:"font-medium text-red-900 mb-2",children:"Focus on Fundamentals"}),(0,r.jsx)("p",{className:"text-sm text-red-800",children:"Your overall score suggests you should review the basic concepts covered in this material. Consider going through the source material again before retaking the exam."})]}),Object.entries(c).map(e=>{let[t,s]=e,a=s.total>0?Math.round(s.correct/s.total*100):0;return a<70?(0,r.jsxs)("div",{className:"p-4 bg-yellow-50 border border-yellow-200 rounded-lg",children:[(0,r.jsxs)("h4",{className:"font-medium text-yellow-900 mb-2",children:["Improve ",n(t)," Skills"]}),(0,r.jsxs)("p",{className:"text-sm text-yellow-800",children:["You scored ",a,"% on ",n(t)," questions. Practice more questions of this type to improve your performance."]})]},t):null}),l.filter(e=>e.percentage<70).map((e,t)=>(0,r.jsxs)("div",{className:"p-4 bg-blue-50 border border-blue-200 rounded-lg",children:[(0,r.jsxs)("h4",{className:"font-medium text-blue-900 mb-2",children:["Study ",e.topic]}),(0,r.jsxs)("p",{className:"text-sm text-blue-800",children:["You scored ",e.percentage,"% on ",e.topic," questions. Review this topic area to strengthen your understanding."]})]},t)),a.percentage>=80&&(0,r.jsxs)("div",{className:"p-4 bg-green-50 border border-green-200 rounded-lg",children:[(0,r.jsx)("h4",{className:"font-medium text-green-900 mb-2",children:"Excellent Performance!"}),(0,r.jsx)("p",{className:"text-sm text-green-800",children:"You demonstrated strong understanding across most topics. Keep up the great work and consider advancing to more challenging material."})]})]})]})]})}var j=s(1788),N=s(6516),f=s(8883),v=s(7434);function w(e){var t;let{examResults:s,scoreData:a}=e,c=async()=>{let e={title:"My Exam Results",text:"I scored ".concat(a.percentage,"% (").concat(a.score,"/").concat(a.total,") on my exam!"),url:window.location.href};if(navigator.share)try{await navigator.share(e)}catch(t){navigator.clipboard.writeText("".concat(e.text," ").concat(e.url)),alert("Results copied to clipboard!")}else navigator.clipboard.writeText("".concat(e.text," ").concat(e.url)),alert("Results copied to clipboard!")};return(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-6",children:"Export & Share Results"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,r.jsxs)("button",{onClick:()=>{var e;let t=new Blob(["\nEXAM RESULTS REPORT\n==================\n\nScore: ".concat(a.score,"/").concat(a.total," (").concat(a.percentage,"%)\nCompleted: ").concat(new Date(s.completedAt).toLocaleDateString(),"\nDifficulty: ").concat((null==(e=s.config)?void 0:e.difficulty)||"Intermediate","\n\nQUESTION BREAKDOWN:\n").concat(s.questions.map((e,t)=>{let r=s.answers[e.id],a=r===e.correctAnswer;return"\n".concat(t+1,". ").concat(e.question,"\n   Your Answer: ").concat(void 0!==r?r:"No answer","\n   Correct Answer: ").concat(e.correctAnswer,"\n   Result: ").concat(a?"Correct":"Incorrect","\n")}).join(""),"\n    ")],{type:"text/plain"}),r=URL.createObjectURL(t),c=document.createElement("a");c.href=r,c.download="exam-results-".concat(new Date().toISOString().split("T")[0],".txt"),document.body.appendChild(c),c.click(),document.body.removeChild(c),URL.revokeObjectURL(r)},className:"flex flex-col items-center space-y-2 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,r.jsx)(j.A,{className:"w-6 h-6 text-blue-600"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-900",children:"Download Report"}),(0,r.jsx)("span",{className:"text-xs text-gray-600",children:"Export as text file"})]}),(0,r.jsxs)("button",{onClick:c,className:"flex flex-col items-center space-y-2 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,r.jsx)(N.A,{className:"w-6 h-6 text-green-600"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-900",children:"Share Results"}),(0,r.jsx)("span",{className:"text-xs text-gray-600",children:"Share your score"})]}),(0,r.jsxs)("button",{onClick:()=>{var e;let t=encodeURIComponent("My Exam Results"),r=encodeURIComponent("\nI completed an exam and scored ".concat(a.percentage,"% (").concat(a.score,"/").concat(a.total,").\n\nExam Details:\n- Completed: ").concat(new Date(s.completedAt).toLocaleDateString(),"\n- Difficulty: ").concat((null==(e=s.config)?void 0:e.difficulty)||"Intermediate","\n- Questions: ").concat(a.total,"\n- Correct Answers: ").concat(a.score,"\n\nThis exam was generated using the PDF to Interactive Exam Generator.\n    "));window.open("mailto:?subject=".concat(t,"&body=").concat(r))},className:"flex flex-col items-center space-y-2 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,r.jsx)(f.A,{className:"w-6 h-6 text-purple-600"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-900",children:"Email Results"}),(0,r.jsx)("span",{className:"text-xs text-gray-600",children:"Send via email"})]}),(0,r.jsxs)("button",{onClick:()=>{window.print()},className:"flex flex-col items-center space-y-2 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,r.jsx)(v.A,{className:"w-6 h-6 text-orange-600"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-900",children:"Print Results"}),(0,r.jsx)("span",{className:"text-xs text-gray-600",children:"Print this page"})]})]}),(0,r.jsxs)("div",{className:"mt-6 p-4 bg-gray-50 rounded-lg",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-900 mb-2",children:"Quick Summary"}),(0,r.jsxs)("p",{className:"text-sm text-gray-700",children:["Exam completed on ",new Date(s.completedAt).toLocaleDateString()," • Score: ",a.percentage,"% (",a.score,"/",a.total,") • Difficulty: ",(null==(t=s.config)?void 0:t.difficulty)||"Intermediate"]})]})]})}var A=s(7550),k=s(133);function C(){let e=(0,c.useRouter)(),[t,s]=(0,a.useState)(null),[l,n]=(0,a.useState)("overview");if((0,a.useEffect)(()=>{let t=localStorage.getItem("examResults");t?s(JSON.parse(t)):e.push("/")},[e]),!t)return(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Loading results..."})]})});let i=(()=>{if(!t)return{score:0,total:0,percentage:0};let e=0,s=t.questions.length;return t.questions.forEach(s=>{let r=t.answers[s.id];if(void 0!==r){if("multiple-choice"===s.type||"true-false"===s.type)r===s.correctAnswer&&e++;else if("short-answer"===s.type||"fill-blank"===s.type){let t=r.toLowerCase().trim(),a=s.correctAnswer.toLowerCase().trim();(t.includes(a)||a.includes(t))&&e++}}}),{score:e,total:s,percentage:Math.round(e/s*100)}})();return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)("div",{className:"bg-white border-b border-gray-200",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("button",{onClick:()=>e.push("/"),className:"flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors",children:[(0,r.jsx)(A.A,{className:"w-5 h-5"}),(0,r.jsx)("span",{children:"Back to Home"})]}),(0,r.jsx)("div",{className:"h-6 w-px bg-gray-300"}),(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Exam Results"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsxs)("button",{onClick:()=>{localStorage.removeItem("examResults"),e.push("/exam")},className:"flex items-center space-x-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,r.jsx)(k.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"Retake Exam"})]}),(0,r.jsx)("button",{onClick:()=>{localStorage.removeItem("examResults"),localStorage.removeItem("examAnswers"),localStorage.removeItem("uploadedPDF"),localStorage.removeItem("examConfig"),e.push("/")},className:"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:(0,r.jsx)("span",{children:"New Exam"})})]})]}),(0,r.jsx)("div",{className:"mt-6 border-b border-gray-200",children:(0,r.jsxs)("nav",{className:"-mb-px flex space-x-8",children:[(0,r.jsx)("button",{onClick:()=>n("overview"),className:"py-2 px-1 border-b-2 font-medium text-sm transition-colors ".concat("overview"===l?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:"Overview"}),(0,r.jsx)("button",{onClick:()=>n("review"),className:"py-2 px-1 border-b-2 font-medium text-sm transition-colors ".concat("review"===l?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:"Question Review"}),(0,r.jsx)("button",{onClick:()=>n("analytics"),className:"py-2 px-1 border-b-2 font-medium text-sm transition-colors ".concat("analytics"===l?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:"Analytics"})]})})]})}),(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:["overview"===l&&(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsx)(x,{score:i.score,total:i.total,percentage:i.percentage,completedAt:t.completedAt,config:t.config}),(0,r.jsx)(w,{examResults:t,scoreData:i})]}),"review"===l&&(0,r.jsx)(g,{questions:t.questions,answers:t.answers}),"analytics"===l&&(0,r.jsx)(b,{questions:t.questions,answers:t.answers,scoreData:i})]})]})}},1788:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},2657:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},2713:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},3109:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},4186:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},4861:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},5339:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},5695:(e,t,s)=>{"use strict";var r=s(8999);s.o(r,"usePathname")&&s.d(t,{usePathname:function(){return r.usePathname}}),s.o(r,"useRouter")&&s.d(t,{useRouter:function(){return r.useRouter}})},6516:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("share-2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]])},6785:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},7434:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},7550:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},8883:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},9074:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},9558:(e,t,s)=>{Promise.resolve().then(s.bind(s,785))},9946:(e,t,s)=>{"use strict";s.d(t,{A:()=>m});var r=s(2115);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),c=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,s)=>s?s.toUpperCase():t.toLowerCase()),l=e=>{let t=c(e);return t.charAt(0).toUpperCase()+t.slice(1)},n=function(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return t.filter((e,t,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===t).join(" ").trim()},i=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,r.forwardRef)((e,t)=>{let{color:s="currentColor",size:a=24,strokeWidth:c=2,absoluteStrokeWidth:l,className:d="",children:m,iconNode:x,...h}=e;return(0,r.createElement)("svg",{ref:t,...o,width:a,height:a,stroke:s,strokeWidth:l?24*Number(c)/Number(a):c,className:n("lucide",d),...!m&&!i(h)&&{"aria-hidden":"true"},...h},[...x.map(e=>{let[t,s]=e;return(0,r.createElement)(t,s)}),...Array.isArray(m)?m:[m]])}),m=(e,t)=>{let s=(0,r.forwardRef)((s,c)=>{let{className:i,...o}=s;return(0,r.createElement)(d,{ref:c,iconNode:t,className:n("lucide-".concat(a(l(e))),"lucide-".concat(e),i),...o})});return s.displayName=l(e),s}}},e=>{var t=t=>e(e.s=t);e.O(0,[441,684,358],()=>t(9558)),_N_E=e.O()}]);