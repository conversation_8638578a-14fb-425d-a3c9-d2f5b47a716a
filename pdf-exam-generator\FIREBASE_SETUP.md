# Firebase Setup Guide - PDF Exam Generator

## إعداد Firebase للمشروع

### 1. إنشاء مشروع Firebase

1. اذهب إلى [Firebase Console](https://console.firebase.google.com/)
2. انقر على "إنشاء مشروع" (Create a project)
3. أدخل اسم المشروع (مثل: pdf-exam-generator)
4. اختر إعدادات Google Analytics (اختياري)
5. انقر على "إنشاء مشروع"

### 2. إعداد Authentication

1. في لوحة تحكم Firebase، اذهب إلى **Authentication**
2. انقر على **Get started**
3. اذهب إلى تبويب **Sign-in method**
4. فعّل الطرق التالية:
   - **Email/Password**: انقر عليها وفعّلها
   - **Google**: انقر عليها، فعّلها، وأدخل بريدك الإلكتروني كـ support email
   - **Facebook** (اختياري): ستحتاج إلى App ID و App Secret من Facebook Developers

### 3. إعداد Firestore Database

1. اذهب إلى **Firestore Database**
2. انقر على **Create database**
3. اختر **Start in test mode** (للتطوير)
4. اختر موقع قاعدة البيانات (اختر الأقرب لك)

### 4. إعداد Storage

1. اذهب إلى **Storage**
2. انقر على **Get started**
3. اختر **Start in test mode**
4. اختر موقع التخزين

### 5. الحصول على إعدادات المشروع

1. اذهب إلى **Project Settings** (أيقونة الترس)
2. في تبويب **General**، انزل إلى **Your apps**
3. انقر على **Web app** (أيقونة </>)
4. أدخل اسم التطبيق (مثل: pdf-exam-generator-web)
5. انقر على **Register app**
6. انسخ إعدادات Firebase config

### 6. إعداد متغيرات البيئة

1. في مجلد المشروع، افتح ملف `.env.local`
2. استبدل القيم الموجودة بإعدادات مشروعك:

```env
NEXT_PUBLIC_FIREBASE_API_KEY=your_api_key_here
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project_id.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_project_id.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id
```

### 7. إعداد قواعد الأمان (Security Rules)

#### Firestore Rules:
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can read and write their own profile
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Exams - users can read/write their own exams
    match /exams/{examId} {
      allow read, write: if request.auth != null && request.auth.uid == resource.data.userId;
    }
    
    // Results - users can read/write their own results
    match /results/{resultId} {
      allow read, write: if request.auth != null && request.auth.uid == resource.data.userId;
    }
  }
}
```

#### Storage Rules:
```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // Users can upload and read their own files
    match /users/{userId}/{allPaths=**} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
  }
}
```

### 8. تشغيل المشروع

1. تأكد من أن جميع المتغيرات في `.env.local` صحيحة
2. شغّل المشروع:
```bash
npm run dev
```

3. اذهب إلى `http://localhost:3004`
4. جرب إنشاء حساب جديد أو تسجيل الدخول

### 9. اختبار الوظائف

- **التسجيل**: جرب إنشاء حساب جديد بالبريد الإلكتروني
- **تسجيل الدخول**: جرب تسجيل الدخول بالحساب الجديد
- **Google Sign-in**: جرب تسجيل الدخول بـ Google
- **الملف الشخصي**: تحقق من صفحة الملف الشخصي
- **الإعدادات**: جرب تغيير الإعدادات وحفظها

### 10. نشر المشروع (Production)

عند النشر، تأكد من:
1. تغيير قواعد Firestore من test mode إلى production rules
2. تحديث متغيرات البيئة في منصة النشر
3. إضافة domain النشر إلى Authorized domains في Firebase Auth

### استكشاف الأخطاء

#### خطأ "Firebase: Error (auth/configuration-not-found)"
- تأكد من أن جميع متغيرات البيئة صحيحة
- تأكد من أن ملف `.env.local` في المجلد الصحيح

#### خطأ "Firebase: Error (auth/unauthorized-domain)"
- اذهب إلى Authentication > Settings > Authorized domains
- أضف `localhost` و domain موقعك

#### خطأ في Firestore
- تأكد من أن قواعد Firestore تسمح بالقراءة والكتابة
- تحقق من أن المستخدم مسجل دخول

### الدعم

إذا واجهت أي مشاكل:
1. تحقق من Firebase Console للأخطاء
2. افتح Developer Tools في المتصفح وتحقق من Console
3. تأكد من أن جميع الخدمات مفعلة في Firebase

---

**ملاحظة**: هذا الإعداد للتطوير. للإنتاج، ستحتاج إلى إعدادات أمان أكثر تقييداً.
