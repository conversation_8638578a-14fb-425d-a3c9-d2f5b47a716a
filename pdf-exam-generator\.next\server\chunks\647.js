exports.id=647,exports.ids=[647],exports.modules={7043:(e,t,r)=>{"use strict";r.d(t,{LanguageProvider:()=>s});var a=r(12907);let s=(0,a.registerClientReference)(function(){throw Error("Attempted to call LanguageProvider() from the server but LanguageProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\templete\\pdf-exam-generator\\src\\contexts\\LanguageContext.tsx","LanguageProvider");(0,a.registerClientReference)(function(){throw Error("Attempted to call useLanguage() from the server but useLanguage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\templete\\pdf-exam-generator\\src\\contexts\\LanguageContext.tsx","useLanguage")},7613:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>f,A:()=>g});var a=r(60687),s=r(43210),n=r(91042),i=r(75535),o=r(67989),l=r(70146);let c={apiKey:"demo-key",authDomain:"demo-project.firebaseapp.com",projectId:"demo-project",storageBucket:"demo-project.appspot.com",messagingSenderId:"*********",appId:"1:*********:web:abcdef",measurementId:process.env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID||"G-ABCDEF"},d=0===(0,o.Dk)().length?(0,o.Wp)(c):(0,o.Dk)()[0],m=(0,n.xI)(d),h=(0,i.aU)(d);(0,l.c7)(d);let u=new n.HF,x=new n.sk;u.setCustomParameters({prompt:"select_account"}),x.setCustomParameters({display:"popup"});let p=(0,s.createContext)(void 0),g=()=>{let e=(0,s.useContext)(p);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e},f=({children:e})=>{let[t,r]=(0,s.useState)(null),[o,l]=(0,s.useState)(null),[c,d]=(0,s.useState)(!0),g=async(e,t={})=>{if(!e)return;let r=(0,i.H9)(h,"users",e.uid),a=await (0,i.x7)(r);if(a.exists()){let e={...a.data(),lastLoginAt:new Date};await (0,i.BN)(r,e,{merge:!0}),l(e)}else{let{displayName:a,email:s,photoURL:n}=e,o=new Date,c={uid:e.uid,email:s||"",displayName:a||"",photoURL:n||void 0,role:t.role||"student",createdAt:o,lastLoginAt:o,preferences:{language:"ar",theme:"light",notifications:!0},stats:{totalExams:0,averageScore:0,totalQuestions:0,studyTime:0},...t};try{await (0,i.BN)(r,c),l(c)}catch(e){console.error("Error creating user profile:",e)}}},f=async(e,t)=>{d(!0);try{let r=await (0,n.x9)(m,e,t);await g(r.user)}catch(e){throw console.error("Error signing in:",e),e}finally{d(!1)}},v=async(e,t)=>{try{return await f(e,t),!0}catch(e){return!1}},b=async(e,t,r,a)=>{d(!0);try{let s=await (0,n.eJ)(m,e,t);await (0,n.r7)(s.user,{displayName:r}),await g(s.user,{role:a,displayName:r})}catch(e){throw console.error("Error signing up:",e),e}finally{d(!1)}},y=async()=>{d(!0);try{let e=await (0,n.df)(m,u);await g(e.user)}catch(e){throw console.error("Error signing in with Google:",e),e}finally{d(!1)}},j=async()=>{d(!0);try{let e=await (0,n.df)(m,x);await g(e.user)}catch(e){throw console.error("Error signing in with Facebook:",e),e}finally{d(!1)}},w=async()=>{d(!0);try{await (0,n.CI)(m),r(null),l(null),localStorage.removeItem("examResults"),localStorage.removeItem("examAnswers"),localStorage.removeItem("uploadedPDF"),localStorage.removeItem("examConfig")}catch(e){throw console.error("Error signing out:",e),e}finally{d(!1)}},N=async e=>{try{await (0,n.J1)(m,e)}catch(e){throw console.error("Error sending password reset email:",e),e}},A=async e=>{if(t)try{let r=(0,i.H9)(h,"users",t.uid);await (0,i.BN)(r,e,{merge:!0}),o&&l({...o,...e})}catch(e){throw console.error("Error updating user profile:",e),e}};return(0,s.useEffect)(()=>(0,n.hg)(m,async e=>{e?(r(e),await g(e)):(r(null),l(null)),d(!1)}),[]),(0,a.jsx)(p.Provider,{value:{user:t,userProfile:o,loading:c,isAuthenticated:!!t,signIn:f,signUp:b,signInWithGoogle:y,signInWithFacebook:j,logout:w,resetPassword:N,updateUserProfile:A,login:v},children:e})}},29131:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>s});var a=r(12907);(0,a.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\templete\\pdf-exam-generator\\src\\contexts\\AuthContext.tsx","useAuth");let s=(0,a.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\templete\\pdf-exam-generator\\src\\contexts\\AuthContext.tsx","AuthProvider")},34393:(e,t,r)=>{"use strict";r.d(t,{LanguageProvider:()=>o,o:()=>l});var a=r(60687),s=r(43210);let n=(0,s.createContext)(void 0),i={ar:{"nav.features":"المميزات","nav.pricing":"الأسعار","nav.about":"حولنا","nav.contact":"اتصل بنا","nav.login":"تسجيل الدخول","nav.signup":"إنشاء حساب","hero.title":"حول ملفات PDF إلى اختبارات تفاعلية","hero.subtitle":"في دقائق معدودة","hero.description":"استخدم الذكاء الاصطناعي لتحويل أي ملف PDF إلى اختبار تفاعلي مع تحليلات متقدمة وتقييم تلقائي. مثالي للمعلمين والطلاب ومنصات التعلم الإلكتروني.","hero.cta.primary":"ابدأ مجاناً","hero.cta.secondary":"شاهد العرض التوضيحي","hero.trusted":"يثق به أكثر من 10,000 معلم حول العالم","features.title":"مميزات قوية لتعليم أفضل","features.subtitle":"كل ما تحتاجه لإنشاء اختبارات احترافية وتحليل النتائج","feature.ai.title":"ذكاء اصطناعي متقدم","feature.ai.description":"يحلل محتوى PDF ويولد أسئلة متنوعة تلقائياً","feature.interactive.title":"اختبارات تفاعلية","feature.interactive.description":"أسئلة متعددة الخيارات، صح/خطأ، وأسئلة مفتوحة","feature.analytics.title":"تحليلات شاملة","feature.analytics.description":"تقارير مفصلة عن أداء الطلاب ونقاط القوة والضعف","feature.realtime.title":"نتائج فورية","feature.realtime.description":"تصحيح تلقائي وتقييم فوري مع ملاحظات مفصلة","feature.multilang.title":"دعم متعدد اللغات","feature.multilang.description":"يدعم العربية والإنجليزية مع واجهة سهلة الاستخدام","feature.secure.title":"آمن وموثوق","feature.secure.description":"حماية عالية للبيانات مع نسخ احتياطية تلقائية","pricing.title":"خطط تناسب احتياجاتك","pricing.subtitle":"ابدأ مجاناً وارتقِ حسب نموك","pricing.monthly":"شهري","pricing.yearly":"سنوي","pricing.save":"وفر 20%","pricing.popular":"الأكثر شعبية","pricing.questions":"لديك أسئلة؟","pricing.contact_text":"فريقنا هنا لمساعدتك في اختيار الخطة المناسبة","pricing.contact_us":"تواصل معنا","plan.free.name":"مجاني","plan.free.price":"0","plan.free.period":"/شهر","plan.free.description":"مثالي للمعلمين المبتدئين","plan.pro.name":"احترافي","plan.pro.price":"19","plan.pro.period":"/شهر","plan.pro.description":"للمعلمين النشطين","plan.enterprise.name":"مؤسسات","plan.enterprise.price":"99","plan.enterprise.period":"/شهر","plan.enterprise.description":"للمدارس والجامعات","pricing.cta":"ابدأ الآن","pricing.contact":"تواصل معنا","footer.product":"المنتج","footer.company":"الشركة","footer.support":"الدعم","footer.legal":"قانوني","footer.rights":"جميع الحقوق محفوظة","common.loading":"جاري التحميل...","common.error":"حدث خطأ","common.success":"تم بنجاح","common.cancel":"إلغاء","common.save":"حفظ","common.edit":"تعديل","common.delete":"حذف","common.view":"عرض","auth.login":"تسجيل الدخول","auth.register":"إنشاء حساب","auth.email":"البريد الإلكتروني","auth.password":"كلمة المرور","auth.confirmPassword":"تأكيد كلمة المرور","auth.fullName":"الاسم الكامل","auth.role":"الدور","auth.teacher":"معلم","auth.student":"طالب","auth.admin":"مدير","auth.forgotPassword":"نسيت كلمة المرور؟","auth.rememberMe":"تذكرني","auth.signInWith":"تسجيل الدخول باستخدام","auth.google":"جوجل","auth.facebook":"فيسبوك","auth.alreadyHaveAccount":"لديك حساب بالفعل؟","auth.dontHaveAccount":"ليس لديك حساب؟","auth.createAccount":"إنشاء حساب","auth.signIn":"تسجيل الدخول","auth.logout":"تسجيل الخروج","dashboard.title":"لوحة التحكم","dashboard.welcome":"مرحباً","dashboard.overview":"إليك نظرة عامة على نشاطك وإنجازاتك الأخيرة","dashboard.totalExams":"إجمالي الاختبارات","dashboard.averageScore":"متوسط النتائج","dashboard.totalQuestions":"إجمالي الأسئلة","dashboard.studyHours":"ساعات الدراسة","dashboard.recentExams":"الاختبارات الأخيرة","dashboard.quickActions":"إجراءات سريعة","dashboard.createExam":"إنشاء اختبار جديد","dashboard.viewAnalytics":"عرض التحليلات","dashboard.manageStudents":"إدارة الطلاب","dashboard.newAchievement":"إنجاز جديد!","dashboard.congratulations":"تهانينا! لقد حققت إنجازاً رائعاً في رحلتك التعليمية","nav.dashboard":"لوحة التحكم","nav.upload":"رفع ملف جديد","nav.exams":"الاختبارات","nav.analytics":"التحليلات","nav.history":"السجل","nav.students":"الطلاب","nav.profile":"الملف الشخصي","nav.settings":"الإعدادات"},en:{"nav.features":"Features","nav.pricing":"Pricing","nav.about":"About","nav.contact":"Contact","nav.login":"Login","nav.signup":"Sign Up","hero.title":"Transform PDFs into Interactive Exams","hero.subtitle":"in Minutes","hero.description":"Use AI to convert any PDF into interactive exams with advanced analytics and automatic grading. Perfect for teachers, students, and e-learning platforms.","hero.cta.primary":"Get Started Free","hero.cta.secondary":"Watch Demo","hero.trusted":"Trusted by 10,000+ educators worldwide","features.title":"Powerful Features for Better Learning","features.subtitle":"Everything you need to create professional exams and analyze results","feature.ai.title":"Advanced AI","feature.ai.description":"Analyzes PDF content and generates diverse questions automatically","feature.interactive.title":"Interactive Exams","feature.interactive.description":"Multiple choice, true/false, and open-ended questions","feature.analytics.title":"Comprehensive Analytics","feature.analytics.description":"Detailed reports on student performance and learning gaps","feature.realtime.title":"Real-time Results","feature.realtime.description":"Automatic grading and instant feedback with detailed explanations","feature.multilang.title":"Multi-language Support","feature.multilang.description":"Supports Arabic and English with intuitive interface","feature.secure.title":"Secure & Reliable","feature.secure.description":"Enterprise-grade security with automatic backups","pricing.title":"Plans That Fit Your Needs","pricing.subtitle":"Start free and scale as you grow","pricing.monthly":"Monthly","pricing.yearly":"Yearly","pricing.save":"Save 20%","pricing.popular":"Most Popular","pricing.questions":"Have Questions?","pricing.contact_text":"Our team is here to help you choose the right plan","pricing.contact_us":"Contact Us","plan.free.name":"Free","plan.free.price":"0","plan.free.period":"/month","plan.free.description":"Perfect for getting started","plan.pro.name":"Professional","plan.pro.price":"19","plan.pro.period":"/month","plan.pro.description":"For active educators","plan.enterprise.name":"Enterprise","plan.enterprise.price":"99","plan.enterprise.period":"/month","plan.enterprise.description":"For schools and universities","pricing.cta":"Get Started","pricing.contact":"Contact Sales","footer.product":"Product","footer.company":"Company","footer.support":"Support","footer.legal":"Legal","footer.rights":"All rights reserved","common.loading":"Loading...","common.error":"An error occurred","common.success":"Success","common.cancel":"Cancel","common.save":"Save","common.edit":"Edit","common.delete":"Delete","common.view":"View","auth.login":"Login","auth.register":"Sign Up","auth.email":"Email","auth.password":"Password","auth.confirmPassword":"Confirm Password","auth.fullName":"Full Name","auth.role":"Role","auth.teacher":"Teacher","auth.student":"Student","auth.admin":"Admin","auth.forgotPassword":"Forgot Password?","auth.rememberMe":"Remember Me","auth.signInWith":"Sign in with","auth.google":"Google","auth.facebook":"Facebook","auth.alreadyHaveAccount":"Already have an account?","auth.dontHaveAccount":"Don't have an account?","auth.createAccount":"Create Account","auth.signIn":"Sign In","auth.logout":"Logout","dashboard.title":"Dashboard","dashboard.welcome":"Welcome","dashboard.overview":"Here's an overview of your recent activity and achievements","dashboard.totalExams":"Total Exams","dashboard.averageScore":"Average Score","dashboard.totalQuestions":"Total Questions","dashboard.studyHours":"Study Hours","dashboard.recentExams":"Recent Exams","dashboard.quickActions":"Quick Actions","dashboard.createExam":"Create New Exam","dashboard.viewAnalytics":"View Analytics","dashboard.manageStudents":"Manage Students","dashboard.newAchievement":"New Achievement!","dashboard.congratulations":"Congratulations! You have achieved a great milestone in your learning journey","nav.dashboard":"Dashboard","nav.upload":"Upload New File","nav.exams":"Exams","nav.analytics":"Analytics","nav.history":"History","nav.students":"Students","nav.profile":"Profile","nav.settings":"Settings"}};function o({children:e}){let[t,r]=(0,s.useState)("en"),[o,l]=(0,s.useState)("light");return(0,a.jsx)(n.Provider,{value:{language:t,theme:o,setLanguage:r,setTheme:l,t:e=>i[t][e]||e},children:e})}function l(){let e=(0,s.useContext)(n);if(void 0===e)throw Error("useLanguage must be used within a LanguageProvider");return e}},42029:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},48747:(e,t,r)=>{"use strict";r.d(t,{AppContent:()=>L});var a=r(60687),s=r(7613),n=r(34393),i=r(16189),o=r(85814),l=r.n(o),c=r(43210),d=r(32192),m=r(10022),h=r(53411),u=r(84027),x=r(82080),p=r(58869),g=r(11860),f=r(12941),v=r(40083);function b(){let[e,t]=(0,c.useState)(!1),[r,n]=(0,c.useState)(!1),o=(0,i.usePathname)(),{user:b,logout:y}=(0,s.A)(),j=b?[{name:"لوحة التحكم",href:"/dashboard",icon:d.A},{name:"رفع ملف",href:"/dashboard/upload",icon:m.A},{name:"السجل",href:"/dashboard/history",icon:h.A},{name:"الإعدادات",href:"/settings",icon:u.A}]:[{name:"الرئيسية",href:"/",icon:d.A}],w=()=>{y(),n(!1)},N=e=>"/"===e?"/"===o:o.startsWith(e);return(0,a.jsxs)("nav",{className:"bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50",children:[(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex justify-between h-16",children:[(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsxs)(l(),{href:"/",className:"flex items-center space-x-2 rtl:space-x-reverse",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center",children:(0,a.jsx)(x.A,{className:"w-5 h-5 text-white"})}),(0,a.jsx)("span",{className:"text-xl font-bold text-gray-900 hidden sm:block",children:"منصة الاختبارات التفاعلية"}),(0,a.jsx)("span",{className:"text-xl font-bold text-gray-900 sm:hidden",children:"الاختبارات"})]})}),(0,a.jsxs)("div",{className:"hidden md:flex items-center space-x-8 rtl:space-x-reverse",children:[j.map(e=>{let t=e.icon;return(0,a.jsxs)(l(),{href:e.href,className:`flex items-center space-x-1 rtl:space-x-reverse px-3 py-2 rounded-md text-sm font-medium transition-colors ${N(e.href)?"text-blue-600 bg-blue-50":"text-gray-600 hover:text-gray-900 hover:bg-gray-50"}`,children:[(0,a.jsx)(t,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:e.name})]},e.name)}),b?(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("button",{onClick:()=>n(!r),className:"flex items-center space-x-2 rtl:space-x-reverse px-3 py-2 rounded-md text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center",children:b.photoURL?(0,a.jsx)("img",{src:b.photoURL,alt:b?.displayName||"User",className:"w-8 h-8 rounded-full object-cover"}):(0,a.jsx)(p.A,{className:"w-4 h-4 text-white"})}),(0,a.jsx)("span",{children:b?.displayName||b?.email?.split("@")[0]||"المستخدم"})]}),r&&(0,a.jsx)("div",{className:"absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-50",children:(0,a.jsxs)("div",{className:"py-1",children:[(0,a.jsx)(l(),{href:"/dashboard",className:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50",onClick:()=>n(!1),children:"لوحة التحكم"}),(0,a.jsx)(l(),{href:"/settings",className:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50",onClick:()=>n(!1),children:"الإعدادات"}),(0,a.jsx)("button",{onClick:w,className:"w-full text-right block px-4 py-2 text-sm text-red-700 hover:bg-red-50",children:"تسجيل الخروج"})]})})]}):(0,a.jsx)(l(),{href:"/login",className:"bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 transition-colors",children:"تسجيل الدخول"})]}),(0,a.jsx)("div",{className:"md:hidden flex items-center",children:(0,a.jsx)("button",{onClick:()=>t(!e),className:"p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500","aria-label":"Toggle menu",children:e?(0,a.jsx)(g.A,{className:"w-6 h-6"}):(0,a.jsx)(f.A,{className:"w-6 h-6"})})})]})}),e&&(0,a.jsx)("div",{className:"md:hidden border-t border-gray-200 bg-white",children:(0,a.jsxs)("div",{className:"px-2 pt-2 pb-3 space-y-1",children:[j.map(e=>{let r=e.icon;return(0,a.jsxs)(l(),{href:e.href,onClick:()=>t(!1),className:`flex items-center space-x-2 rtl:space-x-reverse px-3 py-2 rounded-md text-base font-medium transition-colors ${N(e.href)?"text-blue-600 bg-blue-50":"text-gray-600 hover:text-gray-900 hover:bg-gray-50"}`,children:[(0,a.jsx)(r,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:e.name})]},e.name)}),b?(0,a.jsxs)("div",{className:"border-t border-gray-200 pt-3 mt-3",children:[(0,a.jsxs)("div",{className:"flex items-center px-3 py-2 mb-2",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center ml-3",children:b.photoURL?(0,a.jsx)("img",{src:b.photoURL,alt:b?.displayName||"User",className:"w-8 h-8 rounded-full object-cover"}):(0,a.jsx)(p.A,{className:"w-4 h-4 text-white"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:b?.displayName||b?.email?.split("@")[0]||"المستخدم"}),(0,a.jsx)("div",{className:"text-xs text-gray-500 capitalize",children:"مستخدم"})]})]}),(0,a.jsxs)(l(),{href:"/dashboard",onClick:()=>t(!1),className:"flex items-center space-x-2 rtl:space-x-reverse px-3 py-2 rounded-md text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50",children:[(0,a.jsx)(h.A,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:"لوحة التحكم"})]}),(0,a.jsxs)("button",{onClick:()=>{w(),t(!1)},className:"w-full flex items-center space-x-2 rtl:space-x-reverse px-3 py-2 rounded-md text-base font-medium text-red-600 hover:text-red-900 hover:bg-red-50",children:[(0,a.jsx)(v.A,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:"تسجيل الخروج"})]})]}):(0,a.jsx)("div",{className:"border-t border-gray-200 pt-3 mt-3",children:(0,a.jsx)(l(),{href:"/login",onClick:()=>t(!1),className:"block px-3 py-2 rounded-md text-base font-medium bg-blue-600 text-white hover:bg-blue-700 text-center",children:"تسجيل الدخول"})})]})})]})}var y=r(99891),j=r(78005),w=r(41550);function N(){let e=new Date().getFullYear();return(0,a.jsx)("footer",{className:"bg-gray-900 text-white",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:[(0,a.jsxs)("div",{className:"lg:col-span-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center",children:(0,a.jsx)(m.A,{className:"w-5 h-5 text-white"})}),(0,a.jsx)("span",{className:"text-xl font-bold",children:"PDF Exam Generator"})]}),(0,a.jsx)("p",{className:"text-gray-400 text-sm mb-4",children:"Transform your PDF documents into interactive exams using AI. Perfect for educators, students, and e-learning platforms."}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-400",children:[(0,a.jsx)(y.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Secure & Privacy-focused"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-semibold text-gray-300 uppercase tracking-wider mb-4",children:"Product"}),(0,a.jsx)("ul",{className:"space-y-2",children:[{name:"Features",href:"#features"},{name:"How it Works",href:"#how-it-works"},{name:"Pricing",href:"#pricing"},{name:"API Documentation",href:"#api"}].map(e=>(0,a.jsx)("li",{children:(0,a.jsx)(l(),{href:e.href,className:"text-gray-400 hover:text-white text-sm transition-colors",children:e.name})},e.name))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-semibold text-gray-300 uppercase tracking-wider mb-4",children:"Legal"}),(0,a.jsx)("ul",{className:"space-y-2",children:[{name:"Privacy Policy",href:"/privacy"},{name:"Terms of Service",href:"/terms"},{name:"Cookie Policy",href:"/cookies"},{name:"GDPR Compliance",href:"/gdpr"}].map(e=>(0,a.jsx)("li",{children:(0,a.jsx)(l(),{href:e.href,className:"text-gray-400 hover:text-white text-sm transition-colors",children:e.name})},e.name))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-semibold text-gray-300 uppercase tracking-wider mb-4",children:"Support"}),(0,a.jsx)("ul",{className:"space-y-2",children:[{name:"Help Center",href:"/help"},{name:"Contact Us",href:"/contact"},{name:"Bug Reports",href:"/bugs"},{name:"Feature Requests",href:"/features"}].map(e=>(0,a.jsx)("li",{children:(0,a.jsx)(l(),{href:e.href,className:"text-gray-400 hover:text-white text-sm transition-colors",children:e.name})},e.name))})]})]}),(0,a.jsx)("div",{className:"mt-8 pt-8 border-t border-gray-800",children:(0,a.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center",children:[(0,a.jsx)("div",{className:"flex items-center space-x-4 text-sm text-gray-400",children:(0,a.jsxs)("span",{children:["\xa9 ",e," PDF Exam Generator. All rights reserved."]})}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 mt-4 md:mt-0",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-400",children:[(0,a.jsx)(j.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"AI-Powered"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-400",children:[(0,a.jsx)(w.A,{className:"w-4 h-4"}),(0,a.jsx)(l(),{href:"mailto:<EMAIL>",className:"hover:text-white transition-colors",children:"<EMAIL>"})]})]})]})})]})})}var A=r(49625),k=r(16023),P=r(39916),C=r(41312),E=r(99270),S=r(363),D=r(21134),I=r(97051);function F({children:e}){let[t,r]=(0,c.useState)(!1),{user:o,logout:d}=(0,s.A)(),{theme:m,setTheme:b,t:y}=(0,n.o)(),j=(0,i.usePathname)(),w=[{name:y("nav.dashboard"),href:"/dashboard",icon:A.A},{name:y("nav.upload"),href:"/dashboard/upload",icon:k.A},{name:y("nav.exams"),href:"/dashboard/exams",icon:x.A},{name:y("nav.analytics"),href:"/dashboard/analytics",icon:h.A},{name:y("nav.history"),href:"/dashboard/history",icon:P.A},{name:y("nav.students"),href:"/dashboard/students",icon:C.A},{name:y("nav.profile"),href:"/profile",icon:p.A},{name:y("nav.settings"),href:"/settings",icon:u.A}];return(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 flex",children:[t&&(0,a.jsx)("div",{className:"fixed inset-0 z-40 lg:hidden",onClick:()=>r(!1),children:(0,a.jsx)("div",{className:"absolute inset-0 bg-gray-600 opacity-75"})}),(0,a.jsxs)("div",{className:`fixed inset-y-0 right-0 z-50 w-64 bg-white dark:bg-gray-800 shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0 ${t?"translate-x-0":"translate-x-full"}`,children:[(0,a.jsxs)("div",{className:"flex items-center justify-between h-16 px-6 border-b border-gray-200 dark:border-gray-700",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center",children:(0,a.jsx)(x.A,{className:"w-5 h-5 text-white"})}),(0,a.jsx)("span",{className:"mr-3 text-lg font-semibold text-gray-900 dark:text-white",children:"ExamAI"})]}),(0,a.jsx)("button",{onClick:()=>r(!1),className:"lg:hidden text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300",children:(0,a.jsx)(g.A,{className:"w-6 h-6"})})]}),(0,a.jsx)("div",{className:"p-6 border-b border-gray-200 dark:border-gray-700",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center",children:o?.photoURL?(0,a.jsx)("img",{src:o.photoURL,alt:o?.displayName||"User",className:"w-12 h-12 rounded-full object-cover"}):(0,a.jsx)(p.A,{className:"w-6 h-6 text-white"})}),(0,a.jsxs)("div",{className:"mr-3",children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:o?.displayName||o?.email?.split("@")[0]||"المستخدم"}),(0,a.jsx)("div",{className:"text-xs text-gray-500 dark:text-gray-400 capitalize",children:"مستخدم"})]})]})}),(0,a.jsx)("nav",{className:"mt-6 px-3",children:(0,a.jsx)("div",{className:"space-y-1",children:w.map(e=>{let t=j===e.href||j.startsWith(e.href+"/");return(0,a.jsxs)(l(),{href:e.href,className:`group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors ${t?"bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900 dark:to-purple-900 text-blue-700 dark:text-blue-300 border-l-4 border-blue-700 dark:border-blue-400":"text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-white"}`,children:[(0,a.jsx)(e.icon,{className:`ml-3 w-5 h-5 ${t?"text-blue-700 dark:text-blue-300":"text-gray-400 dark:text-gray-500 group-hover:text-gray-500 dark:group-hover:text-gray-400"}`}),e.name]},e.name)})})}),(0,a.jsx)("div",{className:"mt-8 px-6",children:(0,a.jsxs)("div",{className:"bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900 dark:to-purple-900 rounded-lg p-4",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-900 dark:text-white mb-3",children:"إحصائياتك"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between text-xs",children:[(0,a.jsx)("span",{className:"text-gray-600 dark:text-gray-400",children:"الاختبارات"}),(0,a.jsx)("span",{className:"font-medium text-gray-900 dark:text-white",children:"24"})]}),(0,a.jsxs)("div",{className:"flex justify-between text-xs",children:[(0,a.jsx)("span",{className:"text-gray-600 dark:text-gray-400",children:"المعدل"}),(0,a.jsx)("span",{className:"font-medium text-gray-900 dark:text-white",children:"85%"})]}),(0,a.jsxs)("div",{className:"flex justify-between text-xs",children:[(0,a.jsx)("span",{className:"text-gray-600 dark:text-gray-400",children:"الأسئلة"}),(0,a.jsx)("span",{className:"font-medium text-gray-900 dark:text-white",children:"342"})]})]})]})}),(0,a.jsx)("div",{className:"absolute bottom-6 left-0 right-0 px-6",children:(0,a.jsxs)("button",{onClick:()=>{d()},className:"w-full flex items-center px-3 py-2 text-sm font-medium text-red-700 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900 rounded-lg transition-colors",children:[(0,a.jsx)(v.A,{className:"ml-3 w-5 h-5"}),"تسجيل الخروج"]})})]}),(0,a.jsxs)("div",{className:"flex-1 flex flex-col lg:mr-64",children:[(0,a.jsx)("header",{className:"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700",children:(0,a.jsxs)("div",{className:"flex items-center justify-between h-16 px-6",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("button",{onClick:()=>r(!0),className:"lg:hidden text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300",children:(0,a.jsx)(f.A,{className:"w-6 h-6"})}),(0,a.jsx)("div",{className:"lg:hidden mr-4",children:(0,a.jsx)("h1",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"لوحة التحكم"})})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 rtl:space-x-reverse",children:[(0,a.jsxs)("div",{className:"hidden md:block relative",children:[(0,a.jsx)(E.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500 w-4 h-4"}),(0,a.jsx)("input",{type:"text",placeholder:"البحث...",className:"w-64 pl-4 pr-10 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"})]}),(0,a.jsx)("button",{onClick:()=>{b("light"===m?"dark":"light")},className:"p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors",children:"light"===m?(0,a.jsx)(S.A,{className:"w-5 h-5"}):(0,a.jsx)(D.A,{className:"w-5 h-5"})}),(0,a.jsxs)("button",{className:"relative p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg",children:[(0,a.jsx)(I.A,{className:"w-5 h-5"}),(0,a.jsx)("span",{className:"absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full"})]}),(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsx)("div",{className:"w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center",children:o?.photoURL?(0,a.jsx)("img",{src:o.photoURL,alt:o?.displayName||"User",className:"w-8 h-8 rounded-full object-cover"}):(0,a.jsx)(p.A,{className:"w-4 h-4 text-white"})})})]})]})}),(0,a.jsx)("main",{className:"flex-1 overflow-auto bg-gray-50 dark:bg-gray-900",children:e})]})]})}function L({children:e}){let{isAuthenticated:t,loading:r}=(0,s.A)(),{theme:o}=(0,n.o)(),l=(0,i.usePathname)();return r?(0,a.jsx)("div",{className:`min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 ${"dark"===o?"dark":""}`,children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"جاري التحميل..."})]})}):l.startsWith("/auth/")?(0,a.jsx)(a.Fragment,{children:e}):t&&(l.startsWith("/dashboard")||"/analyze"===l||"/exam"===l||"/results"===l||"/settings"===l)?(0,a.jsx)("div",{className:"dark"===o?"dark":"",children:(0,a.jsx)(F,{children:e})}):(0,a.jsxs)("div",{className:`min-h-screen flex flex-col ${"dark"===o?"dark":""}`,children:[(0,a.jsx)(b,{}),(0,a.jsx)("main",{className:"flex-1",children:e}),(0,a.jsx)(N,{})]})}},56093:(e,t,r)=>{Promise.resolve().then(r.bind(r,73345)),Promise.resolve().then(r.bind(r,29131)),Promise.resolve().then(r.bind(r,7043))},61135:()=>{},73345:(e,t,r)=>{"use strict";r.d(t,{AppContent:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call AppContent() from the server but AppContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\templete\\pdf-exam-generator\\src\\components\\layout\\AppContent.tsx","AppContent")},74653:(e,t,r)=>{Promise.resolve().then(r.bind(r,48747)),Promise.resolve().then(r.bind(r,7613)),Promise.resolve().then(r.bind(r,34393))},82349:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>m,metadata:()=>c,viewport:()=>d});var a=r(37413),s=r(25091),n=r.n(s);r(61135);var i=r(29131),o=r(7043),l=r(73345);let c={title:"PDF to Interactive Exam Generator",description:"Transform PDF documents into interactive exams using AI. Perfect for teachers, students, and e-learning platforms.",keywords:["PDF","exam","interactive","AI","education","e-learning"],authors:[{name:"PDF Exam Generator Team"}]},d={width:"device-width",initialScale:1};function m({children:e}){return(0,a.jsx)("html",{lang:"en",className:n().variable,children:(0,a.jsx)("body",{className:"font-inter antialiased bg-gray-50 text-gray-900 min-h-screen",children:(0,a.jsx)(o.LanguageProvider,{children:(0,a.jsx)(i.AuthProvider,{children:(0,a.jsx)(l.AppContent,{children:e})})})})})}}};