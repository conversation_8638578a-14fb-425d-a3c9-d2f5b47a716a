/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Ctemplete%5Clanding-page%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ctemplete%5Clanding-page&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Ctemplete%5Clanding-page%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ctemplete%5Clanding-page&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"D:\\\\templete\\\\landing-page\\\\src\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [module0, \"D:\\\\templete\\\\landing-page\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\templete\\\\landing-page\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZwYWdlJnBhZ2U9JTJGcGFnZSZhcHBQYXRocz0lMkZwYWdlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGcGFnZS50c3gmYXBwRGlyPUQlM0ElNUN0ZW1wbGV0ZSU1Q2xhbmRpbmctcGFnZSU1Q3NyYyU1Q2FwcCZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzJnJvb3REaXI9RCUzQSU1Q3RlbXBsZXRlJTVDbGFuZGluZy1wYWdlJmlzRGV2PXRydWUmdHNjb25maWdQYXRoPXRzY29uZmlnLmpzb24mYmFzZVBhdGg9JmFzc2V0UHJlZml4PSZuZXh0Q29uZmlnT3V0cHV0PWV4cG9ydCZwcmVmZXJyZWRSZWdpb249Jm1pZGRsZXdhcmVDb25maWc9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSxzQkFBc0Isb0pBQXFGO0FBQzNHLHNCQUFzQiwwTkFBZ0Y7QUFDdEcsc0JBQXNCLDBOQUFnRjtBQUN0RyxzQkFBc0IsZ09BQW1GO0FBQ3pHLG9CQUFvQixnSkFBbUY7QUFHckc7QUFHQTtBQUNGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQztBQUNqQztBQUNBO0FBQ0EsU0FBUztBQUNULE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ3VCO0FBR3JCO0FBQ0YsNkJBQTZCLG1CQUFtQjtBQUNoRDtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBR0U7QUFDRjtBQUNPLHdCQUF3Qix1R0FBa0I7QUFDakQ7QUFDQSxjQUFjLGtFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSxDQUFDOztBQUVEIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgbW9kdWxlMCA9ICgpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcdGVtcGxldGVcXFxcbGFuZGluZy1wYWdlXFxcXHNyY1xcXFxhcHBcXFxcbGF5b3V0LnRzeFwiKTtcbmNvbnN0IG1vZHVsZTEgPSAoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9ub3QtZm91bmQtZXJyb3JcIik7XG5jb25zdCBtb2R1bGUyID0gKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvZm9yYmlkZGVuLWVycm9yXCIpO1xuY29uc3QgbW9kdWxlMyA9ICgpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3VuYXV0aG9yaXplZC1lcnJvclwiKTtcbmNvbnN0IHBhZ2U0ID0gKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFx0ZW1wbGV0ZVxcXFxsYW5kaW5nLXBhZ2VcXFxcc3JjXFxcXGFwcFxcXFxwYWdlLnRzeFwiKTtcbmltcG9ydCB7IEFwcFBhZ2VSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLW1vZHVsZXMvYXBwLXBhZ2UvbW9kdWxlLmNvbXBpbGVkXCIgd2l0aCB7XG4gICAgJ3R1cmJvcGFjay10cmFuc2l0aW9uJzogJ25leHQtc3NyJ1xufTtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLWtpbmRcIiB3aXRoIHtcbiAgICAndHVyYm9wYWNrLXRyYW5zaXRpb24nOiAnbmV4dC1zZXJ2ZXItdXRpbGl0eSdcbn07XG4vLyBXZSBpbmplY3QgdGhlIHRyZWUgYW5kIHBhZ2VzIGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCB0cmVlID0ge1xuICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAnJyxcbiAgICAgICAge1xuICAgICAgICBjaGlsZHJlbjogWydfX1BBR0VfXycsIHt9LCB7XG4gICAgICAgICAgcGFnZTogW3BhZ2U0LCBcIkQ6XFxcXHRlbXBsZXRlXFxcXGxhbmRpbmctcGFnZVxcXFxzcmNcXFxcYXBwXFxcXHBhZ2UudHN4XCJdLFxuICAgICAgICAgIFxuICAgICAgICB9XVxuICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICAnbGF5b3V0JzogW21vZHVsZTAsIFwiRDpcXFxcdGVtcGxldGVcXFxcbGFuZGluZy1wYWdlXFxcXHNyY1xcXFxhcHBcXFxcbGF5b3V0LnRzeFwiXSxcbidub3QtZm91bmQnOiBbbW9kdWxlMSwgXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbm90LWZvdW5kLWVycm9yXCJdLFxuJ2ZvcmJpZGRlbic6IFttb2R1bGUyLCBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9mb3JiaWRkZW4tZXJyb3JcIl0sXG4ndW5hdXRob3JpemVkJzogW21vZHVsZTMsIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3VuYXV0aG9yaXplZC1lcnJvclwiXSxcbiAgICAgICAgXG4gICAgICB9XG4gICAgICBdXG4gICAgICB9LmNoaWxkcmVuO1xuY29uc3QgcGFnZXMgPSBbXCJEOlxcXFx0ZW1wbGV0ZVxcXFxsYW5kaW5nLXBhZ2VcXFxcc3JjXFxcXGFwcFxcXFxwYWdlLnRzeFwiXTtcbmV4cG9ydCB7IHRyZWUsIHBhZ2VzIH07XG5leHBvcnQgeyBkZWZhdWx0IGFzIEdsb2JhbEVycm9yIH0gZnJvbSBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9lcnJvci1ib3VuZGFyeVwiIHdpdGgge1xuICAgICd0dXJib3BhY2stdHJhbnNpdGlvbic6ICduZXh0LXNlcnZlci11dGlsaXR5J1xufTtcbmNvbnN0IF9fbmV4dF9hcHBfcmVxdWlyZV9fID0gX193ZWJwYWNrX3JlcXVpcmVfX1xuY29uc3QgX19uZXh0X2FwcF9sb2FkX2NodW5rX18gPSAoKSA9PiBQcm9taXNlLnJlc29sdmUoKVxuZXhwb3J0IGNvbnN0IF9fbmV4dF9hcHBfXyA9IHtcbiAgICByZXF1aXJlOiBfX25leHRfYXBwX3JlcXVpcmVfXyxcbiAgICBsb2FkQ2h1bms6IF9fbmV4dF9hcHBfbG9hZF9jaHVua19fXG59O1xuZXhwb3J0ICogZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvYXBwLXJlbmRlci9lbnRyeS1iYXNlXCIgd2l0aCB7XG4gICAgJ3R1cmJvcGFjay10cmFuc2l0aW9uJzogJ25leHQtc2VydmVyLXV0aWxpdHknXG59O1xuLy8gQ3JlYXRlIGFuZCBleHBvcnQgdGhlIHJvdXRlIG1vZHVsZSB0aGF0IHdpbGwgYmUgY29uc3VtZWQuXG5leHBvcnQgY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgQXBwUGFnZVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5BUFBfUEFHRSxcbiAgICAgICAgcGFnZTogXCIvcGFnZVwiLFxuICAgICAgICBwYXRobmFtZTogXCIvXCIsXG4gICAgICAgIC8vIFRoZSBmb2xsb3dpbmcgYXJlbid0IHVzZWQgaW4gcHJvZHVjdGlvbi5cbiAgICAgICAgYnVuZGxlUGF0aDogJycsXG4gICAgICAgIGZpbGVuYW1lOiAnJyxcbiAgICAgICAgYXBwUGF0aHM6IFtdXG4gICAgfSxcbiAgICB1c2VybGFuZDoge1xuICAgICAgICBsb2FkZXJUcmVlOiB0cmVlXG4gICAgfVxufSk7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFwcC1wYWdlLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Ctemplete%5Clanding-page%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ctemplete%5Clanding-page&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Cairo%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22arabic%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-cairo%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22cairo%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Csrc%5C%5Ccontexts%5C%5CLanguageContext.tsx%22%2C%22ids%22%3A%5B%22LanguageProvider%22%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Cairo%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22arabic%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-cairo%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22cairo%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Csrc%5C%5Ccontexts%5C%5CLanguageContext.tsx%22%2C%22ids%22%3A%5B%22LanguageProvider%22%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/LanguageContext.tsx */ \"(rsc)/./src/contexts/LanguageContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Cairo%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22arabic%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-cairo%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22cairo%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Csrc%5C%5Ccontexts%5C%5CLanguageContext.tsx%22%2C%22ids%22%3A%5B%22LanguageProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Csrc%5C%5Ccomponents%5C%5CFeatures.tsx%22%2C%22ids%22%3A%5B%22Features%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Csrc%5C%5Ccomponents%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22Footer%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Csrc%5C%5Ccomponents%5C%5CHero.tsx%22%2C%22ids%22%3A%5B%22Hero%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Csrc%5C%5Ccomponents%5C%5CPricing.tsx%22%2C%22ids%22%3A%5B%22Pricing%22%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Csrc%5C%5Ccomponents%5C%5CFeatures.tsx%22%2C%22ids%22%3A%5B%22Features%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Csrc%5C%5Ccomponents%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22Footer%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Csrc%5C%5Ccomponents%5C%5CHero.tsx%22%2C%22ids%22%3A%5B%22Hero%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Csrc%5C%5Ccomponents%5C%5CPricing.tsx%22%2C%22ids%22%3A%5B%22Pricing%22%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Features.tsx */ \"(rsc)/./src/components/Features.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Footer.tsx */ \"(rsc)/./src/components/Footer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Header.tsx */ \"(rsc)/./src/components/Header.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Hero.tsx */ \"(rsc)/./src/components/Hero.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Pricing.tsx */ \"(rsc)/./src/components/Pricing.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Csrc%5C%5Ccomponents%5C%5CFeatures.tsx%22%2C%22ids%22%3A%5B%22Features%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Csrc%5C%5Ccomponents%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22Footer%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Csrc%5C%5Ccomponents%5C%5CHero.tsx%22%2C%22ids%22%3A%5B%22Hero%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Csrc%5C%5Ccomponents%5C%5CPricing.tsx%22%2C%22ids%22%3A%5B%22Pricing%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"56426b9029d1\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJEOlxcdGVtcGxldGVcXGxhbmRpbmctcGFnZVxcc3JjXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNTY0MjZiOTAyOWQxXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   viewport: () => (/* binding */ viewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Cairo_arguments_subsets_arabic_variable_font_cairo_variableName_cairo___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Cairo\",\"arguments\":[{\"subsets\":[\"arabic\"],\"variable\":\"--font-cairo\"}],\"variableName\":\"cairo\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Cairo\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"arabic\\\"],\\\"variable\\\":\\\"--font-cairo\\\"}],\\\"variableName\\\":\\\"cairo\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Cairo_arguments_subsets_arabic_variable_font_cairo_variableName_cairo___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Cairo_arguments_subsets_arabic_variable_font_cairo_variableName_cairo___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/LanguageContext */ \"(rsc)/./src/contexts/LanguageContext.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: \"ExamAI - Transform PDFs into Interactive Exams\",\n    description: \"Use AI to convert any PDF into interactive exams with advanced analytics and automatic grading. Perfect for teachers, students, and e-learning platforms.\",\n    keywords: [\n        \"PDF\",\n        \"exam\",\n        \"interactive\",\n        \"AI\",\n        \"education\",\n        \"e-learning\",\n        \"teacher\",\n        \"student\"\n    ],\n    authors: [\n        {\n            name: \"ExamAI Team\"\n        }\n    ],\n    openGraph: {\n        title: \"ExamAI - Transform PDFs into Interactive Exams\",\n        description: \"Use AI to convert any PDF into interactive exams with advanced analytics and automatic grading.\",\n        type: \"website\",\n        locale: \"en_US\",\n        alternateLocale: \"ar_SA\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"ExamAI - Transform PDFs into Interactive Exams\",\n        description: \"Use AI to convert any PDF into interactive exams with advanced analytics and automatic grading.\"\n    }\n};\nconst viewport = {\n    width: 'device-width',\n    initialScale: 1\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        href: \"https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Cairo:wght@300;400;500;600;700&display=swap\",\n                        rel: \"stylesheet\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Cairo_arguments_subsets_arabic_variable_font_cairo_variableName_cairo___WEBPACK_IMPORTED_MODULE_4___default().variable)} font-sans antialiased`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_2__.LanguageProvider, {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 46,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Header */ \"(rsc)/./src/components/Header.tsx\");\n/* harmony import */ var _components_Hero__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Hero */ \"(rsc)/./src/components/Hero.tsx\");\n/* harmony import */ var _components_Features__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Features */ \"(rsc)/./src/components/Features.tsx\");\n/* harmony import */ var _components_Pricing__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Pricing */ \"(rsc)/./src/components/Pricing.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/Footer */ \"(rsc)/./src/components/Footer.tsx\");\n\n\n\n\n\n\nfunction HomePage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-white dark:bg-gray-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_1__.Header, {}, void 0, false, {\n                fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Hero__WEBPACK_IMPORTED_MODULE_2__.Hero, {}, void 0, false, {\n                        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 12,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Features__WEBPACK_IMPORTED_MODULE_3__.Features, {}, void 0, false, {\n                        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Pricing__WEBPACK_IMPORTED_MODULE_4__.Pricing, {}, void 0, false, {\n                        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 11,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_5__.Footer, {}, void 0, false, {\n                fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUE2QztBQUNKO0FBQ1E7QUFDRjtBQUNGO0FBRTlCLFNBQVNLO0lBQ3RCLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVOzswQkFDYiw4REFBQ1Asc0RBQU1BOzs7OzswQkFDUCw4REFBQ1E7O2tDQUNDLDhEQUFDUCxrREFBSUE7Ozs7O2tDQUNMLDhEQUFDQywwREFBUUE7Ozs7O2tDQUNULDhEQUFDQyx3REFBT0E7Ozs7Ozs7Ozs7OzBCQUVWLDhEQUFDQyxzREFBTUE7Ozs7Ozs7Ozs7O0FBR2IiLCJzb3VyY2VzIjpbIkQ6XFx0ZW1wbGV0ZVxcbGFuZGluZy1wYWdlXFxzcmNcXGFwcFxccGFnZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgSGVhZGVyIH0gZnJvbSAnQC9jb21wb25lbnRzL0hlYWRlcic7XG5pbXBvcnQgeyBIZXJvIH0gZnJvbSAnQC9jb21wb25lbnRzL0hlcm8nO1xuaW1wb3J0IHsgRmVhdHVyZXMgfSBmcm9tICdAL2NvbXBvbmVudHMvRmVhdHVyZXMnO1xuaW1wb3J0IHsgUHJpY2luZyB9IGZyb20gJ0AvY29tcG9uZW50cy9QcmljaW5nJztcbmltcG9ydCB7IEZvb3RlciB9IGZyb20gJ0AvY29tcG9uZW50cy9Gb290ZXInO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBIb21lUGFnZSgpIHtcbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy13aGl0ZSBkYXJrOmJnLWdyYXktOTAwXCI+XG4gICAgICA8SGVhZGVyIC8+XG4gICAgICA8bWFpbj5cbiAgICAgICAgPEhlcm8gLz5cbiAgICAgICAgPEZlYXR1cmVzIC8+XG4gICAgICAgIDxQcmljaW5nIC8+XG4gICAgICA8L21haW4+XG4gICAgICA8Rm9vdGVyIC8+XG4gICAgPC9kaXY+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiSGVhZGVyIiwiSGVybyIsIkZlYXR1cmVzIiwiUHJpY2luZyIsIkZvb3RlciIsIkhvbWVQYWdlIiwiZGl2IiwiY2xhc3NOYW1lIiwibWFpbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/Features.tsx":
/*!*************************************!*\
  !*** ./src/components/Features.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Features: () => (/* binding */ Features)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Features = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Features() from the server but Features is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\templete\\landing-page\\src\\components\\Features.tsx",
"Features",
);

/***/ }),

/***/ "(rsc)/./src/components/Footer.tsx":
/*!***********************************!*\
  !*** ./src/components/Footer.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Footer: () => (/* binding */ Footer)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Footer = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Footer() from the server but Footer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\templete\\landing-page\\src\\components\\Footer.tsx",
"Footer",
);

/***/ }),

/***/ "(rsc)/./src/components/Header.tsx":
/*!***********************************!*\
  !*** ./src/components/Header.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Header: () => (/* binding */ Header)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Header = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Header() from the server but Header is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\templete\\landing-page\\src\\components\\Header.tsx",
"Header",
);

/***/ }),

/***/ "(rsc)/./src/components/Hero.tsx":
/*!*********************************!*\
  !*** ./src/components/Hero.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Hero: () => (/* binding */ Hero)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Hero = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Hero() from the server but Hero is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\templete\\landing-page\\src\\components\\Hero.tsx",
"Hero",
);

/***/ }),

/***/ "(rsc)/./src/components/Pricing.tsx":
/*!************************************!*\
  !*** ./src/components/Pricing.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Pricing: () => (/* binding */ Pricing)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Pricing = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Pricing() from the server but Pricing is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\templete\\landing-page\\src\\components\\Pricing.tsx",
"Pricing",
);

/***/ }),

/***/ "(rsc)/./src/contexts/LanguageContext.tsx":
/*!******************************************!*\
  !*** ./src/contexts/LanguageContext.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   LanguageProvider: () => (/* binding */ LanguageProvider),
/* harmony export */   useLanguage: () => (/* binding */ useLanguage)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const LanguageProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call LanguageProvider() from the server but LanguageProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\templete\\landing-page\\src\\contexts\\LanguageContext.tsx",
"LanguageProvider",
);const useLanguage = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useLanguage() from the server but useLanguage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\templete\\landing-page\\src\\contexts\\LanguageContext.tsx",
"useLanguage",
);

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Cairo%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22arabic%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-cairo%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22cairo%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Csrc%5C%5Ccontexts%5C%5CLanguageContext.tsx%22%2C%22ids%22%3A%5B%22LanguageProvider%22%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Cairo%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22arabic%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-cairo%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22cairo%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Csrc%5C%5Ccontexts%5C%5CLanguageContext.tsx%22%2C%22ids%22%3A%5B%22LanguageProvider%22%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/LanguageContext.tsx */ \"(ssr)/./src/contexts/LanguageContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Cairo%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22arabic%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-cairo%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22cairo%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Csrc%5C%5Ccontexts%5C%5CLanguageContext.tsx%22%2C%22ids%22%3A%5B%22LanguageProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Csrc%5C%5Ccomponents%5C%5CFeatures.tsx%22%2C%22ids%22%3A%5B%22Features%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Csrc%5C%5Ccomponents%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22Footer%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Csrc%5C%5Ccomponents%5C%5CHero.tsx%22%2C%22ids%22%3A%5B%22Hero%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Csrc%5C%5Ccomponents%5C%5CPricing.tsx%22%2C%22ids%22%3A%5B%22Pricing%22%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Csrc%5C%5Ccomponents%5C%5CFeatures.tsx%22%2C%22ids%22%3A%5B%22Features%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Csrc%5C%5Ccomponents%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22Footer%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Csrc%5C%5Ccomponents%5C%5CHero.tsx%22%2C%22ids%22%3A%5B%22Hero%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Csrc%5C%5Ccomponents%5C%5CPricing.tsx%22%2C%22ids%22%3A%5B%22Pricing%22%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Features.tsx */ \"(ssr)/./src/components/Features.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Footer.tsx */ \"(ssr)/./src/components/Footer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Header.tsx */ \"(ssr)/./src/components/Header.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Hero.tsx */ \"(ssr)/./src/components/Hero.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Pricing.tsx */ \"(ssr)/./src/components/Pricing.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Csrc%5C%5Ccomponents%5C%5CFeatures.tsx%22%2C%22ids%22%3A%5B%22Features%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Csrc%5C%5Ccomponents%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22Footer%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Csrc%5C%5Ccomponents%5C%5CHero.tsx%22%2C%22ids%22%3A%5B%22Hero%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctemplete%5C%5Clanding-page%5C%5Csrc%5C%5Ccomponents%5C%5CPricing.tsx%22%2C%22ids%22%3A%5B%22Pricing%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/Features.tsx":
/*!*************************************!*\
  !*** ./src/components/Features.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Features: () => (/* binding */ Features)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contexts/LanguageContext */ \"(ssr)/./src/contexts/LanguageContext.tsx\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Brain_FileText_Globe_MousePointer_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Brain,FileText,Globe,MousePointer,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Brain_FileText_Globe_MousePointer_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Brain,FileText,Globe,MousePointer,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mouse-pointer.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Brain_FileText_Globe_MousePointer_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Brain,FileText,Globe,MousePointer,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Brain_FileText_Globe_MousePointer_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Brain,FileText,Globe,MousePointer,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Brain_FileText_Globe_MousePointer_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Brain,FileText,Globe,MousePointer,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Brain_FileText_Globe_MousePointer_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Brain,FileText,Globe,MousePointer,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Brain_FileText_Globe_MousePointer_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Brain,FileText,Globe,MousePointer,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Brain_FileText_Globe_MousePointer_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Brain,FileText,Globe,MousePointer,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Brain_FileText_Globe_MousePointer_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Brain,FileText,Globe,MousePointer,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Brain_FileText_Globe_MousePointer_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Brain,FileText,Globe,MousePointer,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Brain_FileText_Globe_MousePointer_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Brain,FileText,Globe,MousePointer,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* __next_internal_client_entry_do_not_use__ Features auto */ \n\n\nfunction Features() {\n    const { t } = (0,_contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_1__.useLanguage)();\n    const features = [\n        {\n            icon: _barrel_optimize_names_BarChart3_Brain_FileText_Globe_MousePointer_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            title: t('feature.ai.title'),\n            description: t('feature.ai.description'),\n            color: 'blue',\n            gradient: 'from-blue-500 to-cyan-500'\n        },\n        {\n            icon: _barrel_optimize_names_BarChart3_Brain_FileText_Globe_MousePointer_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            title: t('feature.interactive.title'),\n            description: t('feature.interactive.description'),\n            color: 'purple',\n            gradient: 'from-purple-500 to-pink-500'\n        },\n        {\n            icon: _barrel_optimize_names_BarChart3_Brain_FileText_Globe_MousePointer_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            title: t('feature.analytics.title'),\n            description: t('feature.analytics.description'),\n            color: 'green',\n            gradient: 'from-green-500 to-emerald-500'\n        },\n        {\n            icon: _barrel_optimize_names_BarChart3_Brain_FileText_Globe_MousePointer_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            title: t('feature.realtime.title'),\n            description: t('feature.realtime.description'),\n            color: 'yellow',\n            gradient: 'from-yellow-500 to-orange-500'\n        },\n        {\n            icon: _barrel_optimize_names_BarChart3_Brain_FileText_Globe_MousePointer_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            title: t('feature.multilang.title'),\n            description: t('feature.multilang.description'),\n            color: 'indigo',\n            gradient: 'from-indigo-500 to-blue-500'\n        },\n        {\n            icon: _barrel_optimize_names_BarChart3_Brain_FileText_Globe_MousePointer_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            title: t('feature.secure.title'),\n            description: t('feature.secure.description'),\n            color: 'red',\n            gradient: 'from-red-500 to-pink-500'\n        }\n    ];\n    const stats = [\n        {\n            icon: _barrel_optimize_names_BarChart3_Brain_FileText_Globe_MousePointer_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            number: '10,000+',\n            label: 'PDFs Processed',\n            color: 'blue'\n        },\n        {\n            icon: _barrel_optimize_names_BarChart3_Brain_FileText_Globe_MousePointer_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            number: '5,000+',\n            label: 'Active Teachers',\n            color: 'green'\n        },\n        {\n            icon: _barrel_optimize_names_BarChart3_Brain_FileText_Globe_MousePointer_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            number: '100,000+',\n            label: 'Exams Created',\n            color: 'purple'\n        },\n        {\n            icon: _barrel_optimize_names_BarChart3_Brain_FileText_Globe_MousePointer_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            number: '95%',\n            label: 'Satisfaction Rate',\n            color: 'orange'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"features\",\n        className: \"py-20 bg-white dark:bg-gray-900\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-6\",\n                            children: t('features.title')\n                        }, void 0, false, {\n                            fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Features.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto\",\n                            children: t('features.subtitle')\n                        }, void 0, false, {\n                            fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Features.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Features.tsx\",\n                    lineNumber: 98,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 md:grid-cols-4 gap-8 mb-20\",\n                    children: stats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `w-16 h-16 mx-auto mb-4 rounded-2xl flex items-center justify-center ${stat.color === 'blue' ? 'bg-blue-100 dark:bg-blue-900' : stat.color === 'green' ? 'bg-green-100 dark:bg-green-900' : stat.color === 'purple' ? 'bg-purple-100 dark:bg-purple-900' : 'bg-orange-100 dark:bg-orange-900'}`,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(stat.icon, {\n                                        className: `w-8 h-8 ${stat.color === 'blue' ? 'text-blue-600 dark:text-blue-400' : stat.color === 'green' ? 'text-green-600 dark:text-green-400' : stat.color === 'purple' ? 'text-purple-600 dark:text-purple-400' : 'text-orange-600 dark:text-orange-400'}`\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Features.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Features.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-3xl font-bold text-gray-900 dark:text-white mb-2\",\n                                    children: stat.number\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Features.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-600 dark:text-gray-400\",\n                                    children: stat.label\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Features.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Features.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Features.tsx\",\n                    lineNumber: 108,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                    children: features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"group relative bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-xl transition-all duration-300 hover:-translate-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `absolute inset-0 bg-gradient-to-br ${feature.gradient} opacity-0 group-hover:opacity-5 rounded-2xl transition-opacity duration-300`\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Features.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `relative w-16 h-16 bg-gradient-to-br ${feature.gradient} rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                        className: \"w-8 h-8 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Features.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Features.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold text-gray-900 dark:text-white mb-4 group-hover:text-transparent group-hover:bg-clip-text group-hover:bg-gradient-to-r group-hover:from-blue-600 group-hover:to-purple-600 transition-all duration-300\",\n                                            children: feature.title\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Features.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-300 leading-relaxed\",\n                                            children: feature.description\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Features.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Features.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Brain_FileText_Globe_MousePointer_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"w-5 h-5 text-yellow-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Features.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Features.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Features.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Features.tsx\",\n                    lineNumber: 135,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mt-16\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-r from-blue-50 to-purple-50 dark:from-gray-800 dark:to-gray-700 rounded-2xl p-8 border border-gray-200 dark:border-gray-600\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-bold text-gray-900 dark:text-white mb-4\",\n                                children: \"Ready to Transform Your Teaching?\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Features.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 dark:text-gray-300 mb-6 max-w-2xl mx-auto\",\n                                children: \"Join thousands of educators who are already using AI to create better exams and improve student outcomes.\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Features.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-4 rtl:space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-3 rounded-xl font-semibold hover:from-blue-700 hover:to-purple-700 transition-all duration-200 transform hover:scale-105\",\n                                        children: \"Start Free Trial\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Features.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors font-medium\",\n                                        children: \"Schedule Demo\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Features.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Features.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Features.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Features.tsx\",\n                    lineNumber: 168,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Features.tsx\",\n            lineNumber: 96,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Features.tsx\",\n        lineNumber: 95,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Features.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Footer.tsx":
/*!***********************************!*\
  !*** ./src/components/Footer.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Footer: () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contexts/LanguageContext */ \"(ssr)/./src/contexts/LanguageContext.tsx\");\n/* harmony import */ var _barrel_optimize_names_Facebook_FileText_Heart_Instagram_Linkedin_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,FileText,Heart,Instagram,Linkedin,Mail,MapPin,Phone,Twitter,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/twitter.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_FileText_Heart_Instagram_Linkedin_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,FileText,Heart,Instagram,Linkedin,Mail,MapPin,Phone,Twitter,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/facebook.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_FileText_Heart_Instagram_Linkedin_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,FileText,Heart,Instagram,Linkedin,Mail,MapPin,Phone,Twitter,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/linkedin.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_FileText_Heart_Instagram_Linkedin_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,FileText,Heart,Instagram,Linkedin,Mail,MapPin,Phone,Twitter,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/instagram.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_FileText_Heart_Instagram_Linkedin_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,FileText,Heart,Instagram,Linkedin,Mail,MapPin,Phone,Twitter,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/youtube.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_FileText_Heart_Instagram_Linkedin_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,FileText,Heart,Instagram,Linkedin,Mail,MapPin,Phone,Twitter,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_FileText_Heart_Instagram_Linkedin_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,FileText,Heart,Instagram,Linkedin,Mail,MapPin,Phone,Twitter,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_FileText_Heart_Instagram_Linkedin_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,FileText,Heart,Instagram,Linkedin,Mail,MapPin,Phone,Twitter,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_FileText_Heart_Instagram_Linkedin_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,FileText,Heart,Instagram,Linkedin,Mail,MapPin,Phone,Twitter,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_FileText_Heart_Instagram_Linkedin_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,FileText,Heart,Instagram,Linkedin,Mail,MapPin,Phone,Twitter,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* __next_internal_client_entry_do_not_use__ Footer auto */ \n\n\nfunction Footer() {\n    const { t } = (0,_contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_1__.useLanguage)();\n    const footerLinks = {\n        product: [\n            {\n                name: 'Features',\n                href: '#features'\n            },\n            {\n                name: 'Pricing',\n                href: '#pricing'\n            },\n            {\n                name: 'API',\n                href: '/api'\n            },\n            {\n                name: 'Integrations',\n                href: '/integrations'\n            },\n            {\n                name: 'Changelog',\n                href: '/changelog'\n            }\n        ],\n        company: [\n            {\n                name: 'About Us',\n                href: '/about'\n            },\n            {\n                name: 'Careers',\n                href: '/careers'\n            },\n            {\n                name: 'Blog',\n                href: '/blog'\n            },\n            {\n                name: 'Press',\n                href: '/press'\n            },\n            {\n                name: 'Partners',\n                href: '/partners'\n            }\n        ],\n        support: [\n            {\n                name: 'Help Center',\n                href: '/help'\n            },\n            {\n                name: 'Documentation',\n                href: '/docs'\n            },\n            {\n                name: 'Community',\n                href: '/community'\n            },\n            {\n                name: 'Contact Us',\n                href: '/contact'\n            },\n            {\n                name: 'Status',\n                href: '/status'\n            }\n        ],\n        legal: [\n            {\n                name: 'Privacy Policy',\n                href: '/privacy'\n            },\n            {\n                name: 'Terms of Service',\n                href: '/terms'\n            },\n            {\n                name: 'Cookie Policy',\n                href: '/cookies'\n            },\n            {\n                name: 'GDPR',\n                href: '/gdpr'\n            },\n            {\n                name: 'Security',\n                href: '/security'\n            }\n        ]\n    };\n    const socialLinks = [\n        {\n            name: 'Twitter',\n            icon: _barrel_optimize_names_Facebook_FileText_Heart_Instagram_Linkedin_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            href: 'https://twitter.com/examai'\n        },\n        {\n            name: 'Facebook',\n            icon: _barrel_optimize_names_Facebook_FileText_Heart_Instagram_Linkedin_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            href: 'https://facebook.com/examai'\n        },\n        {\n            name: 'LinkedIn',\n            icon: _barrel_optimize_names_Facebook_FileText_Heart_Instagram_Linkedin_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            href: 'https://linkedin.com/company/examai'\n        },\n        {\n            name: 'Instagram',\n            icon: _barrel_optimize_names_Facebook_FileText_Heart_Instagram_Linkedin_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            href: 'https://instagram.com/examai'\n        },\n        {\n            name: 'YouTube',\n            icon: _barrel_optimize_names_Facebook_FileText_Heart_Instagram_Linkedin_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            href: 'https://youtube.com/examai'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-gray-900 dark:bg-black text-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 rtl:space-x-reverse mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_FileText_Heart_Instagram_Linkedin_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-5 h-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 68,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 67,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xl font-bold\",\n                                                children: \"ExamAI\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 70,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 mb-6 leading-relaxed\",\n                                        children: \"Transform your PDFs into interactive exams with the power of AI. Join thousands of educators worldwide who trust ExamAI for better learning outcomes.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3 rtl:space-x-reverse text-gray-400\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_FileText_Heart_Instagram_Linkedin_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 81,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"<EMAIL>\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 82,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 80,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3 rtl:space-x-reverse text-gray-400\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_FileText_Heart_Instagram_Linkedin_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 85,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"+****************\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 86,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 84,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3 rtl:space-x-reverse text-gray-400\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_FileText_Heart_Instagram_Linkedin_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 89,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"San Francisco, CA\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 90,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 88,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Footer.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold mb-6\",\n                                        children: t('footer.product')\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-3\",\n                                        children: footerLinks.product.map((link, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: link.href,\n                                                    className: \"text-gray-400 hover:text-white transition-colors\",\n                                                    children: link.name\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 101,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, index, false, {\n                                                fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Footer.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold mb-6\",\n                                        children: t('footer.company')\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-3\",\n                                        children: footerLinks.company.map((link, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: link.href,\n                                                    className: \"text-gray-400 hover:text-white transition-colors\",\n                                                    children: link.name\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 118,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, index, false, {\n                                                fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Footer.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold mb-6\",\n                                        children: t('footer.support')\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-3\",\n                                        children: footerLinks.support.map((link, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: link.href,\n                                                    className: \"text-gray-400 hover:text-white transition-colors\",\n                                                    children: link.name\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 135,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, index, false, {\n                                                fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 134,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Footer.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold mb-6\",\n                                        children: t('footer.legal')\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-3\",\n                                        children: footerLinks.legal.map((link, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: link.href,\n                                                    className: \"text-gray-400 hover:text-white transition-colors\",\n                                                    children: link.name\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 152,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, index, false, {\n                                                fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Footer.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Footer.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-t border-gray-800 pt-12 mt-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-md mx-auto text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold mb-4\",\n                                    children: \"Stay Updated\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 mb-6\",\n                                    children: \"Get the latest updates, tips, and educational resources delivered to your inbox.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-3 rtl:space-x-reverse\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            placeholder: \"Enter your email\",\n                                            className: \"flex-1 px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-white placeholder-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-lg font-semibold hover:from-blue-700 hover:to-purple-700 transition-all duration-200\",\n                                            children: \"Subscribe\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Footer.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Footer.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Footer.tsx\",\n                lineNumber: 62,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-gray-800\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 rtl:space-x-reverse text-gray-400\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"\\xa9 2024 ExamAI.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: t('footer.rights')\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Made with\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_FileText_Heart_Instagram_Linkedin_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"w-4 h-4 text-red-500\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"for educators\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Footer.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4 rtl:space-x-reverse\",\n                                children: socialLinks.map((social, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: social.href,\n                                        target: \"_blank\",\n                                        rel: \"noopener noreferrer\",\n                                        className: \"text-gray-400 hover:text-white transition-colors p-2 hover:bg-gray-800 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(social.icon, {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sr-only\",\n                                                children: social.name\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 209,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Footer.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Footer.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Footer.tsx\",\n                    lineNumber: 187,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Footer.tsx\",\n                lineNumber: 186,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Footer.tsx\",\n        lineNumber: 60,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Footer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Header.tsx":
/*!***********************************!*\
  !*** ./src/components/Header.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Header: () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/LanguageContext */ \"(ssr)/./src/contexts/LanguageContext.tsx\");\n/* harmony import */ var _barrel_optimize_names_FileText_Globe_Menu_Moon_Sun_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,Globe,Menu,Moon,Sun,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_FileText_Globe_Menu_Moon_Sun_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,Globe,Menu,Moon,Sun,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_FileText_Globe_Menu_Moon_Sun_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,Globe,Menu,Moon,Sun,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/moon.js\");\n/* harmony import */ var _barrel_optimize_names_FileText_Globe_Menu_Moon_Sun_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,Globe,Menu,Moon,Sun,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sun.js\");\n/* harmony import */ var _barrel_optimize_names_FileText_Globe_Menu_Moon_Sun_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,Globe,Menu,Moon,Sun,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_FileText_Globe_Menu_Moon_Sun_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=FileText,Globe,Menu,Moon,Sun,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* __next_internal_client_entry_do_not_use__ Header auto */ \n\n\n\nfunction Header() {\n    const { language, theme, setLanguage, setTheme, t } = (0,_contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_2__.useLanguage)();\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLangMenuOpen, setIsLangMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const toggleMenu = ()=>setIsMenuOpen(!isMenuOpen);\n    const toggleTheme = ()=>setTheme(theme === 'light' ? 'dark' : 'light');\n    const toggleLanguage = (lang)=>{\n        setLanguage(lang);\n        setIsLangMenuOpen(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-white dark:bg-gray-900 shadow-sm border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center h-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 rtl:space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_Globe_Menu_Moon_Sun_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"w-5 h-5 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 35,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 34,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xl font-bold text-gray-900 dark:text-white\",\n                                        children: \"ExamAI\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 37,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 32,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"hidden md:flex items-center space-x-8 rtl:space-x-reverse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"#features\",\n                                    className: \"text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors\",\n                                    children: t('nav.features')\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 45,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"#pricing\",\n                                    className: \"text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors\",\n                                    children: t('nav.pricing')\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 51,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"#about\",\n                                    className: \"text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors\",\n                                    children: t('nav.about')\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"#contact\",\n                                    className: \"text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors\",\n                                    children: t('nav.contact')\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4 rtl:space-x-reverse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setIsLangMenuOpen(!isLangMenuOpen),\n                                            className: \"flex items-center space-x-1 rtl:space-x-reverse text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_Globe_Menu_Moon_Sun_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 79,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: language === 'ar' ? 'العربية' : 'English'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 80,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 75,\n                                            columnNumber: 15\n                                        }, this),\n                                        isLangMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-full mt-2 right-0 rtl:right-auto rtl:left-0 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg py-2 min-w-[120px] z-50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>toggleLanguage('ar'),\n                                                    className: `w-full text-right rtl:text-left px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors ${language === 'ar' ? 'text-blue-600 dark:text-blue-400 font-medium' : 'text-gray-700 dark:text-gray-300'}`,\n                                                    children: \"العربية\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 87,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>toggleLanguage('en'),\n                                                    className: `w-full text-right rtl:text-left px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors ${language === 'en' ? 'text-blue-600 dark:text-blue-400 font-medium' : 'text-gray-700 dark:text-gray-300'}`,\n                                                    children: \"English\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 95,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 86,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: toggleTheme,\n                                    className: \"p-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors\",\n                                    children: theme === 'light' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_Globe_Menu_Moon_Sun_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_Globe_Menu_Moon_Sun_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden md:flex items-center space-x-3 rtl:space-x-reverse\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors\",\n                                            children: t('nav.login')\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2 rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200 transform hover:scale-105\",\n                                            children: t('nav.signup')\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: toggleMenu,\n                                    className: \"md:hidden p-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors\",\n                                    children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_Globe_Menu_Moon_Sun_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-6 h-6\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FileText_Globe_Menu_Moon_Sun_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-6 h-6\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Header.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, this),\n                isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"md:hidden border-t border-gray-200 dark:border-gray-700 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"#features\",\n                                className: \"text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors\",\n                                onClick: ()=>setIsMenuOpen(false),\n                                children: t('nav.features')\n                            }, void 0, false, {\n                                fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"#pricing\",\n                                className: \"text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors\",\n                                onClick: ()=>setIsMenuOpen(false),\n                                children: t('nav.pricing')\n                            }, void 0, false, {\n                                fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"#about\",\n                                className: \"text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors\",\n                                onClick: ()=>setIsMenuOpen(false),\n                                children: t('nav.about')\n                            }, void 0, false, {\n                                fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"#contact\",\n                                className: \"text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors\",\n                                onClick: ()=>setIsMenuOpen(false),\n                                children: t('nav.contact')\n                            }, void 0, false, {\n                                fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-t border-gray-200 dark:border-gray-700 pt-4 flex flex-col space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors text-right rtl:text-left\",\n                                        children: t('nav.login')\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2 rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200\",\n                                        children: t('nav.signup')\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Header.tsx\",\n                    lineNumber: 145,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Header.tsx\",\n            lineNumber: 29,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Header.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Hero.tsx":
/*!*********************************!*\
  !*** ./src/components/Hero.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Hero: () => (/* binding */ Hero)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contexts/LanguageContext */ \"(ssr)/./src/contexts/LanguageContext.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_BarChart3_CheckCircle_FileText_Play_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,BarChart3,CheckCircle,FileText,Play,Star,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_BarChart3_CheckCircle_FileText_Play_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,BarChart3,CheckCircle,FileText,Play,Star,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_BarChart3_CheckCircle_FileText_Play_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,BarChart3,CheckCircle,FileText,Play,Star,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_BarChart3_CheckCircle_FileText_Play_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,BarChart3,CheckCircle,FileText,Play,Star,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_BarChart3_CheckCircle_FileText_Play_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,BarChart3,CheckCircle,FileText,Play,Star,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_BarChart3_CheckCircle_FileText_Play_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,BarChart3,CheckCircle,FileText,Play,Star,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_BarChart3_CheckCircle_FileText_Play_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,BarChart3,CheckCircle,FileText,Play,Star,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_BarChart3_CheckCircle_FileText_Play_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,BarChart3,CheckCircle,FileText,Play,Star,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* __next_internal_client_entry_do_not_use__ Hero auto */ \n\n\nfunction Hero() {\n    const { language, t } = (0,_contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_1__.useLanguage)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"relative bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 py-20 lg:py-32 overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute -top-40 -right-32 w-80 h-80 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute -bottom-40 -left-32 w-80 h-80 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-40 left-40 w-80 h-80 bg-gradient-to-r from-yellow-400 to-red-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-4000\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid lg:grid-cols-2 gap-12 items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center lg:text-right rtl:lg:text-left\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"inline-flex items-center space-x-2 rtl:space-x-reverse bg-white dark:bg-gray-800 rounded-full px-4 py-2 shadow-sm border border-gray-200 dark:border-gray-700 mb-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-1 rtl:space-x-reverse\",\n                                            children: [\n                                                ...Array(5)\n                                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_BarChart3_CheckCircle_FileText_Play_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    className: \"w-4 h-4 text-yellow-400 fill-current\"\n                                                }, i, false, {\n                                                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                                                    lineNumber: 36,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                                            lineNumber: 34,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-600 dark:text-gray-300\",\n                                            children: t('hero.trusted')\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                                            lineNumber: 39,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                                    lineNumber: 33,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 dark:text-white mb-6 leading-tight\",\n                                    children: [\n                                        t('hero.title'),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"block text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600\",\n                                            children: t('hero.subtitle')\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                                            lineNumber: 47,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                                    lineNumber: 45,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-600 dark:text-gray-300 mb-8 leading-relaxed max-w-2xl mx-auto lg:mx-0\",\n                                    children: t('hero.description')\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row items-center justify-center lg:justify-start rtl:lg:justify-end space-y-4 sm:space-y-0 sm:space-x-4 rtl:space-x-reverse mb-12\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"w-full sm:w-auto bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-xl font-semibold text-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl\",\n                                            children: [\n                                                t('hero.cta.primary'),\n                                                language === 'ar' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_BarChart3_CheckCircle_FileText_Play_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    className: \"w-5 h-5 mr-2 inline\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                                                    lineNumber: 62,\n                                                    columnNumber: 19\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_BarChart3_CheckCircle_FileText_Play_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"w-5 h-5 ml-2 inline\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                                                    lineNumber: 64,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"w-full sm:w-auto flex items-center justify-center space-x-2 rtl:space-x-reverse text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_BarChart3_CheckCircle_FileText_Play_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                                                    lineNumber: 69,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium\",\n                                                    children: t('hero.cta.secondary')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                                                    lineNumber: 70,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                                            lineNumber: 68,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 sm:grid-cols-3 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 rtl:space-x-reverse\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_BarChart3_CheckCircle_FileText_Play_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"w-5 h-5 text-blue-600 dark:text-blue-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                                                        lineNumber: 78,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                                                    lineNumber: 77,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-right rtl:text-left\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-semibold text-gray-900 dark:text-white\",\n                                                            children: \"AI-Powered\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                                                            lineNumber: 81,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                            children: \"Smart Generation\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                                                            lineNumber: 82,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                                                    lineNumber: 80,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 rtl:space-x-reverse\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-10 h-10 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_BarChart3_CheckCircle_FileText_Play_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"w-5 h-5 text-purple-600 dark:text-purple-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                                                        lineNumber: 88,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                                                    lineNumber: 87,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-right rtl:text-left\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-semibold text-gray-900 dark:text-white\",\n                                                            children: \"Analytics\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                                                            lineNumber: 91,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                            children: \"Deep Insights\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                                                            lineNumber: 92,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                                                    lineNumber: 90,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                                            lineNumber: 86,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 rtl:space-x-reverse\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-10 h-10 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_BarChart3_CheckCircle_FileText_Play_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"w-5 h-5 text-green-600 dark:text-green-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                                                        lineNumber: 98,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                                                    lineNumber: 97,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-right rtl:text-left\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-semibold text-gray-900 dark:text-white\",\n                                                            children: \"Instant\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                                                            lineNumber: 101,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                            children: \"Auto Grading\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                                                            lineNumber: 102,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                                                    lineNumber: 100,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative bg-white dark:bg-gray-800 rounded-2xl shadow-2xl border border-gray-200 dark:border-gray-700 overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-100 dark:bg-gray-700 px-4 py-3 border-b border-gray-200 dark:border-gray-600\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2 rtl:space-x-reverse\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 bg-red-400 rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                                                        lineNumber: 115,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 bg-yellow-400 rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                                                        lineNumber: 116,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 bg-green-400 rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                                                        lineNumber: 117,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 bg-white dark:bg-gray-600 rounded-md px-3 py-1 mx-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                            children: \"examai.com/demo\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                                                            lineNumber: 119,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                                                        lineNumber: 118,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"border-2 border-dashed border-blue-300 dark:border-blue-600 rounded-lg p-6 text-center bg-blue-50 dark:bg-blue-900/20\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_BarChart3_CheckCircle_FileText_Play_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"w-12 h-12 text-blue-600 dark:text-blue-400 mx-auto mb-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                                                                lineNumber: 129,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-blue-600 dark:text-blue-400 font-medium\",\n                                                                children: \"Drop your PDF here\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                                                                lineNumber: 130,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                                                        lineNumber: 128,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between text-sm\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-600 dark:text-gray-400\",\n                                                                        children: \"Processing...\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                                                                        lineNumber: 138,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-blue-600 dark:text-blue-400\",\n                                                                        children: \"85%\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                                                                        lineNumber: 139,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                                                                lineNumber: 137,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-gradient-to-r from-blue-600 to-purple-600 h-2 rounded-full w-[85%] transition-all duration-300\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                                                                    lineNumber: 142,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                                                                lineNumber: 141,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                                                        lineNumber: 136,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-semibold text-gray-900 dark:text-white\",\n                                                                children: \"Generated Questions:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                                                                lineNumber: 148,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-gray-50 dark:bg-gray-700 rounded-lg p-3\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-700 dark:text-gray-300\",\n                                                                            children: \"1. What is the main concept discussed in chapter 2?\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                                                                            lineNumber: 151,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                                                                        lineNumber: 150,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-gray-50 dark:bg-gray-700 rounded-lg p-3\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-700 dark:text-gray-300\",\n                                                                            children: \"2. True or False: The theory applies to all cases.\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                                                                            lineNumber: 154,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                                                                        lineNumber: 153,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-gray-50 dark:bg-gray-700 rounded-lg p-3\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-700 dark:text-gray-300\",\n                                                                            children: \"3. Explain the relationship between...\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                                                                            lineNumber: 157,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                                                                        lineNumber: 156,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                                                                lineNumber: 149,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                                                        lineNumber: 147,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute -top-4 -right-4 bg-green-500 text-white rounded-full p-3 shadow-lg animate-bounce\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_BarChart3_CheckCircle_FileText_Play_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-6 h-6\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute -bottom-4 -left-4 bg-purple-500 text-white rounded-full p-3 shadow-lg animate-pulse\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_BarChart3_CheckCircle_FileText_Play_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-6 h-6\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                    lineNumber: 29,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Hero.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Hero.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Pricing.tsx":
/*!************************************!*\
  !*** ./src/components/Pricing.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Pricing: () => (/* binding */ Pricing)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/LanguageContext */ \"(ssr)/./src/contexts/LanguageContext.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Building_Check_Crown_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Building,Check,Crown,Star,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Building_Check_Crown_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Building,Check,Crown,Star,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Building_Check_Crown_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Building,Check,Crown,Star,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Building_Check_Crown_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Building,Check,Crown,Star,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Building_Check_Crown_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Building,Check,Crown,Star,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Building_Check_Crown_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Building,Check,Crown,Star,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Building_Check_Crown_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Building,Check,Crown,Star,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* __next_internal_client_entry_do_not_use__ Pricing auto */ \n\n\n\nfunction Pricing() {\n    const { language, t } = (0,_contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_2__.useLanguage)();\n    const [isYearly, setIsYearly] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const plans = [\n        {\n            name: t('plan.free.name'),\n            price: t('plan.free.price'),\n            period: t('plan.free.period'),\n            yearlyPrice: '0',\n            description: t('plan.free.description'),\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Building_Check_Crown_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            color: 'gray',\n            gradient: 'from-gray-500 to-gray-600',\n            popular: false,\n            features: [\n                '5 PDFs per month',\n                '50 questions generated',\n                'Basic analytics',\n                'Email support',\n                'Standard templates'\n            ],\n            cta: t('pricing.cta'),\n            ctaStyle: 'border border-gray-300 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700'\n        },\n        {\n            name: t('plan.pro.name'),\n            price: t('plan.pro.price'),\n            period: t('plan.pro.period'),\n            yearlyPrice: '15',\n            description: t('plan.pro.description'),\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Building_Check_Crown_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            color: 'blue',\n            gradient: 'from-blue-600 to-purple-600',\n            popular: true,\n            features: [\n                'Unlimited PDFs',\n                'Unlimited questions',\n                'Advanced analytics',\n                'Priority support',\n                'Custom templates',\n                'Student management',\n                'Export reports',\n                'API access'\n            ],\n            cta: t('pricing.cta'),\n            ctaStyle: 'bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700'\n        },\n        {\n            name: t('plan.enterprise.name'),\n            price: t('plan.enterprise.price'),\n            period: t('plan.enterprise.period'),\n            yearlyPrice: '79',\n            description: t('plan.enterprise.description'),\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Building_Check_Crown_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            color: 'purple',\n            gradient: 'from-purple-600 to-pink-600',\n            popular: false,\n            features: [\n                'Everything in Pro',\n                'Multi-user accounts',\n                'White-label solution',\n                'LMS integration',\n                'Dedicated support',\n                'Custom development',\n                'SLA guarantee',\n                'Training sessions'\n            ],\n            cta: t('pricing.contact'),\n            ctaStyle: 'bg-gradient-to-r from-purple-600 to-pink-600 text-white hover:from-purple-700 hover:to-pink-700'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"pricing\",\n        className: \"py-20 bg-gray-50 dark:bg-gray-800\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-6\",\n                            children: t('pricing.title')\n                        }, void 0, false, {\n                            fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Pricing.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-8\",\n                            children: t('pricing.subtitle')\n                        }, void 0, false, {\n                            fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Pricing.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"inline-flex items-center bg-white dark:bg-gray-700 rounded-xl p-1 border border-gray-200 dark:border-gray-600\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setIsYearly(false),\n                                    className: `px-6 py-2 rounded-lg font-medium transition-all duration-200 ${!isYearly ? 'bg-blue-600 text-white shadow-sm' : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'}`,\n                                    children: t('pricing.monthly')\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Pricing.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setIsYearly(true),\n                                    className: `px-6 py-2 rounded-lg font-medium transition-all duration-200 relative ${isYearly ? 'bg-blue-600 text-white shadow-sm' : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'}`,\n                                    children: [\n                                        t('pricing.yearly'),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"absolute -top-2 -right-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full\",\n                                            children: t('pricing.save')\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Pricing.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Pricing.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Pricing.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Pricing.tsx\",\n                    lineNumber: 92,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid md:grid-cols-3 gap-8 max-w-6xl mx-auto\",\n                    children: plans.map((plan, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `relative bg-white dark:bg-gray-900 rounded-2xl shadow-lg border-2 transition-all duration-300 hover:shadow-xl hover:-translate-y-2 ${plan.popular ? 'border-blue-500 dark:border-blue-400 scale-105' : 'border-gray-200 dark:border-gray-700'}`,\n                            children: [\n                                plan.popular && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute -top-4 left-1/2 transform -translate-x-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-2 rounded-full text-sm font-semibold flex items-center space-x-2 rtl:space-x-reverse\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Check_Crown_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Pricing.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Most Popular\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Pricing.tsx\",\n                                                lineNumber: 144,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Pricing.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Pricing.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center mb-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: `w-16 h-16 mx-auto mb-4 bg-gradient-to-br ${plan.gradient} rounded-2xl flex items-center justify-center`,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(plan.icon, {\n                                                        className: \"w-8 h-8 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Pricing.tsx\",\n                                                        lineNumber: 153,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Pricing.tsx\",\n                                                    lineNumber: 152,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-2xl font-bold text-gray-900 dark:text-white mb-2\",\n                                                    children: plan.name\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Pricing.tsx\",\n                                                    lineNumber: 156,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 dark:text-gray-400 mb-6\",\n                                                    children: plan.description\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Pricing.tsx\",\n                                                    lineNumber: 160,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-baseline justify-center space-x-2 rtl:space-x-reverse\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-5xl font-bold text-gray-900 dark:text-white\",\n                                                                    children: [\n                                                                        \"$\",\n                                                                        isYearly ? plan.yearlyPrice : plan.price\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Pricing.tsx\",\n                                                                    lineNumber: 167,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-600 dark:text-gray-400\",\n                                                                    children: plan.period\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Pricing.tsx\",\n                                                                    lineNumber: 170,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Pricing.tsx\",\n                                                            lineNumber: 166,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        isYearly && plan.price !== '0' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-green-600 dark:text-green-400 mt-2\",\n                                                            children: [\n                                                                \"Save $\",\n                                                                (parseInt(plan.price) * 12 - parseInt(plan.yearlyPrice) * 12).toFixed(0),\n                                                                \" per year\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Pricing.tsx\",\n                                                            lineNumber: 175,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Pricing.tsx\",\n                                                    lineNumber: 165,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Pricing.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4 mb-8\",\n                                            children: plan.features.map((feature, featureIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3 rtl:space-x-reverse\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Check_Crown_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"w-5 h-5 text-green-500 flex-shrink-0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Pricing.tsx\",\n                                                            lineNumber: 186,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-600 dark:text-gray-300\",\n                                                            children: feature\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Pricing.tsx\",\n                                                            lineNumber: 187,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, featureIndex, true, {\n                                                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Pricing.tsx\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Pricing.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: `w-full py-4 px-6 rounded-xl font-semibold transition-all duration-200 transform hover:scale-105 flex items-center justify-center space-x-2 rtl:space-x-reverse ${plan.ctaStyle}`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: plan.cta\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Pricing.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 19\n                                                }, this),\n                                                language === 'ar' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Check_Crown_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Pricing.tsx\",\n                                                    lineNumber: 196,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Check_Crown_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Pricing.tsx\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Pricing.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Pricing.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Pricing.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Pricing.tsx\",\n                    lineNumber: 129,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mt-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 dark:text-gray-400 mb-4\",\n                            children: \"All plans include 14-day free trial • No credit card required • Cancel anytime\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Pricing.tsx\",\n                            lineNumber: 208,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center space-x-6 rtl:space-x-reverse text-sm text-gray-500 dark:text-gray-400\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 rtl:space-x-reverse\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Check_Crown_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"w-4 h-4 text-green-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Pricing.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"SSL Encrypted\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Pricing.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Pricing.tsx\",\n                                    lineNumber: 212,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 rtl:space-x-reverse\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Check_Crown_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"w-4 h-4 text-green-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Pricing.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"GDPR Compliant\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Pricing.tsx\",\n                                            lineNumber: 218,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Pricing.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 rtl:space-x-reverse\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Check_Crown_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"w-4 h-4 text-green-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Pricing.tsx\",\n                                            lineNumber: 221,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"24/7 Support\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Pricing.tsx\",\n                                            lineNumber: 222,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Pricing.tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Pricing.tsx\",\n                            lineNumber: 211,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Pricing.tsx\",\n                    lineNumber: 207,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Pricing.tsx\",\n            lineNumber: 90,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\components\\\\Pricing.tsx\",\n        lineNumber: 89,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9QcmljaW5nLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7OztBQUVpQztBQUN3QjtBQVNuQztBQUVmLFNBQVNTO0lBQ2QsTUFBTSxFQUFFQyxRQUFRLEVBQUVDLENBQUMsRUFBRSxHQUFHVixzRUFBV0E7SUFDbkMsTUFBTSxDQUFDVyxVQUFVQyxZQUFZLEdBQUdiLCtDQUFRQSxDQUFDO0lBRXpDLE1BQU1jLFFBQVE7UUFDWjtZQUNFQyxNQUFNSixFQUFFO1lBQ1JLLE9BQU9MLEVBQUU7WUFDVE0sUUFBUU4sRUFBRTtZQUNWTyxhQUFhO1lBQ2JDLGFBQWFSLEVBQUU7WUFDZlMsTUFBTWpCLDhIQUFJQTtZQUNWa0IsT0FBTztZQUNQQyxVQUFVO1lBQ1ZDLFNBQVM7WUFDVEMsVUFBVTtnQkFDUjtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTthQUNEO1lBQ0RDLEtBQUtkLEVBQUU7WUFDUGUsVUFBVTtRQUNaO1FBQ0E7WUFDRVgsTUFBTUosRUFBRTtZQUNSSyxPQUFPTCxFQUFFO1lBQ1RNLFFBQVFOLEVBQUU7WUFDVk8sYUFBYTtZQUNiQyxhQUFhUixFQUFFO1lBQ2ZTLE1BQU1oQiw4SEFBR0E7WUFDVGlCLE9BQU87WUFDUEMsVUFBVTtZQUNWQyxTQUFTO1lBQ1RDLFVBQVU7Z0JBQ1I7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7YUFDRDtZQUNEQyxLQUFLZCxFQUFFO1lBQ1BlLFVBQVU7UUFDWjtRQUNBO1lBQ0VYLE1BQU1KLEVBQUU7WUFDUkssT0FBT0wsRUFBRTtZQUNUTSxRQUFRTixFQUFFO1lBQ1ZPLGFBQWE7WUFDYkMsYUFBYVIsRUFBRTtZQUNmUyxNQUFNZCw4SEFBUUE7WUFDZGUsT0FBTztZQUNQQyxVQUFVO1lBQ1ZDLFNBQVM7WUFDVEMsVUFBVTtnQkFDUjtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTthQUNEO1lBQ0RDLEtBQUtkLEVBQUU7WUFDUGUsVUFBVTtRQUNaO0tBQ0Q7SUFFRCxxQkFDRSw4REFBQ0M7UUFBUUMsSUFBRztRQUFVQyxXQUFVO2tCQUM5Qiw0RUFBQ0M7WUFBSUQsV0FBVTs7OEJBRWIsOERBQUNDO29CQUFJRCxXQUFVOztzQ0FDYiw4REFBQ0U7NEJBQUdGLFdBQVU7c0NBQ1hsQixFQUFFOzs7Ozs7c0NBRUwsOERBQUNxQjs0QkFBRUgsV0FBVTtzQ0FDVmxCLEVBQUU7Ozs7OztzQ0FJTCw4REFBQ21COzRCQUFJRCxXQUFVOzs4Q0FDYiw4REFBQ0k7b0NBQ0NDLFNBQVMsSUFBTXJCLFlBQVk7b0NBQzNCZ0IsV0FBVyxDQUFDLDZEQUE2RCxFQUN2RSxDQUFDakIsV0FDRyxxQ0FDQSw4RUFDSjs4Q0FFREQsRUFBRTs7Ozs7OzhDQUVMLDhEQUFDc0I7b0NBQ0NDLFNBQVMsSUFBTXJCLFlBQVk7b0NBQzNCZ0IsV0FBVyxDQUFDLHNFQUFzRSxFQUNoRmpCLFdBQ0kscUNBQ0EsOEVBQ0o7O3dDQUVERCxFQUFFO3NEQUNILDhEQUFDd0I7NENBQUtOLFdBQVU7c0RBQ2JsQixFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBT1gsOERBQUNtQjtvQkFBSUQsV0FBVTs4QkFDWmYsTUFBTXNCLEdBQUcsQ0FBQyxDQUFDQyxNQUFNQyxzQkFDaEIsOERBQUNSOzRCQUVDRCxXQUFXLENBQUMsbUlBQW1JLEVBQzdJUSxLQUFLZCxPQUFPLEdBQ1IsbURBQ0Esd0NBQ0o7O2dDQUdEYyxLQUFLZCxPQUFPLGtCQUNYLDhEQUFDTztvQ0FBSUQsV0FBVTs4Q0FDYiw0RUFBQ0M7d0NBQUlELFdBQVU7OzBEQUNiLDhEQUFDeEIsOEhBQUtBO2dEQUFDd0IsV0FBVTs7Ozs7OzBEQUNqQiw4REFBQ007MERBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQUtaLDhEQUFDTDtvQ0FBSUQsV0FBVTs7c0RBRWIsOERBQUNDOzRDQUFJRCxXQUFVOzs4REFDYiw4REFBQ0M7b0RBQUlELFdBQVcsQ0FBQyx5Q0FBeUMsRUFBRVEsS0FBS2YsUUFBUSxDQUFDLDZDQUE2QyxDQUFDOzhEQUN0SCw0RUFBQ2UsS0FBS2pCLElBQUk7d0RBQUNTLFdBQVU7Ozs7Ozs7Ozs7OzhEQUd2Qiw4REFBQ1U7b0RBQUdWLFdBQVU7OERBQ1hRLEtBQUt0QixJQUFJOzs7Ozs7OERBR1osOERBQUNpQjtvREFBRUgsV0FBVTs4REFDVlEsS0FBS2xCLFdBQVc7Ozs7Ozs4REFJbkIsOERBQUNXO29EQUFJRCxXQUFVOztzRUFDYiw4REFBQ0M7NERBQUlELFdBQVU7OzhFQUNiLDhEQUFDTTtvRUFBS04sV0FBVTs7d0VBQW1EO3dFQUMvRGpCLFdBQVd5QixLQUFLbkIsV0FBVyxHQUFHbUIsS0FBS3JCLEtBQUs7Ozs7Ozs7OEVBRTVDLDhEQUFDbUI7b0VBQUtOLFdBQVU7OEVBQ2JRLEtBQUtwQixNQUFNOzs7Ozs7Ozs7Ozs7d0RBR2ZMLFlBQVl5QixLQUFLckIsS0FBSyxLQUFLLHFCQUMxQiw4REFBQ2M7NERBQUlELFdBQVU7O2dFQUFrRDtnRUFDdkRXLENBQUFBLFNBQVNILEtBQUtyQixLQUFLLElBQUksS0FBS3dCLFNBQVNILEtBQUtuQixXQUFXLElBQUksRUFBQyxFQUFHdUIsT0FBTyxDQUFDO2dFQUFHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NEQU94Riw4REFBQ1g7NENBQUlELFdBQVU7c0RBQ1pRLEtBQUtiLFFBQVEsQ0FBQ1ksR0FBRyxDQUFDLENBQUNNLFNBQVNDLDZCQUMzQiw4REFBQ2I7b0RBQXVCRCxXQUFVOztzRUFDaEMsOERBQUMzQiw4SEFBS0E7NERBQUMyQixXQUFVOzs7Ozs7c0VBQ2pCLDhEQUFDTTs0REFBS04sV0FBVTtzRUFBb0NhOzs7Ozs7O21EQUY1Q0M7Ozs7Ozs7Ozs7c0RBUWQsOERBQUNWOzRDQUFPSixXQUFXLENBQUMsK0pBQStKLEVBQUVRLEtBQUtYLFFBQVEsRUFBRTs7OERBQ2xNLDhEQUFDUzs4REFBTUUsS0FBS1osR0FBRzs7Ozs7O2dEQUNkZixhQUFhLHFCQUNaLDhEQUFDSCw4SEFBU0E7b0RBQUNzQixXQUFVOzs7Ozt5RUFFckIsOERBQUNyQiw4SEFBVUE7b0RBQUNxQixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzJCQWxFdkJTOzs7Ozs7Ozs7OzhCQTJFWCw4REFBQ1I7b0JBQUlELFdBQVU7O3NDQUNiLDhEQUFDRzs0QkFBRUgsV0FBVTtzQ0FBd0M7Ozs7OztzQ0FHckQsOERBQUNDOzRCQUFJRCxXQUFVOzs4Q0FDYiw4REFBQ0M7b0NBQUlELFdBQVU7O3NEQUNiLDhEQUFDM0IsOEhBQUtBOzRDQUFDMkIsV0FBVTs7Ozs7O3NEQUNqQiw4REFBQ007c0RBQUs7Ozs7Ozs7Ozs7Ozs4Q0FFUiw4REFBQ0w7b0NBQUlELFdBQVU7O3NEQUNiLDhEQUFDM0IsOEhBQUtBOzRDQUFDMkIsV0FBVTs7Ozs7O3NEQUNqQiw4REFBQ007c0RBQUs7Ozs7Ozs7Ozs7Ozs4Q0FFUiw4REFBQ0w7b0NBQUlELFdBQVU7O3NEQUNiLDhEQUFDM0IsOEhBQUtBOzRDQUFDMkIsV0FBVTs7Ozs7O3NEQUNqQiw4REFBQ007c0RBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBT3BCIiwic291cmNlcyI6WyJEOlxcdGVtcGxldGVcXGxhbmRpbmctcGFnZVxcc3JjXFxjb21wb25lbnRzXFxQcmljaW5nLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgdXNlTGFuZ3VhZ2UgfSBmcm9tICdAL2NvbnRleHRzL0xhbmd1YWdlQ29udGV4dCc7XG5pbXBvcnQgeyBcbiAgQ2hlY2ssIFxuICBTdGFyLCBcbiAgWmFwLCBcbiAgQ3Jvd24sIFxuICBCdWlsZGluZyxcbiAgQXJyb3dMZWZ0LFxuICBBcnJvd1JpZ2h0XG59IGZyb20gJ2x1Y2lkZS1yZWFjdCc7XG5cbmV4cG9ydCBmdW5jdGlvbiBQcmljaW5nKCkge1xuICBjb25zdCB7IGxhbmd1YWdlLCB0IH0gPSB1c2VMYW5ndWFnZSgpO1xuICBjb25zdCBbaXNZZWFybHksIHNldElzWWVhcmx5XSA9IHVzZVN0YXRlKGZhbHNlKTtcblxuICBjb25zdCBwbGFucyA9IFtcbiAgICB7XG4gICAgICBuYW1lOiB0KCdwbGFuLmZyZWUubmFtZScpLFxuICAgICAgcHJpY2U6IHQoJ3BsYW4uZnJlZS5wcmljZScpLFxuICAgICAgcGVyaW9kOiB0KCdwbGFuLmZyZWUucGVyaW9kJyksXG4gICAgICB5ZWFybHlQcmljZTogJzAnLFxuICAgICAgZGVzY3JpcHRpb246IHQoJ3BsYW4uZnJlZS5kZXNjcmlwdGlvbicpLFxuICAgICAgaWNvbjogU3RhcixcbiAgICAgIGNvbG9yOiAnZ3JheScsXG4gICAgICBncmFkaWVudDogJ2Zyb20tZ3JheS01MDAgdG8tZ3JheS02MDAnLFxuICAgICAgcG9wdWxhcjogZmFsc2UsXG4gICAgICBmZWF0dXJlczogW1xuICAgICAgICAnNSBQREZzIHBlciBtb250aCcsXG4gICAgICAgICc1MCBxdWVzdGlvbnMgZ2VuZXJhdGVkJyxcbiAgICAgICAgJ0Jhc2ljIGFuYWx5dGljcycsXG4gICAgICAgICdFbWFpbCBzdXBwb3J0JyxcbiAgICAgICAgJ1N0YW5kYXJkIHRlbXBsYXRlcydcbiAgICAgIF0sXG4gICAgICBjdGE6IHQoJ3ByaWNpbmcuY3RhJyksXG4gICAgICBjdGFTdHlsZTogJ2JvcmRlciBib3JkZXItZ3JheS0zMDAgdGV4dC1ncmF5LTcwMCBkYXJrOnRleHQtZ3JheS0zMDAgaG92ZXI6YmctZ3JheS01MCBkYXJrOmhvdmVyOmJnLWdyYXktNzAwJ1xuICAgIH0sXG4gICAge1xuICAgICAgbmFtZTogdCgncGxhbi5wcm8ubmFtZScpLFxuICAgICAgcHJpY2U6IHQoJ3BsYW4ucHJvLnByaWNlJyksXG4gICAgICBwZXJpb2Q6IHQoJ3BsYW4ucHJvLnBlcmlvZCcpLFxuICAgICAgeWVhcmx5UHJpY2U6ICcxNScsXG4gICAgICBkZXNjcmlwdGlvbjogdCgncGxhbi5wcm8uZGVzY3JpcHRpb24nKSxcbiAgICAgIGljb246IFphcCxcbiAgICAgIGNvbG9yOiAnYmx1ZScsXG4gICAgICBncmFkaWVudDogJ2Zyb20tYmx1ZS02MDAgdG8tcHVycGxlLTYwMCcsXG4gICAgICBwb3B1bGFyOiB0cnVlLFxuICAgICAgZmVhdHVyZXM6IFtcbiAgICAgICAgJ1VubGltaXRlZCBQREZzJyxcbiAgICAgICAgJ1VubGltaXRlZCBxdWVzdGlvbnMnLFxuICAgICAgICAnQWR2YW5jZWQgYW5hbHl0aWNzJyxcbiAgICAgICAgJ1ByaW9yaXR5IHN1cHBvcnQnLFxuICAgICAgICAnQ3VzdG9tIHRlbXBsYXRlcycsXG4gICAgICAgICdTdHVkZW50IG1hbmFnZW1lbnQnLFxuICAgICAgICAnRXhwb3J0IHJlcG9ydHMnLFxuICAgICAgICAnQVBJIGFjY2VzcydcbiAgICAgIF0sXG4gICAgICBjdGE6IHQoJ3ByaWNpbmcuY3RhJyksXG4gICAgICBjdGFTdHlsZTogJ2JnLWdyYWRpZW50LXRvLXIgZnJvbS1ibHVlLTYwMCB0by1wdXJwbGUtNjAwIHRleHQtd2hpdGUgaG92ZXI6ZnJvbS1ibHVlLTcwMCBob3Zlcjp0by1wdXJwbGUtNzAwJ1xuICAgIH0sXG4gICAge1xuICAgICAgbmFtZTogdCgncGxhbi5lbnRlcnByaXNlLm5hbWUnKSxcbiAgICAgIHByaWNlOiB0KCdwbGFuLmVudGVycHJpc2UucHJpY2UnKSxcbiAgICAgIHBlcmlvZDogdCgncGxhbi5lbnRlcnByaXNlLnBlcmlvZCcpLFxuICAgICAgeWVhcmx5UHJpY2U6ICc3OScsXG4gICAgICBkZXNjcmlwdGlvbjogdCgncGxhbi5lbnRlcnByaXNlLmRlc2NyaXB0aW9uJyksXG4gICAgICBpY29uOiBCdWlsZGluZyxcbiAgICAgIGNvbG9yOiAncHVycGxlJyxcbiAgICAgIGdyYWRpZW50OiAnZnJvbS1wdXJwbGUtNjAwIHRvLXBpbmstNjAwJyxcbiAgICAgIHBvcHVsYXI6IGZhbHNlLFxuICAgICAgZmVhdHVyZXM6IFtcbiAgICAgICAgJ0V2ZXJ5dGhpbmcgaW4gUHJvJyxcbiAgICAgICAgJ011bHRpLXVzZXIgYWNjb3VudHMnLFxuICAgICAgICAnV2hpdGUtbGFiZWwgc29sdXRpb24nLFxuICAgICAgICAnTE1TIGludGVncmF0aW9uJyxcbiAgICAgICAgJ0RlZGljYXRlZCBzdXBwb3J0JyxcbiAgICAgICAgJ0N1c3RvbSBkZXZlbG9wbWVudCcsXG4gICAgICAgICdTTEEgZ3VhcmFudGVlJyxcbiAgICAgICAgJ1RyYWluaW5nIHNlc3Npb25zJ1xuICAgICAgXSxcbiAgICAgIGN0YTogdCgncHJpY2luZy5jb250YWN0JyksXG4gICAgICBjdGFTdHlsZTogJ2JnLWdyYWRpZW50LXRvLXIgZnJvbS1wdXJwbGUtNjAwIHRvLXBpbmstNjAwIHRleHQtd2hpdGUgaG92ZXI6ZnJvbS1wdXJwbGUtNzAwIGhvdmVyOnRvLXBpbmstNzAwJ1xuICAgIH1cbiAgXTtcblxuICByZXR1cm4gKFxuICAgIDxzZWN0aW9uIGlkPVwicHJpY2luZ1wiIGNsYXNzTmFtZT1cInB5LTIwIGJnLWdyYXktNTAgZGFyazpiZy1ncmF5LTgwMFwiPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy03eGwgbXgtYXV0byBweC00IHNtOnB4LTYgbGc6cHgtOFwiPlxuICAgICAgICB7LyogSGVhZGVyICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIG1iLTE2XCI+XG4gICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtM3hsIG1kOnRleHQtNHhsIGxnOnRleHQtNXhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwIGRhcms6dGV4dC13aGl0ZSBtYi02XCI+XG4gICAgICAgICAgICB7dCgncHJpY2luZy50aXRsZScpfVxuICAgICAgICAgIDwvaDI+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14bCB0ZXh0LWdyYXktNjAwIGRhcms6dGV4dC1ncmF5LTMwMCBtYXgtdy0zeGwgbXgtYXV0byBtYi04XCI+XG4gICAgICAgICAgICB7dCgncHJpY2luZy5zdWJ0aXRsZScpfVxuICAgICAgICAgIDwvcD5cblxuICAgICAgICAgIHsvKiBCaWxsaW5nIFRvZ2dsZSAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBiZy13aGl0ZSBkYXJrOmJnLWdyYXktNzAwIHJvdW5kZWQteGwgcC0xIGJvcmRlciBib3JkZXItZ3JheS0yMDAgZGFyazpib3JkZXItZ3JheS02MDBcIj5cbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0SXNZZWFybHkoZmFsc2UpfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9e2BweC02IHB5LTIgcm91bmRlZC1sZyBmb250LW1lZGl1bSB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgJHtcbiAgICAgICAgICAgICAgICAhaXNZZWFybHkgXG4gICAgICAgICAgICAgICAgICA/ICdiZy1ibHVlLTYwMCB0ZXh0LXdoaXRlIHNoYWRvdy1zbScgXG4gICAgICAgICAgICAgICAgICA6ICd0ZXh0LWdyYXktNjAwIGRhcms6dGV4dC1ncmF5LTMwMCBob3Zlcjp0ZXh0LWdyYXktOTAwIGRhcms6aG92ZXI6dGV4dC13aGl0ZSdcbiAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIHt0KCdwcmljaW5nLm1vbnRobHknKX1cbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRJc1llYXJseSh0cnVlKX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcHgtNiBweS0yIHJvdW5kZWQtbGcgZm9udC1tZWRpdW0gdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwIHJlbGF0aXZlICR7XG4gICAgICAgICAgICAgICAgaXNZZWFybHkgXG4gICAgICAgICAgICAgICAgICA/ICdiZy1ibHVlLTYwMCB0ZXh0LXdoaXRlIHNoYWRvdy1zbScgXG4gICAgICAgICAgICAgICAgICA6ICd0ZXh0LWdyYXktNjAwIGRhcms6dGV4dC1ncmF5LTMwMCBob3Zlcjp0ZXh0LWdyYXktOTAwIGRhcms6aG92ZXI6dGV4dC13aGl0ZSdcbiAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIHt0KCdwcmljaW5nLnllYXJseScpfVxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJhYnNvbHV0ZSAtdG9wLTIgLXJpZ2h0LTIgYmctZ3JlZW4tNTAwIHRleHQtd2hpdGUgdGV4dC14cyBweC0yIHB5LTEgcm91bmRlZC1mdWxsXCI+XG4gICAgICAgICAgICAgICAge3QoJ3ByaWNpbmcuc2F2ZScpfVxuICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIFByaWNpbmcgQ2FyZHMgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBtZDpncmlkLWNvbHMtMyBnYXAtOCBtYXgtdy02eGwgbXgtYXV0b1wiPlxuICAgICAgICAgIHtwbGFucy5tYXAoKHBsYW4sIGluZGV4KSA9PiAoXG4gICAgICAgICAgICA8ZGl2IFxuICAgICAgICAgICAgICBrZXk9e2luZGV4fVxuICAgICAgICAgICAgICBjbGFzc05hbWU9e2ByZWxhdGl2ZSBiZy13aGl0ZSBkYXJrOmJnLWdyYXktOTAwIHJvdW5kZWQtMnhsIHNoYWRvdy1sZyBib3JkZXItMiB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgaG92ZXI6c2hhZG93LXhsIGhvdmVyOi10cmFuc2xhdGUteS0yICR7XG4gICAgICAgICAgICAgICAgcGxhbi5wb3B1bGFyIFxuICAgICAgICAgICAgICAgICAgPyAnYm9yZGVyLWJsdWUtNTAwIGRhcms6Ym9yZGVyLWJsdWUtNDAwIHNjYWxlLTEwNScgXG4gICAgICAgICAgICAgICAgICA6ICdib3JkZXItZ3JheS0yMDAgZGFyazpib3JkZXItZ3JheS03MDAnXG4gICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICB7LyogUG9wdWxhciBCYWRnZSAqL31cbiAgICAgICAgICAgICAge3BsYW4ucG9wdWxhciAmJiAoXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSAtdG9wLTQgbGVmdC0xLzIgdHJhbnNmb3JtIC10cmFuc2xhdGUteC0xLzJcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JhZGllbnQtdG8tciBmcm9tLWJsdWUtNjAwIHRvLXB1cnBsZS02MDAgdGV4dC13aGl0ZSBweC02IHB5LTIgcm91bmRlZC1mdWxsIHRleHQtc20gZm9udC1zZW1pYm9sZCBmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTIgcnRsOnNwYWNlLXgtcmV2ZXJzZVwiPlxuICAgICAgICAgICAgICAgICAgICA8Q3Jvd24gY2xhc3NOYW1lPVwidy00IGgtNFwiIC8+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuPk1vc3QgUG9wdWxhcjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApfVxuXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC04XCI+XG4gICAgICAgICAgICAgICAgey8qIFBsYW4gSGVhZGVyICovfVxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgbWItOFwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2B3LTE2IGgtMTYgbXgtYXV0byBtYi00IGJnLWdyYWRpZW50LXRvLWJyICR7cGxhbi5ncmFkaWVudH0gcm91bmRlZC0yeGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJgfT5cbiAgICAgICAgICAgICAgICAgICAgPHBsYW4uaWNvbiBjbGFzc05hbWU9XCJ3LTggaC04IHRleHQtd2hpdGVcIiAvPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMCBkYXJrOnRleHQtd2hpdGUgbWItMlwiPlxuICAgICAgICAgICAgICAgICAgICB7cGxhbi5uYW1lfVxuICAgICAgICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCBkYXJrOnRleHQtZ3JheS00MDAgbWItNlwiPlxuICAgICAgICAgICAgICAgICAgICB7cGxhbi5kZXNjcmlwdGlvbn1cbiAgICAgICAgICAgICAgICAgIDwvcD5cblxuICAgICAgICAgICAgICAgICAgey8qIFByaWNlICovfVxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi02XCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1iYXNlbGluZSBqdXN0aWZ5LWNlbnRlciBzcGFjZS14LTIgcnRsOnNwYWNlLXgtcmV2ZXJzZVwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtNXhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwIGRhcms6dGV4dC13aGl0ZVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgJHtpc1llYXJseSA/IHBsYW4ueWVhcmx5UHJpY2UgOiBwbGFuLnByaWNlfVxuICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIGRhcms6dGV4dC1ncmF5LTQwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAge3BsYW4ucGVyaW9kfVxuICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIHtpc1llYXJseSAmJiBwbGFuLnByaWNlICE9PSAnMCcgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyZWVuLTYwMCBkYXJrOnRleHQtZ3JlZW4tNDAwIG10LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIFNhdmUgJHsocGFyc2VJbnQocGxhbi5wcmljZSkgKiAxMiAtIHBhcnNlSW50KHBsYW4ueWVhcmx5UHJpY2UpICogMTIpLnRvRml4ZWQoMCl9IHBlciB5ZWFyXG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgIHsvKiBGZWF0dXJlcyAqL31cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNCBtYi04XCI+XG4gICAgICAgICAgICAgICAgICB7cGxhbi5mZWF0dXJlcy5tYXAoKGZlYXR1cmUsIGZlYXR1cmVJbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGtleT17ZmVhdHVyZUluZGV4fSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTMgcnRsOnNwYWNlLXgtcmV2ZXJzZVwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxDaGVjayBjbGFzc05hbWU9XCJ3LTUgaC01IHRleHQtZ3JlZW4tNTAwIGZsZXgtc2hyaW5rLTBcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgZGFyazp0ZXh0LWdyYXktMzAwXCI+e2ZlYXR1cmV9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgey8qIENUQSBCdXR0b24gKi99XG4gICAgICAgICAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9e2B3LWZ1bGwgcHktNCBweC02IHJvdW5kZWQteGwgZm9udC1zZW1pYm9sZCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgdHJhbnNmb3JtIGhvdmVyOnNjYWxlLTEwNSBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBzcGFjZS14LTIgcnRsOnNwYWNlLXgtcmV2ZXJzZSAke3BsYW4uY3RhU3R5bGV9YH0+XG4gICAgICAgICAgICAgICAgICA8c3Bhbj57cGxhbi5jdGF9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAge2xhbmd1YWdlID09PSAnYXInID8gKFxuICAgICAgICAgICAgICAgICAgICA8QXJyb3dMZWZ0IGNsYXNzTmFtZT1cInctNSBoLTVcIiAvPlxuICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgPEFycm93UmlnaHQgY2xhc3NOYW1lPVwidy01IGgtNVwiIC8+XG4gICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICkpfVxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogQm90dG9tIE5vdGUgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgbXQtMTZcIj5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIGRhcms6dGV4dC1ncmF5LTQwMCBtYi00XCI+XG4gICAgICAgICAgICBBbGwgcGxhbnMgaW5jbHVkZSAxNC1kYXkgZnJlZSB0cmlhbCDigKIgTm8gY3JlZGl0IGNhcmQgcmVxdWlyZWQg4oCiIENhbmNlbCBhbnl0aW1lXG4gICAgICAgICAgPC9wPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgc3BhY2UteC02IHJ0bDpzcGFjZS14LXJldmVyc2UgdGV4dC1zbSB0ZXh0LWdyYXktNTAwIGRhcms6dGV4dC1ncmF5LTQwMFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTIgcnRsOnNwYWNlLXgtcmV2ZXJzZVwiPlxuICAgICAgICAgICAgICA8Q2hlY2sgY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LWdyZWVuLTUwMFwiIC8+XG4gICAgICAgICAgICAgIDxzcGFuPlNTTCBFbmNyeXB0ZWQ8L3NwYW4+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yIHJ0bDpzcGFjZS14LXJldmVyc2VcIj5cbiAgICAgICAgICAgICAgPENoZWNrIGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC1ncmVlbi01MDBcIiAvPlxuICAgICAgICAgICAgICA8c3Bhbj5HRFBSIENvbXBsaWFudDwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTIgcnRsOnNwYWNlLXgtcmV2ZXJzZVwiPlxuICAgICAgICAgICAgICA8Q2hlY2sgY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LWdyZWVuLTUwMFwiIC8+XG4gICAgICAgICAgICAgIDxzcGFuPjI0LzcgU3VwcG9ydDwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvc2VjdGlvbj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUxhbmd1YWdlIiwiQ2hlY2siLCJTdGFyIiwiWmFwIiwiQ3Jvd24iLCJCdWlsZGluZyIsIkFycm93TGVmdCIsIkFycm93UmlnaHQiLCJQcmljaW5nIiwibGFuZ3VhZ2UiLCJ0IiwiaXNZZWFybHkiLCJzZXRJc1llYXJseSIsInBsYW5zIiwibmFtZSIsInByaWNlIiwicGVyaW9kIiwieWVhcmx5UHJpY2UiLCJkZXNjcmlwdGlvbiIsImljb24iLCJjb2xvciIsImdyYWRpZW50IiwicG9wdWxhciIsImZlYXR1cmVzIiwiY3RhIiwiY3RhU3R5bGUiLCJzZWN0aW9uIiwiaWQiLCJjbGFzc05hbWUiLCJkaXYiLCJoMiIsInAiLCJidXR0b24iLCJvbkNsaWNrIiwic3BhbiIsIm1hcCIsInBsYW4iLCJpbmRleCIsImgzIiwicGFyc2VJbnQiLCJ0b0ZpeGVkIiwiZmVhdHVyZSIsImZlYXR1cmVJbmRleCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Pricing.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/LanguageContext.tsx":
/*!******************************************!*\
  !*** ./src/contexts/LanguageContext.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LanguageProvider: () => (/* binding */ LanguageProvider),\n/* harmony export */   useLanguage: () => (/* binding */ useLanguage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ LanguageProvider,useLanguage auto */ \n\nconst LanguageContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst translations = {\n    ar: {\n        // Navigation\n        'nav.features': 'المميزات',\n        'nav.pricing': 'الأسعار',\n        'nav.about': 'حولنا',\n        'nav.contact': 'اتصل بنا',\n        'nav.login': 'تسجيل الدخول',\n        'nav.signup': 'إنشاء حساب',\n        // Hero Section\n        'hero.title': 'حول ملفات PDF إلى اختبارات تفاعلية',\n        'hero.subtitle': 'في دقائق معدودة',\n        'hero.description': 'استخدم الذكاء الاصطناعي لتحويل أي ملف PDF إلى اختبار تفاعلي مع تحليلات متقدمة وتقييم تلقائي. مثالي للمعلمين والطلاب ومنصات التعلم الإلكتروني.',\n        'hero.cta.primary': 'ابدأ مجاناً',\n        'hero.cta.secondary': 'شاهد العرض التوضيحي',\n        'hero.trusted': 'يثق به أكثر من 10,000 معلم حول العالم',\n        // Features\n        'features.title': 'مميزات قوية لتعليم أفضل',\n        'features.subtitle': 'كل ما تحتاجه لإنشاء اختبارات احترافية وتحليل النتائج',\n        'feature.ai.title': 'ذكاء اصطناعي متقدم',\n        'feature.ai.description': 'يحلل محتوى PDF ويولد أسئلة متنوعة تلقائياً',\n        'feature.interactive.title': 'اختبارات تفاعلية',\n        'feature.interactive.description': 'أسئلة متعددة الخيارات، صح/خطأ، وأسئلة مفتوحة',\n        'feature.analytics.title': 'تحليلات شاملة',\n        'feature.analytics.description': 'تقارير مفصلة عن أداء الطلاب ونقاط القوة والضعف',\n        'feature.realtime.title': 'نتائج فورية',\n        'feature.realtime.description': 'تصحيح تلقائي وتقييم فوري مع ملاحظات مفصلة',\n        'feature.multilang.title': 'دعم متعدد اللغات',\n        'feature.multilang.description': 'يدعم العربية والإنجليزية مع واجهة سهلة الاستخدام',\n        'feature.secure.title': 'آمن وموثوق',\n        'feature.secure.description': 'حماية عالية للبيانات مع نسخ احتياطية تلقائية',\n        // Pricing\n        'pricing.title': 'خطط تناسب احتياجاتك',\n        'pricing.subtitle': 'ابدأ مجاناً وارتقِ حسب نموك',\n        'pricing.monthly': 'شهري',\n        'pricing.yearly': 'سنوي',\n        'pricing.save': 'وفر 20%',\n        'plan.free.name': 'مجاني',\n        'plan.free.price': '0',\n        'plan.free.period': '/شهر',\n        'plan.free.description': 'مثالي للمعلمين المبتدئين',\n        'plan.pro.name': 'احترافي',\n        'plan.pro.price': '19',\n        'plan.pro.period': '/شهر',\n        'plan.pro.description': 'للمعلمين النشطين',\n        'plan.enterprise.name': 'مؤسسات',\n        'plan.enterprise.price': '99',\n        'plan.enterprise.period': '/شهر',\n        'plan.enterprise.description': 'للمدارس والجامعات',\n        'pricing.cta': 'ابدأ الآن',\n        'pricing.contact': 'تواصل معنا',\n        // Footer\n        'footer.product': 'المنتج',\n        'footer.company': 'الشركة',\n        'footer.support': 'الدعم',\n        'footer.legal': 'قانوني',\n        'footer.rights': 'جميع الحقوق محفوظة',\n        // Common\n        'common.loading': 'جاري التحميل...',\n        'common.error': 'حدث خطأ',\n        'common.success': 'تم بنجاح',\n        'common.cancel': 'إلغاء',\n        'common.save': 'حفظ',\n        'common.edit': 'تعديل',\n        'common.delete': 'حذف',\n        'common.view': 'عرض'\n    },\n    en: {\n        // Navigation\n        'nav.features': 'Features',\n        'nav.pricing': 'Pricing',\n        'nav.about': 'About',\n        'nav.contact': 'Contact',\n        'nav.login': 'Login',\n        'nav.signup': 'Sign Up',\n        // Hero Section\n        'hero.title': 'Transform PDFs into Interactive Exams',\n        'hero.subtitle': 'in Minutes',\n        'hero.description': 'Use AI to convert any PDF into interactive exams with advanced analytics and automatic grading. Perfect for teachers, students, and e-learning platforms.',\n        'hero.cta.primary': 'Start Free',\n        'hero.cta.secondary': 'Watch Demo',\n        'hero.trusted': 'Trusted by 10,000+ teachers worldwide',\n        // Features\n        'features.title': 'Powerful Features for Better Learning',\n        'features.subtitle': 'Everything you need to create professional exams and analyze results',\n        'feature.ai.title': 'Advanced AI',\n        'feature.ai.description': 'Analyzes PDF content and generates diverse questions automatically',\n        'feature.interactive.title': 'Interactive Exams',\n        'feature.interactive.description': 'Multiple choice, true/false, and open-ended questions',\n        'feature.analytics.title': 'Comprehensive Analytics',\n        'feature.analytics.description': 'Detailed reports on student performance and learning gaps',\n        'feature.realtime.title': 'Instant Results',\n        'feature.realtime.description': 'Automatic grading and immediate feedback with detailed notes',\n        'feature.multilang.title': 'Multi-language Support',\n        'feature.multilang.description': 'Supports Arabic and English with intuitive interface',\n        'feature.secure.title': 'Secure & Reliable',\n        'feature.secure.description': 'High-level data protection with automatic backups',\n        // Pricing\n        'pricing.title': 'Plans That Fit Your Needs',\n        'pricing.subtitle': 'Start free and scale as you grow',\n        'pricing.monthly': 'Monthly',\n        'pricing.yearly': 'Yearly',\n        'pricing.save': 'Save 20%',\n        'plan.free.name': 'Free',\n        'plan.free.price': '0',\n        'plan.free.period': '/month',\n        'plan.free.description': 'Perfect for getting started',\n        'plan.pro.name': 'Professional',\n        'plan.pro.price': '19',\n        'plan.pro.period': '/month',\n        'plan.pro.description': 'For active teachers',\n        'plan.enterprise.name': 'Enterprise',\n        'plan.enterprise.price': '99',\n        'plan.enterprise.period': '/month',\n        'plan.enterprise.description': 'For schools and universities',\n        'pricing.cta': 'Get Started',\n        'pricing.contact': 'Contact Us',\n        // Footer\n        'footer.product': 'Product',\n        'footer.company': 'Company',\n        'footer.support': 'Support',\n        'footer.legal': 'Legal',\n        'footer.rights': 'All rights reserved',\n        // Common\n        'common.loading': 'Loading...',\n        'common.error': 'An error occurred',\n        'common.success': 'Success',\n        'common.cancel': 'Cancel',\n        'common.save': 'Save',\n        'common.edit': 'Edit',\n        'common.delete': 'Delete',\n        'common.view': 'View'\n    }\n};\nfunction LanguageProvider({ children }) {\n    const [language, setLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('ar');\n    const [theme, setTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('light');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LanguageProvider.useEffect\": ()=>{\n            // Load saved preferences\n            const savedLang = localStorage.getItem('language');\n            const savedTheme = localStorage.getItem('theme');\n            if (savedLang) setLanguage(savedLang);\n            if (savedTheme) setTheme(savedTheme);\n            // Apply theme to document\n            document.documentElement.classList.toggle('dark', savedTheme === 'dark');\n            document.documentElement.setAttribute('dir', savedLang === 'ar' ? 'rtl' : 'ltr');\n        }\n    }[\"LanguageProvider.useEffect\"], []);\n    const handleSetLanguage = (lang)=>{\n        setLanguage(lang);\n        localStorage.setItem('language', lang);\n        document.documentElement.setAttribute('dir', lang === 'ar' ? 'rtl' : 'ltr');\n    };\n    const handleSetTheme = (newTheme)=>{\n        setTheme(newTheme);\n        localStorage.setItem('theme', newTheme);\n        document.documentElement.classList.toggle('dark', newTheme === 'dark');\n    };\n    const t = (key)=>{\n        return translations[language][key] || key;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LanguageContext.Provider, {\n        value: {\n            language,\n            theme,\n            setLanguage: handleSetLanguage,\n            setTheme: handleSetTheme,\n            t\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\templete\\\\landing-page\\\\src\\\\contexts\\\\LanguageContext.tsx\",\n        lineNumber: 217,\n        columnNumber: 5\n    }, this);\n}\nfunction useLanguage() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(LanguageContext);\n    if (context === undefined) {\n        throw new Error('useLanguage must be used within a LanguageProvider');\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/LanguageContext.tsx\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/lucide-react","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Ctemplete%5Clanding-page%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ctemplete%5Clanding-page&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();