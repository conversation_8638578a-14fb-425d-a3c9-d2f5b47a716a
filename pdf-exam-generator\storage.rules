rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // PDF files - users can upload and read their own files
    match /pdfs/{userId}/{allPaths=**} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Profile images
    match /profiles/{userId}/{allPaths=**} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Public files (like app assets)
    match /public/{allPaths=**} {
      allow read: if true;
      allow write: if false;
    }
  }
}
