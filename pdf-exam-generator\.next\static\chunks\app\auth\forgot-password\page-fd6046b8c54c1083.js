(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[413],{646:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},844:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>g,A:()=>f});var s=r(5155),a=r(2115),l=r(6203),i=r(5317),n=r(3915),o=r(858);let c=(0,n.Wp)({apiKey:"your_api_key_here",authDomain:"your_project_id.firebaseapp.com",projectId:"your_project_id",storageBucket:"your_project_id.appspot.com",messagingSenderId:"your_sender_id",appId:"your_app_id",measurementId:"your_measurement_id"}),d=(0,l.xI)(c),u=(0,i.aU)(c);(0,o.c7)(c);let m=new l.HF,h=new l.sk;m.setCustomParameters({prompt:"select_account"}),h.setCustomParameters({display:"popup"});let x=(0,a.createContext)(void 0),f=()=>{let e=(0,a.useContext)(x);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e},g=e=>{let{children:t}=e,[r,n]=(0,a.useState)(null),[o,c]=(0,a.useState)(null),[f,g]=(0,a.useState)(!0),p=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!e)return;let r=(0,i.H9)(u,"users",e.uid),s=await (0,i.x7)(r);if(s.exists()){let e={...s.data(),lastLoginAt:new Date};await (0,i.BN)(r,e,{merge:!0}),c(e)}else{let{displayName:s,email:a,photoURL:l}=e,n=new Date,o={uid:e.uid,email:a||"",displayName:s||"",photoURL:l||void 0,role:t.role||"student",createdAt:n,lastLoginAt:n,preferences:{language:"ar",theme:"light",notifications:!0},stats:{totalExams:0,averageScore:0,totalQuestions:0,studyTime:0},...t};try{await (0,i.BN)(r,o),c(o)}catch(e){console.error("Error creating user profile:",e)}}},y=async(e,t)=>{g(!0);try{let r=await (0,l.x9)(d,e,t);await p(r.user)}catch(e){throw console.error("Error signing in:",e),e}finally{g(!1)}},b=async(e,t)=>{try{return await y(e,t),!0}catch(e){return!1}},w=async(e,t,r,s)=>{g(!0);try{let a=await (0,l.eJ)(d,e,t);await (0,l.r7)(a.user,{displayName:r}),await p(a.user,{role:s,displayName:r})}catch(e){throw console.error("Error signing up:",e),e}finally{g(!1)}},j=async()=>{g(!0);try{let e=await (0,l.df)(d,m);await p(e.user)}catch(e){throw console.error("Error signing in with Google:",e),e}finally{g(!1)}},v=async()=>{g(!0);try{let e=await (0,l.df)(d,h);await p(e.user)}catch(e){throw console.error("Error signing in with Facebook:",e),e}finally{g(!1)}},N=async()=>{g(!0);try{await (0,l.CI)(d),n(null),c(null),localStorage.removeItem("examResults"),localStorage.removeItem("examAnswers"),localStorage.removeItem("uploadedPDF"),localStorage.removeItem("examConfig")}catch(e){throw console.error("Error signing out:",e),e}finally{g(!1)}},_=async e=>{try{await (0,l.J1)(d,e)}catch(e){throw console.error("Error sending password reset email:",e),e}},A=async e=>{if(r)try{let t=(0,i.H9)(u,"users",r.uid);await (0,i.BN)(t,e,{merge:!0}),o&&c({...o,...e})}catch(e){throw console.error("Error updating user profile:",e),e}};return(0,a.useEffect)(()=>(0,l.hg)(d,async e=>{e?(n(e),await p(e)):(n(null),c(null)),g(!1)}),[]),(0,s.jsx)(x.Provider,{value:{user:r,userProfile:o,loading:f,isAuthenticated:!!r,signIn:y,signUp:w,signInWithGoogle:j,signInWithFacebook:v,logout:N,resetPassword:_,updateUserProfile:A,login:b},children:t})}},5039:(e,t,r)=>{Promise.resolve().then(r.bind(r,6195))},6195:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var s=r(5155),a=r(2115),l=r(6874),i=r.n(l),n=r(844),o=r(646),c=r(7550),d=r(8883);function u(){let[e,t]=(0,a.useState)(""),[r,l]=(0,a.useState)(""),[u,m]=(0,a.useState)(!1),[h,x]=(0,a.useState)(!1),{resetPassword:f}=(0,n.A)(),g=async t=>{t.preventDefault(),l(""),m(!0);try{await f(e),x(!0)}catch(e){l(p(e.code||"unknown-error"))}finally{m(!1)}},p=e=>{switch(e){case"auth/user-not-found":return"لا يوجد حساب مرتبط بهذا البريد الإلكتروني";case"auth/invalid-email":return"البريد الإلكتروني غير صالح";case"auth/too-many-requests":return"تم تجاوز عدد المحاولات المسموح. حاول مرة أخرى لاحقاً";default:return"حدث خطأ أثناء إرسال رابط إعادة التعيين. حاول مرة أخرى"}};return h?(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4",children:(0,s.jsx)("div",{className:"max-w-md w-full space-y-8",children:(0,s.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl p-8 text-center",children:[(0,s.jsx)("div",{className:"mx-auto h-16 w-16 bg-green-100 rounded-full flex items-center justify-center mb-6",children:(0,s.jsx)(o.A,{className:"h-8 w-8 text-green-600"})}),(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"تم إرسال الرابط!"}),(0,s.jsx)("p",{className:"text-gray-600 mb-6",children:"تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني:"}),(0,s.jsx)("p",{className:"text-blue-600 font-medium mb-6",children:e}),(0,s.jsx)("p",{className:"text-sm text-gray-500 mb-8",children:"تحقق من صندوق الوارد وصندوق الرسائل المزعجة. قد يستغرق وصول الرسالة بضع دقائق."}),(0,s.jsxs)(i(),{href:"/auth/login",className:"inline-flex items-center justify-center w-full py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors",children:[(0,s.jsx)(c.A,{className:"h-4 w-4 ml-2"}),"العودة إلى تسجيل الدخول"]})]})})}):(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4",children:(0,s.jsx)("div",{className:"max-w-md w-full space-y-8",children:(0,s.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl p-8",children:[(0,s.jsxs)("div",{className:"text-center mb-8",children:[(0,s.jsx)("div",{className:"mx-auto h-12 w-12 bg-blue-600 rounded-xl flex items-center justify-center mb-4",children:(0,s.jsx)(d.A,{className:"h-6 w-6 text-white"})}),(0,s.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-2",children:"نسيت كلمة المرور؟"}),(0,s.jsx)("p",{className:"text-gray-600",children:"أدخل بريدك الإلكتروني وسنرسل لك رابط إعادة التعيين"})]}),r&&(0,s.jsx)("div",{className:"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg",children:(0,s.jsx)("p",{className:"text-red-600 text-sm text-center",children:r})}),(0,s.jsxs)("form",{onSubmit:g,className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"البريد الإلكتروني"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:(0,s.jsx)(d.A,{className:"h-5 w-5 text-gray-400"})}),(0,s.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:e,onChange:e=>t(e.target.value),className:"block w-full pr-10 pl-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-right",placeholder:"أدخل بريدك الإلكتروني",dir:"rtl"})]})]}),(0,s.jsx)("button",{type:"submit",disabled:u,className:"w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:u?(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"}),"جاري الإرسال..."]}):"إرسال رابط إعادة التعيين"})]}),(0,s.jsx)("div",{className:"mt-6 text-center",children:(0,s.jsxs)(i(),{href:"/auth/login",className:"inline-flex items-center text-sm text-blue-600 hover:text-blue-500",children:[(0,s.jsx)(c.A,{className:"h-4 w-4 ml-1"}),"العودة إلى تسجيل الدخول"]})})]})})})}},7550:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},8883:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[992,811,100,470,874,441,684,358],()=>t(5039)),_N_E=e.O()}]);