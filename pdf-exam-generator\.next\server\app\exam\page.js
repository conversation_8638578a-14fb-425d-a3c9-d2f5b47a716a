(()=>{var e={};e.id=344,e.ids=[344],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28559:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37366:e=>{"use strict";e.exports=require("dns")},47719:(e,t,s)=>{Promise.resolve().then(s.bind(s,92887))},48730:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},55511:e=>{"use strict";e.exports=require("crypto")},56872:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\templete\\\\pdf-exam-generator\\\\src\\\\app\\\\exam\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\templete\\pdf-exam-generator\\src\\app\\exam\\page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},85734:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>d,routeModule:()=>u,tree:()=>c});var r=s(65239),a=s(48088),n=s(88170),i=s.n(n),l=s(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let c={children:["",{children:["exam",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,56872)),"D:\\templete\\pdf-exam-generator\\src\\app\\exam\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:"/manifest.webmanifest"}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"D:\\templete\\pdf-exam-generator\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:"/manifest.webmanifest"}}]}.children,d=["D:\\templete\\pdf-exam-generator\\src\\app\\exam\\page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/exam/page",pathname:"/exam",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},87887:(e,t,s)=>{Promise.resolve().then(s.bind(s,56872))},91645:e=>{"use strict";e.exports=require("net")},92887:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>f});var r=s(60687),a=s(43210),n=s(16189),i=s(62688);let l=(0,i.A)("flag",[["path",{d:"M4 22V4a1 1 0 0 1 .4-.8A6 6 0 0 1 8 2c3 0 5 2 7.333 2q2 0 3.067-.8A1 1 0 0 1 20 4v10a1 1 0 0 1-.4.8A6 6 0 0 1 16 16c-3 0-5-2-8-2a6 6 0 0 0-4 1.528",key:"1jaruq"}]]),o=(0,i.A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]);var c=s(5336);let d=(0,i.A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);function x({questions:e,currentQuestion:t,answers:s,onAnswerChange:n,onQuestionChange:i,onSubmit:x}){let[u,m]=(0,a.useState)(new Set),p=e[t],h=s[p.id];return(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden",children:[(0,r.jsx)("div",{className:"px-6 py-4 border-b border-gray-200 bg-gray-50",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("span",{className:"text-sm font-medium text-gray-600",children:["Question ",t+1," of ",e.length]}),(0,r.jsx)("span",{className:"px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded-full capitalize",children:p.type.replace("-"," ")})]}),(0,r.jsxs)("button",{onClick:()=>{m(e=>{let t=new Set(e);return t.has(p.id)?t.delete(p.id):t.add(p.id),t})},className:`flex items-center space-x-1 px-3 py-1 rounded-lg transition-colors ${u.has(p.id)?"bg-yellow-100 text-yellow-800":"bg-gray-100 text-gray-600 hover:bg-gray-200"}`,children:[(0,r.jsx)(l,{className:"w-4 h-4"}),(0,r.jsx)("span",{className:"text-sm",children:u.has(p.id)?"Flagged":"Flag"})]})]})}),(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsx)("h2",{className:"text-lg font-medium text-gray-900 mb-6 leading-relaxed",children:p.question}),(()=>{switch(p.type){case"multiple-choice":return(0,r.jsx)("div",{className:"space-y-3",children:p.options?.map((e,t)=>(0,r.jsxs)("label",{className:"flex items-start space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors",children:[(0,r.jsx)("input",{type:"radio",name:`question-${p.id}`,value:t,checked:h===t,onChange:()=>n(p.id,t),className:"mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"}),(0,r.jsx)("span",{className:"text-gray-900 flex-1",children:e})]},t))});case"true-false":return(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("label",{className:"flex items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors",children:[(0,r.jsx)("input",{type:"radio",name:`question-${p.id}`,value:"true",checked:!0===h,onChange:()=>n(p.id,!0),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"}),(0,r.jsx)("span",{className:"text-gray-900",children:"True"})]}),(0,r.jsxs)("label",{className:"flex items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors",children:[(0,r.jsx)("input",{type:"radio",name:`question-${p.id}`,value:"false",checked:!1===h,onChange:()=>n(p.id,!1),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"}),(0,r.jsx)("span",{className:"text-gray-900",children:"False"})]})]});case"short-answer":return(0,r.jsx)("textarea",{value:h||"",onChange:e=>n(p.id,e.target.value),placeholder:"Type your answer here...",className:"w-full h-32 p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"});case"fill-blank":return(0,r.jsx)("div",{className:"space-y-4",children:(0,r.jsx)("p",{className:"text-gray-700",children:p.question.split("______").map((e,t,s)=>(0,r.jsxs)("span",{children:[e,t<s.length-1&&(0,r.jsx)("input",{type:"text",value:h||"",onChange:e=>n(p.id,e.target.value),className:"mx-2 px-3 py-1 border-b-2 border-blue-300 focus:outline-none focus:border-blue-500 bg-transparent min-w-32",placeholder:"your answer"})]},t))})});default:return null}})()]}),(0,r.jsx)("div",{className:"px-6 py-4 border-t border-gray-200 bg-gray-50",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("button",{onClick:()=>{t>0&&i(t-1)},disabled:0===t,className:"flex items-center space-x-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:[(0,r.jsx)(o,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"Previous"})]}),(0,r.jsx)("div",{className:"flex items-center space-x-2",children:e.map((a,n)=>(0,r.jsx)("button",{onClick:()=>i(n),className:`w-8 h-8 rounded-full text-sm font-medium transition-colors ${n===t?"bg-blue-600 text-white":void 0!==s[e[n].id]?"bg-green-100 text-green-800":"bg-gray-100 text-gray-600 hover:bg-gray-200"}`,children:n+1},n))}),t===e.length-1?(0,r.jsxs)("button",{onClick:x,className:"flex items-center space-x-2 px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium",children:[(0,r.jsx)(c.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"Submit Exam"})]}):(0,r.jsxs)("button",{onClick:()=>{t<e.length-1&&i(t+1)},className:"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,r.jsx)("span",{children:"Next"}),(0,r.jsx)(d,{className:"w-4 h-4"})]})]})}),(0,r.jsxs)("div",{className:"border-t border-gray-200 bg-gray-50 p-4",children:[(0,r.jsx)("h3",{className:"text-sm font-medium text-gray-900 mb-3",children:"Question Overview"}),(0,r.jsx)("div",{className:"grid grid-cols-10 gap-2",children:e.map((a,n)=>{let o=void 0!==s[e[n].id],c=u.has(e[n].id),d=n===t;return(0,r.jsxs)("button",{onClick:()=>i(n),className:`relative w-8 h-8 rounded text-xs font-medium transition-colors ${d?"bg-blue-600 text-white":o?"bg-green-100 text-green-800 hover:bg-green-200":"bg-gray-100 text-gray-600 hover:bg-gray-200"}`,children:[n+1,c&&(0,r.jsx)(l,{className:"absolute -top-1 -right-1 w-3 h-3 text-yellow-500"})]},n)})}),(0,r.jsxs)("div",{className:"mt-4 flex items-center justify-between text-xs text-gray-600",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)("div",{className:"w-3 h-3 bg-green-100 rounded"}),(0,r.jsx)("span",{children:"Answered"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)("div",{className:"w-3 h-3 bg-gray-100 rounded"}),(0,r.jsx)("span",{children:"Unanswered"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(l,{className:"w-3 h-3 text-yellow-500"}),(0,r.jsx)("span",{children:"Flagged"})]})]}),(0,r.jsxs)("span",{children:[Object.keys(s).length,"/",e.length," completed"]})]})]})]})}let u=(0,i.A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);var m=s(48730);function p({timeRemaining:e,onTimeUp:t,onTimeUpdate:s}){let[n,i]=(0,a.useState)(e),l=n<=600,o=n<=300;return(0,r.jsxs)("div",{className:`flex items-center space-x-2 px-3 py-2 rounded-lg border ${n<=300?"bg-red-50 border-red-200":n<=600?"bg-yellow-50 border-yellow-200":"bg-gray-50 border-gray-200"}`,children:[l?(0,r.jsx)(u,{className:`w-4 h-4 ${o?"text-red-600":"text-yellow-600"}`}):(0,r.jsx)(m.A,{className:"w-4 h-4 text-gray-600"}),(0,r.jsx)("span",{className:`font-mono text-sm font-medium ${n<=300?"text-red-600":n<=600?"text-yellow-600":"text-gray-700"}`,children:(e=>{let t=Math.floor(e/3600),s=Math.floor(e%3600/60),r=e%60;return t>0?`${t}:${s.toString().padStart(2,"0")}:${r.toString().padStart(2,"0")}`:`${s}:${r.toString().padStart(2,"0")}`})(n)}),l&&(0,r.jsx)("span",{className:`text-xs ${o?"text-red-600":"text-yellow-600"}`,children:o?"Time running out!":"Time warning"})]})}let h=(0,i.A)("circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]);function g({current:e,total:t,answered:s}){let a=s/t*100;return(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"Progress:"}),(0,r.jsx)("div",{className:"w-32 bg-gray-200 rounded-full h-2",children:(0,r.jsx)("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:`${a}%`}})}),(0,r.jsxs)("span",{className:"text-sm text-gray-600",children:[s,"/",t]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-600",children:[(0,r.jsx)(h,{className:"w-4 h-4"}),(0,r.jsxs)("span",{children:["Question ",e]})]})]})}var b=s(28559);let y=[{id:1,type:"multiple-choice",question:"What is the primary goal of supervised learning in machine learning?",options:["To find hidden patterns in unlabeled data","To learn from labeled training data to make predictions on new data","To reduce the dimensionality of the dataset","To cluster similar data points together"],correctAnswer:1,explanation:"Supervised learning uses labeled training data to learn patterns and make predictions on new, unseen data."},{id:2,type:"true-false",question:"Neural networks can only be used for classification problems.",correctAnswer:!1,explanation:"Neural networks can be used for both classification and regression problems, as well as other tasks like clustering and dimensionality reduction."},{id:3,type:"multiple-choice",question:"Which of the following is NOT a common activation function in neural networks?",options:["ReLU (Rectified Linear Unit)","Sigmoid","Tanh","Linear Regression"],correctAnswer:3,explanation:"Linear Regression is a machine learning algorithm, not an activation function. ReLU, Sigmoid, and Tanh are all common activation functions."},{id:4,type:"short-answer",question:"Explain what overfitting means in machine learning and how it can be prevented.",correctAnswer:"Overfitting occurs when a model learns the training data too well, including noise and irrelevant patterns, leading to poor performance on new data. It can be prevented through techniques like cross-validation, regularization, early stopping, and using more training data.",explanation:"Overfitting is a common problem where the model memorizes the training data rather than learning generalizable patterns."},{id:5,type:"fill-blank",question:"The process of adjusting the weights in a neural network during training is called ______.",correctAnswer:"backpropagation",explanation:"Backpropagation is the algorithm used to calculate gradients and update weights in neural networks during training."}];function f(){let e=(0,n.useRouter)(),[t,s]=(0,a.useState)(null),[i,l]=(0,a.useState)(0),[o,c]=(0,a.useState)({}),[d,u]=(0,a.useState)(null),[m,h]=(0,a.useState)(!1),[f,j]=(0,a.useState)(!1),v=()=>{localStorage.setItem("examResults",JSON.stringify({answers:o,questions:y,config:t,completedAt:new Date().toISOString()})),e.push("/results")};return t?m?(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)("div",{className:"bg-white border-b border-gray-200 sticky top-0 z-10",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("h1",{className:"text-xl font-semibold text-gray-900",children:"Interactive Exam"}),(0,r.jsx)(g,{current:i+1,total:y.length,answered:Object.keys(o).length})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[null!==d&&(0,r.jsx)(p,{timeRemaining:d,onTimeUp:()=>{v()},onTimeUpdate:u}),(0,r.jsx)("button",{onClick:()=>j(!0),className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium",children:"Submit Exam"})]})]})})}),(0,r.jsx)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,r.jsx)(x,{questions:y,currentQuestion:i,answers:o,onAnswerChange:(e,t)=>{c(s=>({...s,[e]:t}))},onQuestionChange:l,onSubmit:()=>j(!0)})}),f&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-md mx-4",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Submit Exam?"}),(0,r.jsxs)("p",{className:"text-gray-600 mb-6",children:["Are you sure you want to submit your exam? You have answered ",Object.keys(o).length," out of ",y.length," questions."]}),(0,r.jsxs)("div",{className:"flex space-x-4",children:[(0,r.jsx)("button",{onClick:()=>j(!1),className:"px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors",children:"Continue Exam"}),(0,r.jsx)("button",{onClick:v,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Submit Exam"})]})]})})]}):(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,r.jsx)("div",{className:"max-w-2xl mx-auto p-8",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-8 text-center",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Ready to Start Your Exam?"}),(0,r.jsxs)("div",{className:"bg-blue-50 rounded-lg p-6 mb-8",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-blue-900 mb-4",children:"Exam Details"}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-blue-700 font-medium",children:"Questions:"}),(0,r.jsx)("p",{className:"text-blue-600",children:y.length})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-blue-700 font-medium",children:"Time Limit:"}),(0,r.jsx)("p",{className:"text-blue-600",children:t.hasTimeLimit?`${t.timeLimit} minutes`:"No limit"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-blue-700 font-medium",children:"Difficulty:"}),(0,r.jsx)("p",{className:"text-blue-600 capitalize",children:t.difficulty})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-blue-700 font-medium",children:"Language:"}),(0,r.jsx)("p",{className:"text-blue-600 capitalize",children:t.language})]})]})]}),(0,r.jsxs)("div",{className:"space-y-4 mb-8 text-left",children:[(0,r.jsx)("h3",{className:"font-semibold text-gray-900",children:"Instructions:"}),(0,r.jsxs)("ul",{className:"text-sm text-gray-600 space-y-2",children:[(0,r.jsx)("li",{children:"• Read each question carefully before answering"}),(0,r.jsx)("li",{children:"• You can navigate between questions using the Previous/Next buttons"}),(0,r.jsx)("li",{children:"• Your progress is automatically saved"}),(0,r.jsx)("li",{children:"• You can flag questions for review"}),t.hasTimeLimit&&(0,r.jsx)("li",{children:"• The exam will auto-submit when time runs out"})]})]}),(0,r.jsxs)("div",{className:"flex space-x-4 justify-center",children:[(0,r.jsxs)("button",{onClick:()=>{e.push("/analyze")},className:"px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,r.jsx)(b.A,{className:"w-4 h-4 inline mr-2"}),"Back to Configuration"]}),(0,r.jsx)("button",{onClick:()=>{h(!0)},className:"px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-semibold",children:"Start Exam"})]})]})})}):(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Loading exam..."})]})})}},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,248,658,647],()=>s(85734));module.exports=r})();