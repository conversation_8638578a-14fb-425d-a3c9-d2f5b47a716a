(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[983],{1007:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},2318:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(9946).A)("user-plus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},2657:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},2919:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(9946).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},4247:(e,s,r)=>{Promise.resolve().then(r.bind(r,6117))},5695:(e,s,r)=>{"use strict";var t=r(8999);r.o(t,"usePathname")&&r.d(s,{usePathname:function(){return t.usePathname}}),r.o(t,"useRouter")&&r.d(s,{useRouter:function(){return t.useRouter}})},6117:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>y});var t=r(5155),a=r(2115),l=r(5695),i=r(6874),n=r.n(i),o=r(844),d=r(9283),c=r(2318),m=r(1007),u=r(8883),x=r(2919),h=r(8749),g=r(2657);function y(){let[e,s]=(0,a.useState)({displayName:"",email:"",password:"",confirmPassword:"",role:"student"}),[r,i]=(0,a.useState)(!1),[y,f]=(0,a.useState)(!1),[p,b]=(0,a.useState)(""),[v,w]=(0,a.useState)(!1),{signUp:j,signInWithGoogle:N,signInWithFacebook:k}=(0,o.A)(),{t:A}=(0,d.o)(),C=(0,l.useRouter)(),M=r=>{s({...e,[r.target.name]:r.target.value})},P=async s=>{if(s.preventDefault(),b(""),e.password!==e.confirmPassword)return void b("كلمات المرور غير متطابقة");if(e.password.length<6)return void b("كلمة المرور يجب أن تكون 6 أحرف على الأقل");w(!0);try{await j(e.email,e.password,e.displayName,e.role),C.push("/dashboard")}catch(e){b(q(e.code||"unknown-error"))}finally{w(!1)}},F=async()=>{b(""),w(!0);try{await N(),C.push("/dashboard")}catch(e){b(q(e.code||"unknown-error"))}finally{w(!1)}},z=async()=>{b(""),w(!0);try{await k(),C.push("/dashboard")}catch(e){b(q(e.code||"unknown-error"))}finally{w(!1)}},q=e=>{switch(e){case"auth/email-already-in-use":return"هذا البريد الإلكتروني مستخدم بالفعل";case"auth/invalid-email":return"البريد الإلكتروني غير صالح";case"auth/weak-password":return"كلمة المرور ضعيفة جداً";case"auth/operation-not-allowed":return"تسجيل الحسابات الجديدة غير مفعل حالياً";default:return"حدث خطأ أثناء إنشاء الحساب. حاول مرة أخرى"}};return(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-green-50 to-emerald-100 flex items-center justify-center p-4",children:(0,t.jsx)("div",{className:"max-w-md w-full space-y-8",children:(0,t.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl p-8",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsx)("div",{className:"mx-auto h-12 w-12 bg-green-600 rounded-xl flex items-center justify-center mb-4",children:(0,t.jsx)(c.A,{className:"h-6 w-6 text-white"})}),(0,t.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-2",children:"إنشاء حساب جديد"}),(0,t.jsx)("p",{className:"text-gray-600",children:"انضم إلينا وابدأ رحلتك التعليمية"})]}),p&&(0,t.jsx)("div",{className:"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg",children:(0,t.jsx)("p",{className:"text-red-600 text-sm text-center",children:p})}),(0,t.jsxs)("form",{onSubmit:P,className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"displayName",className:"block text-sm font-medium text-gray-700 mb-2",children:"الاسم الكامل"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:(0,t.jsx)(m.A,{className:"h-5 w-5 text-gray-400"})}),(0,t.jsx)("input",{id:"displayName",name:"displayName",type:"text",required:!0,value:e.displayName,onChange:M,className:"block w-full pr-10 pl-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 text-right",placeholder:"أدخل اسمك الكامل",dir:"rtl"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"البريد الإلكتروني"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:(0,t.jsx)(u.A,{className:"h-5 w-5 text-gray-400"})}),(0,t.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:e.email,onChange:M,className:"block w-full pr-10 pl-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 text-right",placeholder:"أدخل بريدك الإلكتروني",dir:"rtl"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"role",className:"block text-sm font-medium text-gray-700 mb-2",children:"نوع الحساب"}),(0,t.jsxs)("select",{id:"role",name:"role",value:e.role,onChange:M,className:"block w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 text-right",dir:"rtl",children:[(0,t.jsx)("option",{value:"student",children:"طالب"}),(0,t.jsx)("option",{value:"teacher",children:"معلم"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-2",children:"كلمة المرور"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:(0,t.jsx)(x.A,{className:"h-5 w-5 text-gray-400"})}),(0,t.jsx)("input",{id:"password",name:"password",type:r?"text":"password",autoComplete:"new-password",required:!0,value:e.password,onChange:M,className:"block w-full pr-10 pl-10 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 text-right",placeholder:"أدخل كلمة المرور",dir:"rtl"}),(0,t.jsx)("button",{type:"button",className:"absolute inset-y-0 left-0 pl-3 flex items-center",onClick:()=>i(!r),children:r?(0,t.jsx)(h.A,{className:"h-5 w-5 text-gray-400 hover:text-gray-600"}):(0,t.jsx)(g.A,{className:"h-5 w-5 text-gray-400 hover:text-gray-600"})})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700 mb-2",children:"تأكيد كلمة المرور"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:(0,t.jsx)(x.A,{className:"h-5 w-5 text-gray-400"})}),(0,t.jsx)("input",{id:"confirmPassword",name:"confirmPassword",type:y?"text":"password",autoComplete:"new-password",required:!0,value:e.confirmPassword,onChange:M,className:"block w-full pr-10 pl-10 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 text-right",placeholder:"أعد إدخال كلمة المرور",dir:"rtl"}),(0,t.jsx)("button",{type:"button",className:"absolute inset-y-0 left-0 pl-3 flex items-center",onClick:()=>f(!y),children:y?(0,t.jsx)(h.A,{className:"h-5 w-5 text-gray-400 hover:text-gray-600"}):(0,t.jsx)(g.A,{className:"h-5 w-5 text-gray-400 hover:text-gray-600"})})]})]}),(0,t.jsx)("button",{type:"submit",disabled:v,className:"w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:v?(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"}),"جاري إنشاء الحساب..."]}):"إنشاء حساب"})]}),(0,t.jsx)("div",{className:"mt-6",children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,t.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,t.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,t.jsx)("span",{className:"px-2 bg-white text-gray-500",children:"أو"})})]})}),(0,t.jsxs)("div",{className:"mt-6 space-y-3",children:[(0,t.jsxs)("button",{onClick:F,disabled:v,className:"w-full flex justify-center items-center py-3 px-4 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:[(0,t.jsxs)("svg",{className:"w-5 h-5 ml-2",viewBox:"0 0 24 24",children:[(0,t.jsx)("path",{fill:"#4285F4",d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"}),(0,t.jsx)("path",{fill:"#34A853",d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"}),(0,t.jsx)("path",{fill:"#FBBC05",d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"}),(0,t.jsx)("path",{fill:"#EA4335",d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"})]}),"التسجيل بـ Google"]}),(0,t.jsxs)("button",{onClick:z,disabled:v,className:"w-full flex justify-center items-center py-3 px-4 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:[(0,t.jsx)("svg",{className:"w-5 h-5 ml-2",fill:"#1877F2",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{d:"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"})}),"التسجيل بـ Facebook"]})]}),(0,t.jsx)("div",{className:"mt-6 text-center",children:(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:["لديك حساب بالفعل؟"," ",(0,t.jsx)(n(),{href:"/auth/login",className:"font-medium text-green-600 hover:text-green-500",children:"تسجيل الدخول"})]})})]})})})}},8749:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(9946).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},8883:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(9946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])}},e=>{var s=s=>e(e.s=s);e.O(0,[992,811,100,470,874,642,441,684,358],()=>s(4247)),_N_E=e.O()}]);