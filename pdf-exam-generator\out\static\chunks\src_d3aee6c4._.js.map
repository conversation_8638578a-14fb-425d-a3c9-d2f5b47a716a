{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/templete/pdf-exam-generator/src/lib/firebase.ts"], "sourcesContent": ["import { initializeApp, getApps } from 'firebase/app';\nimport { getAuth, GoogleAuthProvider, FacebookAuthProvider } from 'firebase/auth';\nimport { getFirestore } from 'firebase/firestore';\nimport { getStorage } from 'firebase/storage';\n\nconst firebaseConfig = {\n  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY || 'demo-key',\n  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN || 'demo-project.firebaseapp.com',\n  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID || 'demo-project',\n  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET || 'demo-project.appspot.com',\n  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID || '123456789',\n  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID || '1:123456789:web:abcdef',\n  measurementId: process.env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID || 'G-ABCDEF'\n};\n\n// Initialize Firebase only if it hasn't been initialized\nconst app = getApps().length === 0 ? initializeApp(firebaseConfig) : getApps()[0];\n\n// Initialize Firebase Authentication and get a reference to the service\nexport const auth = getAuth(app);\n\n// Initialize Cloud Firestore and get a reference to the service\nexport const db = getFirestore(app);\n\n// Initialize Cloud Storage and get a reference to the service\nexport const storage = getStorage(app);\n\n// Initialize providers\nexport const googleProvider = new GoogleAuthProvider();\nexport const facebookProvider = new FacebookAuthProvider();\n\n// Configure providers\ngoogleProvider.setCustomParameters({\n  prompt: 'select_account'\n});\n\nfacebookProvider.setCustomParameters({\n  display: 'popup'\n});\n\nexport default app;\n"], "names": [], "mappings": ";;;;;;;;AAMU;AANV;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AAAA;;;;;AAEA,MAAM,iBAAiB;IACrB,QAAQ,wCAA4C;IACpD,YAAY,wCAAgD;IAC5D,WAAW,wCAA+C;IAC1D,eAAe,wCAAmD;IAClE,mBAAmB,wCAAwD;IAC3E,OAAO,wCAA2C;IAClD,eAAe,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,mCAAmC,IAAI;AACpE;AAEA,yDAAyD;AACzD,MAAM,MAAM,CAAA,GAAA,uLAAA,CAAA,UAAO,AAAD,IAAI,MAAM,KAAK,IAAI,CAAA,GAAA,uLAAA,CAAA,gBAAa,AAAD,EAAE,kBAAkB,CAAA,GAAA,uLAAA,CAAA,UAAO,AAAD,GAAG,CAAC,EAAE;AAG1E,MAAM,OAAO,CAAA,GAAA,6MAAA,CAAA,UAAO,AAAD,EAAE;AAGrB,MAAM,KAAK,CAAA,GAAA,sKAAA,CAAA,eAAY,AAAD,EAAE;AAGxB,MAAM,UAAU,CAAA,GAAA,oKAAA,CAAA,aAAU,AAAD,EAAE;AAG3B,MAAM,iBAAiB,IAAI,wNAAA,CAAA,qBAAkB;AAC7C,MAAM,mBAAmB,IAAI,0NAAA,CAAA,uBAAoB;AAExD,sBAAsB;AACtB,eAAe,mBAAmB,CAAC;IACjC,QAAQ;AACV;AAEA,iBAAiB,mBAAmB,CAAC;IACnC,SAAS;AACX;uCAEe", "debugId": null}}, {"offset": {"line": 63, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/templete/pdf-exam-generator/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';\nimport {\n  User as FirebaseUser,\n  signInWithEmailAndPassword,\n  createUserWithEmailAndPassword,\n  signOut,\n  onAuthStateChanged,\n  signInWithPopup,\n  sendPasswordResetEmail,\n  updateProfile,\n  GoogleAuthProvider,\n  FacebookAuthProvider\n} from 'firebase/auth';\nimport { doc, setDoc, getDoc } from 'firebase/firestore';\nimport { auth, db, googleProvider, facebookProvider } from '@/lib/firebase';\n\ninterface UserProfile {\n  uid: string;\n  email: string;\n  displayName: string;\n  photoURL?: string;\n  role: 'teacher' | 'student' | 'admin';\n  createdAt: Date;\n  lastLoginAt: Date;\n  preferences: {\n    language: 'ar' | 'en';\n    theme: 'light' | 'dark';\n    notifications: boolean;\n  };\n  stats: {\n    totalExams: number;\n    averageScore: number;\n    totalQuestions: number;\n    studyTime: number; // in minutes\n  };\n}\n\ninterface AuthContextType {\n  user: FirebaseUser | null;\n  userProfile: UserProfile | null;\n  loading: boolean;\n  isAuthenticated: boolean;\n  signIn: (email: string, password: string) => Promise<void>;\n  signUp: (email: string, password: string, displayName: string, role: 'teacher' | 'student') => Promise<void>;\n  signInWithGoogle: () => Promise<void>;\n  signInWithFacebook: () => Promise<void>;\n  logout: () => Promise<void>;\n  resetPassword: (email: string) => Promise<void>;\n  updateUserProfile: (data: Partial<UserProfile>) => Promise<void>;\n  // Legacy compatibility\n  login: (email: string, password: string) => Promise<boolean>;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {\n  const [user, setUser] = useState<FirebaseUser | null>(null);\n  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);\n  const [loading, setLoading] = useState(true);\n\n  // Create or update user profile in Firestore\n  const createUserProfile = async (user: FirebaseUser, additionalData: any = {}) => {\n    if (!user) return;\n\n    const userRef = doc(db, 'users', user.uid);\n    const userSnap = await getDoc(userRef);\n\n    if (!userSnap.exists()) {\n      const { displayName, email, photoURL } = user;\n      const createdAt = new Date();\n\n      const defaultProfile: UserProfile = {\n        uid: user.uid,\n        email: email || '',\n        displayName: displayName || '',\n        photoURL: photoURL || undefined,\n        role: additionalData.role || 'student',\n        createdAt,\n        lastLoginAt: createdAt,\n        preferences: {\n          language: 'ar',\n          theme: 'light',\n          notifications: true\n        },\n        stats: {\n          totalExams: 0,\n          averageScore: 0,\n          totalQuestions: 0,\n          studyTime: 0\n        },\n        ...additionalData\n      };\n\n      try {\n        await setDoc(userRef, defaultProfile);\n        setUserProfile(defaultProfile);\n      } catch (error) {\n        console.error('Error creating user profile:', error);\n      }\n    } else {\n      // Update last login time\n      const existingProfile = userSnap.data() as UserProfile;\n      const updatedProfile = {\n        ...existingProfile,\n        lastLoginAt: new Date()\n      };\n\n      await setDoc(userRef, updatedProfile, { merge: true });\n      setUserProfile(updatedProfile);\n    }\n  };\n\n  // Sign in with email and password\n  const signIn = async (email: string, password: string) => {\n    setLoading(true);\n    try {\n      const result = await signInWithEmailAndPassword(auth, email, password);\n      await createUserProfile(result.user);\n    } catch (error) {\n      console.error('Error signing in:', error);\n      throw error;\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Legacy login function for compatibility\n  const login = async (email: string, password: string): Promise<boolean> => {\n    try {\n      await signIn(email, password);\n      return true;\n    } catch (error) {\n      return false;\n    }\n  };\n\n  // Sign up with email and password\n  const signUp = async (email: string, password: string, displayName: string, role: 'teacher' | 'student') => {\n    setLoading(true);\n    try {\n      const result = await createUserWithEmailAndPassword(auth, email, password);\n\n      // Update display name\n      await updateProfile(result.user, { displayName });\n\n      // Create user profile\n      await createUserProfile(result.user, { role, displayName });\n    } catch (error) {\n      console.error('Error signing up:', error);\n      throw error;\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Sign in with Google\n  const signInWithGoogle = async () => {\n    setLoading(true);\n    try {\n      const result = await signInWithPopup(auth, googleProvider);\n      await createUserProfile(result.user);\n    } catch (error) {\n      console.error('Error signing in with Google:', error);\n      throw error;\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Sign in with Facebook\n  const signInWithFacebook = async () => {\n    setLoading(true);\n    try {\n      const result = await signInWithPopup(auth, facebookProvider);\n      await createUserProfile(result.user);\n    } catch (error) {\n      console.error('Error signing in with Facebook:', error);\n      throw error;\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Logout\n  const logout = async () => {\n    setLoading(true);\n    try {\n      await signOut(auth);\n      setUser(null);\n      setUserProfile(null);\n      // Clear all exam data\n      localStorage.removeItem('examResults');\n      localStorage.removeItem('examAnswers');\n      localStorage.removeItem('uploadedPDF');\n      localStorage.removeItem('examConfig');\n    } catch (error) {\n      console.error('Error signing out:', error);\n      throw error;\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Reset password\n  const resetPassword = async (email: string) => {\n    try {\n      await sendPasswordResetEmail(auth, email);\n    } catch (error) {\n      console.error('Error sending password reset email:', error);\n      throw error;\n    }\n  };\n\n  // Update user profile\n  const updateUserProfile = async (data: Partial<UserProfile>) => {\n    if (!user) return;\n\n    try {\n      const userRef = doc(db, 'users', user.uid);\n      await setDoc(userRef, data, { merge: true });\n\n      if (userProfile) {\n        setUserProfile({ ...userProfile, ...data });\n      }\n    } catch (error) {\n      console.error('Error updating user profile:', error);\n      throw error;\n    }\n  };\n\n  // Listen for authentication state changes\n  useEffect(() => {\n    const unsubscribe = onAuthStateChanged(auth, async (user) => {\n      if (user) {\n        setUser(user);\n        await createUserProfile(user);\n      } else {\n        setUser(null);\n        setUserProfile(null);\n      }\n      setLoading(false);\n    });\n\n    return unsubscribe;\n  }, []);\n\n  const value: AuthContextType = {\n    user,\n    userProfile,\n    loading,\n    isAuthenticated: !!user,\n    signIn,\n    signUp,\n    signInWithGoogle,\n    signInWithFacebook,\n    logout,\n    resetPassword,\n    updateUserProfile,\n    login // Legacy compatibility\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AAAA;AACA;;;AAhBA;;;;;AAuDA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,MAAM,UAAU;;IACrB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;GANa;AAQN,MAAM,eAAkD,CAAC,EAAE,QAAQ,EAAE;;IAC1E,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;IACtD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IACnE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,6CAA6C;IAC7C,MAAM,oBAAoB,OAAO,MAAoB,iBAAsB,CAAC,CAAC;QAC3E,IAAI,CAAC,MAAM;QAEX,MAAM,UAAU,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,SAAS,KAAK,GAAG;QACzC,MAAM,WAAW,MAAM,CAAA,GAAA,sKAAA,CAAA,SAAM,AAAD,EAAE;QAE9B,IAAI,CAAC,SAAS,MAAM,IAAI;YACtB,MAAM,EAAE,WAAW,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG;YACzC,MAAM,YAAY,IAAI;YAEtB,MAAM,iBAA8B;gBAClC,KAAK,KAAK,GAAG;gBACb,OAAO,SAAS;gBAChB,aAAa,eAAe;gBAC5B,UAAU,YAAY;gBACtB,MAAM,eAAe,IAAI,IAAI;gBAC7B;gBACA,aAAa;gBACb,aAAa;oBACX,UAAU;oBACV,OAAO;oBACP,eAAe;gBACjB;gBACA,OAAO;oBACL,YAAY;oBACZ,cAAc;oBACd,gBAAgB;oBAChB,WAAW;gBACb;gBACA,GAAG,cAAc;YACnB;YAEA,IAAI;gBACF,MAAM,CAAA,GAAA,sKAAA,CAAA,SAAM,AAAD,EAAE,SAAS;gBACtB,eAAe;YACjB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,gCAAgC;YAChD;QACF,OAAO;YACL,yBAAyB;YACzB,MAAM,kBAAkB,SAAS,IAAI;YACrC,MAAM,iBAAiB;gBACrB,GAAG,eAAe;gBAClB,aAAa,IAAI;YACnB;YAEA,MAAM,CAAA,GAAA,sKAAA,CAAA,SAAM,AAAD,EAAE,SAAS,gBAAgB;gBAAE,OAAO;YAAK;YACpD,eAAe;QACjB;IACF;IAEA,kCAAkC;IAClC,MAAM,SAAS,OAAO,OAAe;QACnC,WAAW;QACX,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,iOAAA,CAAA,6BAA0B,AAAD,EAAE,yHAAA,CAAA,OAAI,EAAE,OAAO;YAC7D,MAAM,kBAAkB,OAAO,IAAI;QACrC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,0CAA0C;IAC1C,MAAM,QAAQ,OAAO,OAAe;QAClC,IAAI;YACF,MAAM,OAAO,OAAO;YACpB,OAAO;QACT,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;IAEA,kCAAkC;IAClC,MAAM,SAAS,OAAO,OAAe,UAAkB,aAAqB;QAC1E,WAAW;QACX,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,qOAAA,CAAA,iCAA8B,AAAD,EAAE,yHAAA,CAAA,OAAI,EAAE,OAAO;YAEjE,sBAAsB;YACtB,MAAM,CAAA,GAAA,oNAAA,CAAA,gBAAa,AAAD,EAAE,OAAO,IAAI,EAAE;gBAAE;YAAY;YAE/C,sBAAsB;YACtB,MAAM,kBAAkB,OAAO,IAAI,EAAE;gBAAE;gBAAM;YAAY;QAC3D,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,sBAAsB;IACtB,MAAM,mBAAmB;QACvB,WAAW;QACX,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,qNAAA,CAAA,kBAAe,AAAD,EAAE,yHAAA,CAAA,OAAI,EAAE,yHAAA,CAAA,iBAAc;YACzD,MAAM,kBAAkB,OAAO,IAAI;QACrC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,wBAAwB;IACxB,MAAM,qBAAqB;QACzB,WAAW;QACX,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,qNAAA,CAAA,kBAAe,AAAD,EAAE,yHAAA,CAAA,OAAI,EAAE,yHAAA,CAAA,mBAAgB;YAC3D,MAAM,kBAAkB,OAAO,IAAI;QACrC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,SAAS;IACT,MAAM,SAAS;QACb,WAAW;QACX,IAAI;YACF,MAAM,CAAA,GAAA,6MAAA,CAAA,UAAO,AAAD,EAAE,yHAAA,CAAA,OAAI;YAClB,QAAQ;YACR,eAAe;YACf,sBAAsB;YACtB,aAAa,UAAU,CAAC;YACxB,aAAa,UAAU,CAAC;YACxB,aAAa,UAAU,CAAC;YACxB,aAAa,UAAU,CAAC;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;YACpC,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,iBAAiB;IACjB,MAAM,gBAAgB,OAAO;QAC3B,IAAI;YACF,MAAM,CAAA,GAAA,6NAAA,CAAA,yBAAsB,AAAD,EAAE,yHAAA,CAAA,OAAI,EAAE;QACrC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;YACrD,MAAM;QACR;IACF;IAEA,sBAAsB;IACtB,MAAM,oBAAoB,OAAO;QAC/B,IAAI,CAAC,MAAM;QAEX,IAAI;YACF,MAAM,UAAU,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,SAAS,KAAK,GAAG;YACzC,MAAM,CAAA,GAAA,sKAAA,CAAA,SAAM,AAAD,EAAE,SAAS,MAAM;gBAAE,OAAO;YAAK;YAE1C,IAAI,aAAa;gBACf,eAAe;oBAAE,GAAG,WAAW;oBAAE,GAAG,IAAI;gBAAC;YAC3C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,MAAM;QACR;IACF;IAEA,0CAA0C;IAC1C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM,cAAc,CAAA,GAAA,wNAAA,CAAA,qBAAkB,AAAD,EAAE,yHAAA,CAAA,OAAI;sDAAE,OAAO;oBAClD,IAAI,MAAM;wBACR,QAAQ;wBACR,MAAM,kBAAkB;oBAC1B,OAAO;wBACL,QAAQ;wBACR,eAAe;oBACjB;oBACA,WAAW;gBACb;;YAEA,OAAO;QACT;iCAAG,EAAE;IAEL,MAAM,QAAyB;QAC7B;QACA;QACA;QACA,iBAAiB,CAAC,CAAC;QACnB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;IAnNa;KAAA", "debugId": null}}, {"offset": {"line": 320, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/templete/pdf-exam-generator/src/contexts/LanguageContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useState, useEffect } from 'react';\n\ntype Language = 'ar' | 'en';\ntype Theme = 'light' | 'dark';\n\ninterface LanguageContextType {\n  language: Language;\n  theme: Theme;\n  setLanguage: (lang: Language) => void;\n  setTheme: (theme: Theme) => void;\n  t: (key: string) => string;\n}\n\nconst LanguageContext = createContext<LanguageContextType | undefined>(undefined);\n\nconst translations = {\n  ar: {\n    // Navigation\n    'nav.features': 'المميزات',\n    'nav.pricing': 'الأسعار',\n    'nav.about': 'حولنا',\n    'nav.contact': 'اتصل بنا',\n    'nav.login': 'تسجيل الدخول',\n    'nav.signup': 'إنشاء حساب',\n    \n    // Hero Section\n    'hero.title': 'حول ملفات PDF إلى اختبارات تفاعلية',\n    'hero.subtitle': 'في دقائق معدودة',\n    'hero.description': 'استخدم الذكاء الاصطناعي لتحويل أي ملف PDF إلى اختبار تفاعلي مع تحليلات متقدمة وتقييم تلقائي. مثالي للمعلمين والطلاب ومنصات التعلم الإلكتروني.',\n    'hero.cta.primary': 'ابدأ مجاناً',\n    'hero.cta.secondary': 'شاهد العرض التوضيحي',\n    'hero.trusted': 'يثق به أكثر من 10,000 معلم حول العالم',\n    \n    // Features\n    'features.title': 'مميزات قوية لتعليم أفضل',\n    'features.subtitle': 'كل ما تحتاجه لإنشاء اختبارات احترافية وتحليل النتائج',\n    \n    'feature.ai.title': 'ذكاء اصطناعي متقدم',\n    'feature.ai.description': 'يحلل محتوى PDF ويولد أسئلة متنوعة تلقائياً',\n    \n    'feature.interactive.title': 'اختبارات تفاعلية',\n    'feature.interactive.description': 'أسئلة متعددة الخيارات، صح/خطأ، وأسئلة مفتوحة',\n    \n    'feature.analytics.title': 'تحليلات شاملة',\n    'feature.analytics.description': 'تقارير مفصلة عن أداء الطلاب ونقاط القوة والضعف',\n    \n    'feature.realtime.title': 'نتائج فورية',\n    'feature.realtime.description': 'تصحيح تلقائي وتقييم فوري مع ملاحظات مفصلة',\n    \n    'feature.multilang.title': 'دعم متعدد اللغات',\n    'feature.multilang.description': 'يدعم العربية والإنجليزية مع واجهة سهلة الاستخدام',\n    \n    'feature.secure.title': 'آمن وموثوق',\n    'feature.secure.description': 'حماية عالية للبيانات مع نسخ احتياطية تلقائية',\n    \n    // Pricing\n    'pricing.title': 'خطط تناسب احتياجاتك',\n    'pricing.subtitle': 'ابدأ مجاناً وارتقِ حسب نموك',\n    'pricing.monthly': 'شهري',\n    'pricing.yearly': 'سنوي',\n    'pricing.save': 'وفر 20%',\n    'pricing.popular': 'الأكثر شعبية',\n    'pricing.questions': 'لديك أسئلة؟',\n    'pricing.contact_text': 'فريقنا هنا لمساعدتك في اختيار الخطة المناسبة',\n    'pricing.contact_us': 'تواصل معنا',\n    \n    'plan.free.name': 'مجاني',\n    'plan.free.price': '0',\n    'plan.free.period': '/شهر',\n    'plan.free.description': 'مثالي للمعلمين المبتدئين',\n    \n    'plan.pro.name': 'احترافي',\n    'plan.pro.price': '19',\n    'plan.pro.period': '/شهر',\n    'plan.pro.description': 'للمعلمين النشطين',\n    \n    'plan.enterprise.name': 'مؤسسات',\n    'plan.enterprise.price': '99',\n    'plan.enterprise.period': '/شهر',\n    'plan.enterprise.description': 'للمدارس والجامعات',\n    \n    'pricing.cta': 'ابدأ الآن',\n    'pricing.contact': 'تواصل معنا',\n    \n    // Footer\n    'footer.product': 'المنتج',\n    'footer.company': 'الشركة',\n    'footer.support': 'الدعم',\n    'footer.legal': 'قانوني',\n    'footer.rights': 'جميع الحقوق محفوظة',\n    \n    // Common\n    'common.loading': 'جاري التحميل...',\n    'common.error': 'حدث خطأ',\n    'common.success': 'تم بنجاح',\n    'common.cancel': 'إلغاء',\n    'common.save': 'حفظ',\n    'common.edit': 'تعديل',\n    'common.delete': 'حذف',\n    'common.view': 'عرض',\n\n    // Authentication\n    'auth.login': 'تسجيل الدخول',\n    'auth.register': 'إنشاء حساب',\n    'auth.email': 'البريد الإلكتروني',\n    'auth.password': 'كلمة المرور',\n    'auth.confirmPassword': 'تأكيد كلمة المرور',\n    'auth.fullName': 'الاسم الكامل',\n    'auth.role': 'الدور',\n    'auth.teacher': 'معلم',\n    'auth.student': 'طالب',\n    'auth.admin': 'مدير',\n    'auth.forgotPassword': 'نسيت كلمة المرور؟',\n    'auth.rememberMe': 'تذكرني',\n    'auth.signInWith': 'تسجيل الدخول باستخدام',\n    'auth.google': 'جوجل',\n    'auth.facebook': 'فيسبوك',\n    'auth.alreadyHaveAccount': 'لديك حساب بالفعل؟',\n    'auth.dontHaveAccount': 'ليس لديك حساب؟',\n    'auth.createAccount': 'إنشاء حساب',\n    'auth.signIn': 'تسجيل الدخول',\n    'auth.logout': 'تسجيل الخروج',\n\n    // Dashboard\n    'dashboard.title': 'لوحة التحكم',\n    'dashboard.welcome': 'مرحباً',\n    'dashboard.overview': 'إليك نظرة عامة على نشاطك وإنجازاتك الأخيرة',\n    'dashboard.totalExams': 'إجمالي الاختبارات',\n    'dashboard.averageScore': 'متوسط النتائج',\n    'dashboard.totalQuestions': 'إجمالي الأسئلة',\n    'dashboard.studyHours': 'ساعات الدراسة',\n    'dashboard.recentExams': 'الاختبارات الأخيرة',\n    'dashboard.quickActions': 'إجراءات سريعة',\n    'dashboard.createExam': 'إنشاء اختبار جديد',\n    'dashboard.viewAnalytics': 'عرض التحليلات',\n    'dashboard.manageStudents': 'إدارة الطلاب',\n    'dashboard.newAchievement': 'إنجاز جديد!',\n    'dashboard.congratulations': 'تهانينا! لقد حققت إنجازاً رائعاً في رحلتك التعليمية',\n\n    // Navigation\n    'nav.dashboard': 'لوحة التحكم',\n    'nav.upload': 'رفع ملف جديد',\n    'nav.exams': 'الاختبارات',\n    'nav.analytics': 'التحليلات',\n    'nav.history': 'السجل',\n    'nav.students': 'الطلاب',\n    'nav.profile': 'الملف الشخصي',\n    'nav.settings': 'الإعدادات',\n  },\n  en: {\n    // Navigation\n    'nav.features': 'Features',\n    'nav.pricing': 'Pricing',\n    'nav.about': 'About',\n    'nav.contact': 'Contact',\n    'nav.login': 'Login',\n    'nav.signup': 'Sign Up',\n    \n    // Hero Section\n    'hero.title': 'Transform PDFs into Interactive Exams',\n    'hero.subtitle': 'in Minutes',\n    'hero.description': 'Use AI to convert any PDF into interactive exams with advanced analytics and automatic grading. Perfect for teachers, students, and e-learning platforms.',\n    'hero.cta.primary': 'Get Started Free',\n    'hero.cta.secondary': 'Watch Demo',\n    'hero.trusted': 'Trusted by 10,000+ educators worldwide',\n    \n    // Features\n    'features.title': 'Powerful Features for Better Learning',\n    'features.subtitle': 'Everything you need to create professional exams and analyze results',\n    \n    'feature.ai.title': 'Advanced AI',\n    'feature.ai.description': 'Analyzes PDF content and generates diverse questions automatically',\n    \n    'feature.interactive.title': 'Interactive Exams',\n    'feature.interactive.description': 'Multiple choice, true/false, and open-ended questions',\n    \n    'feature.analytics.title': 'Comprehensive Analytics',\n    'feature.analytics.description': 'Detailed reports on student performance and learning gaps',\n    \n    'feature.realtime.title': 'Real-time Results',\n    'feature.realtime.description': 'Automatic grading and instant feedback with detailed explanations',\n    \n    'feature.multilang.title': 'Multi-language Support',\n    'feature.multilang.description': 'Supports Arabic and English with intuitive interface',\n    \n    'feature.secure.title': 'Secure & Reliable',\n    'feature.secure.description': 'Enterprise-grade security with automatic backups',\n    \n    // Pricing\n    'pricing.title': 'Plans That Fit Your Needs',\n    'pricing.subtitle': 'Start free and scale as you grow',\n    'pricing.monthly': 'Monthly',\n    'pricing.yearly': 'Yearly',\n    'pricing.save': 'Save 20%',\n    'pricing.popular': 'Most Popular',\n    'pricing.questions': 'Have Questions?',\n    'pricing.contact_text': 'Our team is here to help you choose the right plan',\n    'pricing.contact_us': 'Contact Us',\n    \n    'plan.free.name': 'Free',\n    'plan.free.price': '0',\n    'plan.free.period': '/month',\n    'plan.free.description': 'Perfect for getting started',\n    \n    'plan.pro.name': 'Professional',\n    'plan.pro.price': '19',\n    'plan.pro.period': '/month',\n    'plan.pro.description': 'For active educators',\n    \n    'plan.enterprise.name': 'Enterprise',\n    'plan.enterprise.price': '99',\n    'plan.enterprise.period': '/month',\n    'plan.enterprise.description': 'For schools and universities',\n    \n    'pricing.cta': 'Get Started',\n    'pricing.contact': 'Contact Sales',\n    \n    // Footer\n    'footer.product': 'Product',\n    'footer.company': 'Company',\n    'footer.support': 'Support',\n    'footer.legal': 'Legal',\n    'footer.rights': 'All rights reserved',\n    \n    // Common\n    'common.loading': 'Loading...',\n    'common.error': 'An error occurred',\n    'common.success': 'Success',\n    'common.cancel': 'Cancel',\n    'common.save': 'Save',\n    'common.edit': 'Edit',\n    'common.delete': 'Delete',\n    'common.view': 'View',\n\n    // Authentication\n    'auth.login': 'Login',\n    'auth.register': 'Sign Up',\n    'auth.email': 'Email',\n    'auth.password': 'Password',\n    'auth.confirmPassword': 'Confirm Password',\n    'auth.fullName': 'Full Name',\n    'auth.role': 'Role',\n    'auth.teacher': 'Teacher',\n    'auth.student': 'Student',\n    'auth.admin': 'Admin',\n    'auth.forgotPassword': 'Forgot Password?',\n    'auth.rememberMe': 'Remember Me',\n    'auth.signInWith': 'Sign in with',\n    'auth.google': 'Google',\n    'auth.facebook': 'Facebook',\n    'auth.alreadyHaveAccount': 'Already have an account?',\n    'auth.dontHaveAccount': \"Don't have an account?\",\n    'auth.createAccount': 'Create Account',\n    'auth.signIn': 'Sign In',\n    'auth.logout': 'Logout',\n\n    // Dashboard\n    'dashboard.title': 'Dashboard',\n    'dashboard.welcome': 'Welcome',\n    'dashboard.overview': \"Here's an overview of your recent activity and achievements\",\n    'dashboard.totalExams': 'Total Exams',\n    'dashboard.averageScore': 'Average Score',\n    'dashboard.totalQuestions': 'Total Questions',\n    'dashboard.studyHours': 'Study Hours',\n    'dashboard.recentExams': 'Recent Exams',\n    'dashboard.quickActions': 'Quick Actions',\n    'dashboard.createExam': 'Create New Exam',\n    'dashboard.viewAnalytics': 'View Analytics',\n    'dashboard.manageStudents': 'Manage Students',\n    'dashboard.newAchievement': 'New Achievement!',\n    'dashboard.congratulations': 'Congratulations! You have achieved a great milestone in your learning journey',\n\n    // Navigation\n    'nav.dashboard': 'Dashboard',\n    'nav.upload': 'Upload New File',\n    'nav.exams': 'Exams',\n    'nav.analytics': 'Analytics',\n    'nav.history': 'History',\n    'nav.students': 'Students',\n    'nav.profile': 'Profile',\n    'nav.settings': 'Settings',\n  }\n};\n\nexport function LanguageProvider({ children }: { children: React.ReactNode }) {\n  const [language, setLanguage] = useState<Language>('en');\n  const [theme, setTheme] = useState<Theme>('light');\n\n  useEffect(() => {\n    // Load saved preferences\n    const savedLanguage = localStorage.getItem('language') as Language;\n    const savedTheme = localStorage.getItem('theme') as Theme;\n    \n    if (savedLanguage) setLanguage(savedLanguage);\n    if (savedTheme) setTheme(savedTheme);\n    \n    // Apply theme to document\n    document.documentElement.classList.toggle('dark', savedTheme === 'dark');\n    document.documentElement.setAttribute('dir', savedLanguage === 'ar' ? 'rtl' : 'ltr');\n  }, []);\n\n  useEffect(() => {\n    // Save preferences and apply changes\n    localStorage.setItem('language', language);\n    localStorage.setItem('theme', theme);\n    \n    document.documentElement.classList.toggle('dark', theme === 'dark');\n    document.documentElement.setAttribute('dir', language === 'ar' ? 'rtl' : 'ltr');\n  }, [language, theme]);\n\n  const t = (key: string): string => {\n    return translations[language][key as keyof typeof translations[typeof language]] || key;\n  };\n\n  return (\n    <LanguageContext.Provider value={{\n      language,\n      theme,\n      setLanguage,\n      setTheme,\n      t\n    }}>\n      {children}\n    </LanguageContext.Provider>\n  );\n}\n\nexport function useLanguage() {\n  const context = useContext(LanguageContext);\n  if (context === undefined) {\n    throw new Error('useLanguage must be used within a LanguageProvider');\n  }\n  return context;\n}\n"], "names": [], "mappings": ";;;;;AAEA;;;AAFA;;AAeA,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAmC;AAEvE,MAAM,eAAe;IACnB,IAAI;QACF,aAAa;QACb,gBAAgB;QAChB,eAAe;QACf,aAAa;QACb,eAAe;QACf,aAAa;QACb,cAAc;QAEd,eAAe;QACf,cAAc;QACd,iBAAiB;QACjB,oBAAoB;QACpB,oBAAoB;QACpB,sBAAsB;QACtB,gBAAgB;QAEhB,WAAW;QACX,kBAAkB;QAClB,qBAAqB;QAErB,oBAAoB;QACpB,0BAA0B;QAE1B,6BAA6B;QAC7B,mCAAmC;QAEnC,2BAA2B;QAC3B,iCAAiC;QAEjC,0BAA0B;QAC1B,gCAAgC;QAEhC,2BAA2B;QAC3B,iCAAiC;QAEjC,wBAAwB;QACxB,8BAA8B;QAE9B,UAAU;QACV,iBAAiB;QACjB,oBAAoB;QACpB,mBAAmB;QACnB,kBAAkB;QAClB,gBAAgB;QAChB,mBAAmB;QACnB,qBAAqB;QACrB,wBAAwB;QACxB,sBAAsB;QAEtB,kBAAkB;QAClB,mBAAmB;QACnB,oBAAoB;QACpB,yBAAyB;QAEzB,iBAAiB;QACjB,kBAAkB;QAClB,mBAAmB;QACnB,wBAAwB;QAExB,wBAAwB;QACxB,yBAAyB;QACzB,0BAA0B;QAC1B,+BAA+B;QAE/B,eAAe;QACf,mBAAmB;QAEnB,SAAS;QACT,kBAAkB;QAClB,kBAAkB;QAClB,kBAAkB;QAClB,gBAAgB;QAChB,iBAAiB;QAEjB,SAAS;QACT,kBAAkB;QAClB,gBAAgB;QAChB,kBAAkB;QAClB,iBAAiB;QACjB,eAAe;QACf,eAAe;QACf,iBAAiB;QACjB,eAAe;QAEf,iBAAiB;QACjB,cAAc;QACd,iBAAiB;QACjB,cAAc;QACd,iBAAiB;QACjB,wBAAwB;QACxB,iBAAiB;QACjB,aAAa;QACb,gBAAgB;QAChB,gBAAgB;QAChB,cAAc;QACd,uBAAuB;QACvB,mBAAmB;QACnB,mBAAmB;QACnB,eAAe;QACf,iBAAiB;QACjB,2BAA2B;QAC3B,wBAAwB;QACxB,sBAAsB;QACtB,eAAe;QACf,eAAe;QAEf,YAAY;QACZ,mBAAmB;QACnB,qBAAqB;QACrB,sBAAsB;QACtB,wBAAwB;QACxB,0BAA0B;QAC1B,4BAA4B;QAC5B,wBAAwB;QACxB,yBAAyB;QACzB,0BAA0B;QAC1B,wBAAwB;QACxB,2BAA2B;QAC3B,4BAA4B;QAC5B,4BAA4B;QAC5B,6BAA6B;QAE7B,aAAa;QACb,iBAAiB;QACjB,cAAc;QACd,aAAa;QACb,iBAAiB;QACjB,eAAe;QACf,gBAAgB;QAChB,eAAe;QACf,gBAAgB;IAClB;IACA,IAAI;QACF,aAAa;QACb,gBAAgB;QAChB,eAAe;QACf,aAAa;QACb,eAAe;QACf,aAAa;QACb,cAAc;QAEd,eAAe;QACf,cAAc;QACd,iBAAiB;QACjB,oBAAoB;QACpB,oBAAoB;QACpB,sBAAsB;QACtB,gBAAgB;QAEhB,WAAW;QACX,kBAAkB;QAClB,qBAAqB;QAErB,oBAAoB;QACpB,0BAA0B;QAE1B,6BAA6B;QAC7B,mCAAmC;QAEnC,2BAA2B;QAC3B,iCAAiC;QAEjC,0BAA0B;QAC1B,gCAAgC;QAEhC,2BAA2B;QAC3B,iCAAiC;QAEjC,wBAAwB;QACxB,8BAA8B;QAE9B,UAAU;QACV,iBAAiB;QACjB,oBAAoB;QACpB,mBAAmB;QACnB,kBAAkB;QAClB,gBAAgB;QAChB,mBAAmB;QACnB,qBAAqB;QACrB,wBAAwB;QACxB,sBAAsB;QAEtB,kBAAkB;QAClB,mBAAmB;QACnB,oBAAoB;QACpB,yBAAyB;QAEzB,iBAAiB;QACjB,kBAAkB;QAClB,mBAAmB;QACnB,wBAAwB;QAExB,wBAAwB;QACxB,yBAAyB;QACzB,0BAA0B;QAC1B,+BAA+B;QAE/B,eAAe;QACf,mBAAmB;QAEnB,SAAS;QACT,kBAAkB;QAClB,kBAAkB;QAClB,kBAAkB;QAClB,gBAAgB;QAChB,iBAAiB;QAEjB,SAAS;QACT,kBAAkB;QAClB,gBAAgB;QAChB,kBAAkB;QAClB,iBAAiB;QACjB,eAAe;QACf,eAAe;QACf,iBAAiB;QACjB,eAAe;QAEf,iBAAiB;QACjB,cAAc;QACd,iBAAiB;QACjB,cAAc;QACd,iBAAiB;QACjB,wBAAwB;QACxB,iBAAiB;QACjB,aAAa;QACb,gBAAgB;QAChB,gBAAgB;QAChB,cAAc;QACd,uBAAuB;QACvB,mBAAmB;QACnB,mBAAmB;QACnB,eAAe;QACf,iBAAiB;QACjB,2BAA2B;QAC3B,wBAAwB;QACxB,sBAAsB;QACtB,eAAe;QACf,eAAe;QAEf,YAAY;QACZ,mBAAmB;QACnB,qBAAqB;QACrB,sBAAsB;QACtB,wBAAwB;QACxB,0BAA0B;QAC1B,4BAA4B;QAC5B,wBAAwB;QACxB,yBAAyB;QACzB,0BAA0B;QAC1B,wBAAwB;QACxB,2BAA2B;QAC3B,4BAA4B;QAC5B,4BAA4B;QAC5B,6BAA6B;QAE7B,aAAa;QACb,iBAAiB;QACjB,cAAc;QACd,aAAa;QACb,iBAAiB;QACjB,eAAe;QACf,gBAAgB;QAChB,eAAe;QACf,gBAAgB;IAClB;AACF;AAEO,SAAS,iBAAiB,EAAE,QAAQ,EAAiC;;IAC1E,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;IACnD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS;IAE1C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,yBAAyB;YACzB,MAAM,gBAAgB,aAAa,OAAO,CAAC;YAC3C,MAAM,aAAa,aAAa,OAAO,CAAC;YAExC,IAAI,eAAe,YAAY;YAC/B,IAAI,YAAY,SAAS;YAEzB,0BAA0B;YAC1B,SAAS,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,eAAe;YACjE,SAAS,eAAe,CAAC,YAAY,CAAC,OAAO,kBAAkB,OAAO,QAAQ;QAChF;qCAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,qCAAqC;YACrC,aAAa,OAAO,CAAC,YAAY;YACjC,aAAa,OAAO,CAAC,SAAS;YAE9B,SAAS,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,UAAU;YAC5D,SAAS,eAAe,CAAC,YAAY,CAAC,OAAO,aAAa,OAAO,QAAQ;QAC3E;qCAAG;QAAC;QAAU;KAAM;IAEpB,MAAM,IAAI,CAAC;QACT,OAAO,YAAY,CAAC,SAAS,CAAC,IAAkD,IAAI;IACtF;IAEA,qBACE,6LAAC,gBAAgB,QAAQ;QAAC,OAAO;YAC/B;YACA;YACA;YACA;YACA;QACF;kBACG;;;;;;AAGP;GAzCgB;KAAA;AA2CT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB", "debugId": null}}, {"offset": {"line": 631, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/templete/pdf-exam-generator/src/components/layout/Navbar.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { useState } from 'react';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { Menu, X, FileText, Settings, Home, BarChart3, User, LogOut, BookOpen } from 'lucide-react';\n\nexport function Navbar() {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [showUserMenu, setShowUserMenu] = useState(false);\n  const pathname = usePathname();\n  const { user, logout } = useAuth();\n\n  const navigation = user ? [\n    { name: 'لوحة التحكم', href: '/dashboard', icon: Home },\n    { name: 'رفع ملف', href: '/dashboard/upload', icon: FileText },\n    { name: 'السجل', href: '/dashboard/history', icon: BarChart3 },\n    { name: 'الإعدادات', href: '/settings', icon: Settings },\n  ] : [\n    { name: 'الرئيسية', href: '/', icon: Home },\n  ];\n\n  const handleLogout = () => {\n    logout();\n    setShowUserMenu(false);\n  };\n\n  const isActive = (href: string) => {\n    if (href === '/') {\n      return pathname === '/';\n    }\n    return pathname.startsWith(href);\n  };\n\n  return (\n    <nav className=\"bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between h-16\">\n          {/* Logo and brand */}\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"flex items-center space-x-2 rtl:space-x-reverse\">\n              <div className=\"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n                <BookOpen className=\"w-5 h-5 text-white\" />\n              </div>\n              <span className=\"text-xl font-bold text-gray-900 hidden sm:block\">\n                منصة الاختبارات التفاعلية\n              </span>\n              <span className=\"text-xl font-bold text-gray-900 sm:hidden\">\n                الاختبارات\n              </span>\n            </Link>\n          </div>\n\n          {/* Desktop navigation */}\n          <div className=\"hidden md:flex items-center space-x-8 rtl:space-x-reverse\">\n            {navigation.map((item) => {\n              const Icon = item.icon;\n              return (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className={`flex items-center space-x-1 rtl:space-x-reverse px-3 py-2 rounded-md text-sm font-medium transition-colors ${\n                    isActive(item.href)\n                      ? 'text-blue-600 bg-blue-50'\n                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'\n                  }`}\n                >\n                  <Icon className=\"w-4 h-4\" />\n                  <span>{item.name}</span>\n                </Link>\n              );\n            })}\n\n            {/* User Menu */}\n            {user ? (\n              <div className=\"relative\">\n                <button\n                  onClick={() => setShowUserMenu(!showUserMenu)}\n                  className=\"flex items-center space-x-2 rtl:space-x-reverse px-3 py-2 rounded-md text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50\"\n                >\n                  <div className=\"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center\">\n                    {user.photoURL ? (\n                      <img src={user.photoURL} alt={user?.displayName || 'User'} className=\"w-8 h-8 rounded-full object-cover\" />\n                    ) : (\n                      <User className=\"w-4 h-4 text-white\" />\n                    )}\n                  </div>\n                  <span>{user?.displayName || user?.email?.split('@')[0] || 'المستخدم'}</span>\n                </button>\n\n                {showUserMenu && (\n                  <div className=\"absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-50\">\n                    <div className=\"py-1\">\n                      <Link\n                        href=\"/dashboard\"\n                        className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50\"\n                        onClick={() => setShowUserMenu(false)}\n                      >\n                        لوحة التحكم\n                      </Link>\n                      <Link\n                        href=\"/settings\"\n                        className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50\"\n                        onClick={() => setShowUserMenu(false)}\n                      >\n                        الإعدادات\n                      </Link>\n                      <button\n                        onClick={handleLogout}\n                        className=\"w-full text-right block px-4 py-2 text-sm text-red-700 hover:bg-red-50\"\n                      >\n                        تسجيل الخروج\n                      </button>\n                    </div>\n                  </div>\n                )}\n              </div>\n            ) : (\n              <Link\n                href=\"/login\"\n                className=\"bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 transition-colors\"\n              >\n                تسجيل الدخول\n              </Link>\n            )}\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden flex items-center\">\n            <button\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n              className=\"p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              aria-label=\"Toggle menu\"\n            >\n              {isMenuOpen ? (\n                <X className=\"w-6 h-6\" />\n              ) : (\n                <Menu className=\"w-6 h-6\" />\n              )}\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile menu */}\n      {isMenuOpen && (\n        <div className=\"md:hidden border-t border-gray-200 bg-white\">\n          <div className=\"px-2 pt-2 pb-3 space-y-1\">\n            {navigation.map((item) => {\n              const Icon = item.icon;\n              return (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  onClick={() => setIsMenuOpen(false)}\n                  className={`flex items-center space-x-2 rtl:space-x-reverse px-3 py-2 rounded-md text-base font-medium transition-colors ${\n                    isActive(item.href)\n                      ? 'text-blue-600 bg-blue-50'\n                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'\n                  }`}\n                >\n                  <Icon className=\"w-5 h-5\" />\n                  <span>{item.name}</span>\n                </Link>\n              );\n            })}\n\n            {/* Mobile User Menu */}\n            {user ? (\n              <div className=\"border-t border-gray-200 pt-3 mt-3\">\n                <div className=\"flex items-center px-3 py-2 mb-2\">\n                  <div className=\"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center ml-3\">\n                    {user.photoURL ? (\n                      <img src={user.photoURL} alt={user?.displayName || 'User'} className=\"w-8 h-8 rounded-full object-cover\" />\n                    ) : (\n                      <User className=\"w-4 h-4 text-white\" />\n                    )}\n                  </div>\n                  <div>\n                    <div className=\"text-sm font-medium text-gray-900\">{user?.displayName || user?.email?.split('@')[0] || 'المستخدم'}</div>\n                    <div className=\"text-xs text-gray-500 capitalize\">مستخدم</div>\n                  </div>\n                </div>\n                <Link\n                  href=\"/dashboard\"\n                  onClick={() => setIsMenuOpen(false)}\n                  className=\"flex items-center space-x-2 rtl:space-x-reverse px-3 py-2 rounded-md text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50\"\n                >\n                  <BarChart3 className=\"w-5 h-5\" />\n                  <span>لوحة التحكم</span>\n                </Link>\n                <button\n                  onClick={() => {\n                    handleLogout();\n                    setIsMenuOpen(false);\n                  }}\n                  className=\"w-full flex items-center space-x-2 rtl:space-x-reverse px-3 py-2 rounded-md text-base font-medium text-red-600 hover:text-red-900 hover:bg-red-50\"\n                >\n                  <LogOut className=\"w-5 h-5\" />\n                  <span>تسجيل الخروج</span>\n                </button>\n              </div>\n            ) : (\n              <div className=\"border-t border-gray-200 pt-3 mt-3\">\n                <Link\n                  href=\"/login\"\n                  onClick={() => setIsMenuOpen(false)}\n                  className=\"block px-3 py-2 rounded-md text-base font-medium bg-blue-600 text-white hover:bg-blue-700 text-center\"\n                >\n                  تسجيل الدخول\n                </Link>\n              </div>\n            )}\n          </div>\n        </div>\n      )}\n    </nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AANA;;;;;;AAQO,SAAS;;IACd,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAE/B,MAAM,aAAa,OAAO;QACxB;YAAE,MAAM;YAAe,MAAM;YAAc,MAAM,sMAAA,CAAA,OAAI;QAAC;QACtD;YAAE,MAAM;YAAW,MAAM;YAAqB,MAAM,iNAAA,CAAA,WAAQ;QAAC;QAC7D;YAAE,MAAM;YAAS,MAAM;YAAsB,MAAM,qNAAA,CAAA,YAAS;QAAC;QAC7D;YAAE,MAAM;YAAa,MAAM;YAAa,MAAM,6MAAA,CAAA,WAAQ;QAAC;KACxD,GAAG;QACF;YAAE,MAAM;YAAY,MAAM;YAAK,MAAM,sMAAA,CAAA,OAAI;QAAC;KAC3C;IAED,MAAM,eAAe;QACnB;QACA,gBAAgB;IAClB;IAEA,MAAM,WAAW,CAAC;QAChB,IAAI,SAAS,KAAK;YAChB,OAAO,aAAa;QACtB;QACA,OAAO,SAAS,UAAU,CAAC;IAC7B;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,iNAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAEtB,6LAAC;wCAAK,WAAU;kDAAkD;;;;;;kDAGlE,6LAAC;wCAAK,WAAU;kDAA4C;;;;;;;;;;;;;;;;;sCAOhE,6LAAC;4BAAI,WAAU;;gCACZ,WAAW,GAAG,CAAC,CAAC;oCACf,MAAM,OAAO,KAAK,IAAI;oCACtB,qBACE,6LAAC,+JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAW,CAAC,2GAA2G,EACrH,SAAS,KAAK,IAAI,IACd,6BACA,sDACJ;;0DAEF,6LAAC;gDAAK,WAAU;;;;;;0DAChB,6LAAC;0DAAM,KAAK,IAAI;;;;;;;uCATX,KAAK,IAAI;;;;;gCAYpB;gCAGC,qBACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,SAAS,IAAM,gBAAgB,CAAC;4CAChC,WAAU;;8DAEV,6LAAC;oDAAI,WAAU;8DACZ,KAAK,QAAQ,iBACZ,6LAAC;wDAAI,KAAK,KAAK,QAAQ;wDAAE,KAAK,MAAM,eAAe;wDAAQ,WAAU;;;;;6EAErE,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;8DAGpB,6LAAC;8DAAM,MAAM,eAAe,MAAM,OAAO,MAAM,IAAI,CAAC,EAAE,IAAI;;;;;;;;;;;;wCAG3D,8BACC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,+JAAA,CAAA,UAAI;wDACH,MAAK;wDACL,WAAU;wDACV,SAAS,IAAM,gBAAgB;kEAChC;;;;;;kEAGD,6LAAC,+JAAA,CAAA,UAAI;wDACH,MAAK;wDACL,WAAU;wDACV,SAAS,IAAM,gBAAgB;kEAChC;;;;;;kEAGD,6LAAC;wDACC,SAAS;wDACT,WAAU;kEACX;;;;;;;;;;;;;;;;;;;;;;yDAQT,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;sCAOL,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,SAAS,IAAM,cAAc,CAAC;gCAC9B,WAAU;gCACV,cAAW;0CAEV,2BACC,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;yDAEb,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQzB,4BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;wBACZ,WAAW,GAAG,CAAC,CAAC;4BACf,MAAM,OAAO,KAAK,IAAI;4BACtB,qBACE,6LAAC,+JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,SAAS,IAAM,cAAc;gCAC7B,WAAW,CAAC,6GAA6G,EACvH,SAAS,KAAK,IAAI,IACd,6BACA,sDACJ;;kDAEF,6LAAC;wCAAK,WAAU;;;;;;kDAChB,6LAAC;kDAAM,KAAK,IAAI;;;;;;;+BAVX,KAAK,IAAI;;;;;wBAapB;wBAGC,qBACC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACZ,KAAK,QAAQ,iBACZ,6LAAC;gDAAI,KAAK,KAAK,QAAQ;gDAAE,KAAK,MAAM,eAAe;gDAAQ,WAAU;;;;;qEAErE,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAGpB,6LAAC;;8DACC,6LAAC;oDAAI,WAAU;8DAAqC,MAAM,eAAe,MAAM,OAAO,MAAM,IAAI,CAAC,EAAE,IAAI;;;;;;8DACvG,6LAAC;oDAAI,WAAU;8DAAmC;;;;;;;;;;;;;;;;;;8CAGtD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,SAAS,IAAM,cAAc;oCAC7B,WAAU;;sDAEV,6LAAC,qNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;sDACrB,6LAAC;sDAAK;;;;;;;;;;;;8CAER,6LAAC;oCACC,SAAS;wCACP;wCACA,cAAc;oCAChB;oCACA,WAAU;;sDAEV,6LAAC,6MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC;sDAAK;;;;;;;;;;;;;;;;;iDAIV,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,SAAS,IAAM,cAAc;gCAC7B,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB;GAnNgB;;QAGG,qIAAA,CAAA,cAAW;QACH,kIAAA,CAAA,UAAO;;;KAJlB", "debugId": null}}, {"offset": {"line": 1133, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/templete/pdf-exam-generator/src/components/layout/Footer.tsx"], "sourcesContent": ["import Link from 'next/link';\nimport { FileText, Mail, Shield, FileCheck } from 'lucide-react';\n\nexport function Footer() {\n  const currentYear = new Date().getFullYear();\n\n  const footerLinks = {\n    product: [\n      { name: 'Features', href: '#features' },\n      { name: 'How it Works', href: '#how-it-works' },\n      { name: 'Pricing', href: '#pricing' },\n      { name: 'API Documentation', href: '#api' },\n    ],\n    legal: [\n      { name: 'Privacy Policy', href: '/privacy' },\n      { name: 'Terms of Service', href: '/terms' },\n      { name: 'Cookie Policy', href: '/cookies' },\n      { name: 'GDPR Compliance', href: '/gdpr' },\n    ],\n    support: [\n      { name: 'Help Center', href: '/help' },\n      { name: 'Contact Us', href: '/contact' },\n      { name: 'Bug Reports', href: '/bugs' },\n      { name: 'Feature Requests', href: '/features' },\n    ],\n  };\n\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {/* Brand section */}\n          <div className=\"lg:col-span-1\">\n            <div className=\"flex items-center space-x-2 mb-4\">\n              <div className=\"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n                <FileText className=\"w-5 h-5 text-white\" />\n              </div>\n              <span className=\"text-xl font-bold\">PDF Exam Generator</span>\n            </div>\n            <p className=\"text-gray-400 text-sm mb-4\">\n              Transform your PDF documents into interactive exams using AI. \n              Perfect for educators, students, and e-learning platforms.\n            </p>\n            <div className=\"flex items-center space-x-2 text-sm text-gray-400\">\n              <Shield className=\"w-4 h-4\" />\n              <span>Secure & Privacy-focused</span>\n            </div>\n          </div>\n\n          {/* Product links */}\n          <div>\n            <h3 className=\"text-sm font-semibold text-gray-300 uppercase tracking-wider mb-4\">\n              Product\n            </h3>\n            <ul className=\"space-y-2\">\n              {footerLinks.product.map((link) => (\n                <li key={link.name}>\n                  <Link\n                    href={link.href}\n                    className=\"text-gray-400 hover:text-white text-sm transition-colors\"\n                  >\n                    {link.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Legal links */}\n          <div>\n            <h3 className=\"text-sm font-semibold text-gray-300 uppercase tracking-wider mb-4\">\n              Legal\n            </h3>\n            <ul className=\"space-y-2\">\n              {footerLinks.legal.map((link) => (\n                <li key={link.name}>\n                  <Link\n                    href={link.href}\n                    className=\"text-gray-400 hover:text-white text-sm transition-colors\"\n                  >\n                    {link.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Support links */}\n          <div>\n            <h3 className=\"text-sm font-semibold text-gray-300 uppercase tracking-wider mb-4\">\n              Support\n            </h3>\n            <ul className=\"space-y-2\">\n              {footerLinks.support.map((link) => (\n                <li key={link.name}>\n                  <Link\n                    href={link.href}\n                    className=\"text-gray-400 hover:text-white text-sm transition-colors\"\n                  >\n                    {link.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n        </div>\n\n        {/* Bottom section */}\n        <div className=\"mt-8 pt-8 border-t border-gray-800\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center\">\n            <div className=\"flex items-center space-x-4 text-sm text-gray-400\">\n              <span>© {currentYear} PDF Exam Generator. All rights reserved.</span>\n            </div>\n            <div className=\"flex items-center space-x-4 mt-4 md:mt-0\">\n              <div className=\"flex items-center space-x-2 text-sm text-gray-400\">\n                <FileCheck className=\"w-4 h-4\" />\n                <span>AI-Powered</span>\n              </div>\n              <div className=\"flex items-center space-x-2 text-sm text-gray-400\">\n                <Mail className=\"w-4 h-4\" />\n                <Link href=\"mailto:<EMAIL>\" className=\"hover:text-white transition-colors\">\n                  <EMAIL>\n                </Link>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;;;;AAEO,SAAS;IACd,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,MAAM,cAAc;QAClB,SAAS;YACP;gBAAE,MAAM;gBAAY,MAAM;YAAY;YACtC;gBAAE,MAAM;gBAAgB,MAAM;YAAgB;YAC9C;gBAAE,MAAM;gBAAW,MAAM;YAAW;YACpC;gBAAE,MAAM;gBAAqB,MAAM;YAAO;SAC3C;QACD,OAAO;YACL;gBAAE,MAAM;gBAAkB,MAAM;YAAW;YAC3C;gBAAE,MAAM;gBAAoB,MAAM;YAAS;YAC3C;gBAAE,MAAM;gBAAiB,MAAM;YAAW;YAC1C;gBAAE,MAAM;gBAAmB,MAAM;YAAQ;SAC1C;QACD,SAAS;YACP;gBAAE,MAAM;gBAAe,MAAM;YAAQ;YACrC;gBAAE,MAAM;gBAAc,MAAM;YAAW;YACvC;gBAAE,MAAM;gBAAe,MAAM;YAAQ;YACrC;gBAAE,MAAM;gBAAoB,MAAM;YAAY;SAC/C;IACH;IAEA,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,iNAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,6LAAC;4CAAK,WAAU;sDAAoB;;;;;;;;;;;;8CAEtC,6LAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAI1C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC;sDAAK;;;;;;;;;;;;;;;;;;sCAKV,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAoE;;;;;;8CAGlF,6LAAC;oCAAG,WAAU;8CACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAoE;;;;;;8CAGlF,6LAAC;oCAAG,WAAU;8CACX,YAAY,KAAK,CAAC,GAAG,CAAC,CAAC,qBACtB,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAoE;;;;;;8CAGlF,6LAAC;oCAAG,WAAU;8CACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;8BAc1B,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;;wCAAK;wCAAG;wCAAY;;;;;;;;;;;;0CAEvB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,mNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;0DACrB,6LAAC;0DAAK;;;;;;;;;;;;kDAER,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAgC,WAAU;0DAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU1G;KA/HgB", "debugId": null}}, {"offset": {"line": 1517, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/templete/pdf-exam-generator/src/components/layout/DashboardLayout.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { useLanguage } from '@/contexts/LanguageContext';\nimport { usePathname } from 'next/navigation';\nimport Link from 'next/link';\nimport {\n  LayoutDashboard,\n  BookOpen,\n  BarChart3,\n  Settings,\n  LogOut,\n  Menu,\n  X,\n  User,\n  Bell,\n  Search,\n  Upload,\n  History,\n  Users,\n  Moon,\n  Sun\n} from 'lucide-react';\n\nexport function DashboardLayout({ children }: { children: React.ReactNode }) {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const { user, logout } = useAuth();\n  const { theme, setTheme, t } = useLanguage();\n  const pathname = usePathname();\n\n  const toggleTheme = () => {\n    setTheme(theme === 'light' ? 'dark' : 'light');\n  };\n\n  const navigation = [\n    { name: t('nav.dashboard'), href: '/dashboard', icon: LayoutDashboard },\n    { name: t('nav.upload'), href: '/dashboard/upload', icon: Upload },\n    { name: t('nav.exams'), href: '/dashboard/exams', icon: BookOpen },\n    { name: t('nav.analytics'), href: '/dashboard/analytics', icon: BarChart3 },\n    { name: t('nav.history'), href: '/dashboard/history', icon: History },\n    { name: t('nav.students'), href: '/dashboard/students', icon: Users },\n    { name: t('nav.profile'), href: '/profile', icon: User },\n    { name: t('nav.settings'), href: '/settings', icon: Settings },\n  ];\n\n  const handleLogout = () => {\n    logout();\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900 flex\">\n      {/* Mobile sidebar overlay */}\n      {sidebarOpen && (\n        <div\n          className=\"fixed inset-0 z-40 lg:hidden\"\n          onClick={() => setSidebarOpen(false)}\n        >\n          <div className=\"absolute inset-0 bg-gray-600 opacity-75\"></div>\n        </div>\n      )}\n\n      {/* Sidebar */}\n      <div className={`fixed inset-y-0 right-0 z-50 w-64 bg-white dark:bg-gray-800 shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0 ${\n        sidebarOpen ? 'translate-x-0' : 'translate-x-full'\n      }`}>\n        <div className=\"flex items-center justify-between h-16 px-6 border-b border-gray-200 dark:border-gray-700\">\n          <div className=\"flex items-center\">\n            <div className=\"w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center\">\n              <BookOpen className=\"w-5 h-5 text-white\" />\n            </div>\n            <span className=\"mr-3 text-lg font-semibold text-gray-900 dark:text-white\">ExamAI</span>\n          </div>\n          <button\n            onClick={() => setSidebarOpen(false)}\n            className=\"lg:hidden text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300\"\n          >\n            <X className=\"w-6 h-6\" />\n          </button>\n        </div>\n\n        {/* User Profile */}\n        <div className=\"p-6 border-b border-gray-200 dark:border-gray-700\">\n          <div className=\"flex items-center\">\n            <div className=\"w-12 h-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center\">\n              {user?.photoURL ? (\n                <img src={user.photoURL} alt={user?.displayName || 'User'} className=\"w-12 h-12 rounded-full object-cover\" />\n              ) : (\n                <User className=\"w-6 h-6 text-white\" />\n              )}\n            </div>\n            <div className=\"mr-3\">\n              <div className=\"text-sm font-medium text-gray-900 dark:text-white\">{user?.displayName || user?.email?.split('@')[0] || 'المستخدم'}</div>\n              <div className=\"text-xs text-gray-500 dark:text-gray-400 capitalize\">مستخدم</div>\n            </div>\n          </div>\n        </div>\n\n        {/* Navigation */}\n        <nav className=\"mt-6 px-3\">\n          <div className=\"space-y-1\">\n            {navigation.map((item) => {\n              const isActive = pathname === item.href || pathname.startsWith(item.href + '/');\n              return (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className={`group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors ${\n                    isActive\n                      ? 'bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900 dark:to-purple-900 text-blue-700 dark:text-blue-300 border-l-4 border-blue-700 dark:border-blue-400'\n                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-white'\n                  }`}\n                >\n                  <item.icon className={`ml-3 w-5 h-5 ${\n                    isActive ? 'text-blue-700 dark:text-blue-300' : 'text-gray-400 dark:text-gray-500 group-hover:text-gray-500 dark:group-hover:text-gray-400'\n                  }`} />\n                  {item.name}\n                </Link>\n              );\n            })}\n          </div>\n        </nav>\n\n        {/* User Stats */}\n        <div className=\"mt-8 px-6\">\n          <div className=\"bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900 dark:to-purple-900 rounded-lg p-4\">\n            <h3 className=\"text-sm font-medium text-gray-900 dark:text-white mb-3\">إحصائياتك</h3>\n            <div className=\"space-y-2\">\n              <div className=\"flex justify-between text-xs\">\n                <span className=\"text-gray-600 dark:text-gray-400\">الاختبارات</span>\n                <span className=\"font-medium text-gray-900 dark:text-white\">24</span>\n              </div>\n              <div className=\"flex justify-between text-xs\">\n                <span className=\"text-gray-600 dark:text-gray-400\">المعدل</span>\n                <span className=\"font-medium text-gray-900 dark:text-white\">85%</span>\n              </div>\n              <div className=\"flex justify-between text-xs\">\n                <span className=\"text-gray-600 dark:text-gray-400\">الأسئلة</span>\n                <span className=\"font-medium text-gray-900 dark:text-white\">342</span>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Logout */}\n        <div className=\"absolute bottom-6 left-0 right-0 px-6\">\n          <button\n            onClick={handleLogout}\n            className=\"w-full flex items-center px-3 py-2 text-sm font-medium text-red-700 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900 rounded-lg transition-colors\"\n          >\n            <LogOut className=\"ml-3 w-5 h-5\" />\n            تسجيل الخروج\n          </button>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"flex-1 flex flex-col lg:mr-64\">\n        {/* Top bar */}\n        <header className=\"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700\">\n          <div className=\"flex items-center justify-between h-16 px-6\">\n            <div className=\"flex items-center\">\n              <button\n                onClick={() => setSidebarOpen(true)}\n                className=\"lg:hidden text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300\"\n              >\n                <Menu className=\"w-6 h-6\" />\n              </button>\n\n              <div className=\"lg:hidden mr-4\">\n                <h1 className=\"text-lg font-semibold text-gray-900 dark:text-white\">لوحة التحكم</h1>\n              </div>\n            </div>\n\n            <div className=\"flex items-center space-x-4 rtl:space-x-reverse\">\n              {/* Search */}\n              <div className=\"hidden md:block relative\">\n                <Search className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500 w-4 h-4\" />\n                <input\n                  type=\"text\"\n                  placeholder=\"البحث...\"\n                  className=\"w-64 pl-4 pr-10 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm\"\n                />\n              </div>\n\n              {/* Theme Toggle */}\n              <button\n                onClick={toggleTheme}\n                className=\"p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors\"\n              >\n                {theme === 'light' ? (\n                  <Moon className=\"w-5 h-5\" />\n                ) : (\n                  <Sun className=\"w-5 h-5\" />\n                )}\n              </button>\n\n              {/* Notifications */}\n              <button className=\"relative p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg\">\n                <Bell className=\"w-5 h-5\" />\n                <span className=\"absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full\"></span>\n              </button>\n\n              {/* User menu */}\n              <div className=\"flex items-center\">\n                <div className=\"w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center\">\n                  {user?.photoURL ? (\n                    <img src={user.photoURL} alt={user?.displayName || 'User'} className=\"w-8 h-8 rounded-full object-cover\" />\n                  ) : (\n                    <User className=\"w-4 h-4 text-white\" />\n                  )}\n                </div>\n              </div>\n            </div>\n          </div>\n        </header>\n\n        {/* Page content */}\n        <main className=\"flex-1 overflow-auto bg-gray-50 dark:bg-gray-900\">\n          {children}\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAPA;;;;;;;AAyBO,SAAS,gBAAgB,EAAE,QAAQ,EAAiC;;IACzE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,cAAW,AAAD;IACzC,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,cAAc;QAClB,SAAS,UAAU,UAAU,SAAS;IACxC;IAEA,MAAM,aAAa;QACjB;YAAE,MAAM,EAAE;YAAkB,MAAM;YAAc,MAAM,+NAAA,CAAA,kBAAe;QAAC;QACtE;YAAE,MAAM,EAAE;YAAe,MAAM;YAAqB,MAAM,yMAAA,CAAA,SAAM;QAAC;QACjE;YAAE,MAAM,EAAE;YAAc,MAAM;YAAoB,MAAM,iNAAA,CAAA,WAAQ;QAAC;QACjE;YAAE,MAAM,EAAE;YAAkB,MAAM;YAAwB,MAAM,qNAAA,CAAA,YAAS;QAAC;QAC1E;YAAE,MAAM,EAAE;YAAgB,MAAM;YAAsB,MAAM,2MAAA,CAAA,UAAO;QAAC;QACpE;YAAE,MAAM,EAAE;YAAiB,MAAM;YAAuB,MAAM,uMAAA,CAAA,QAAK;QAAC;QACpE;YAAE,MAAM,EAAE;YAAgB,MAAM;YAAY,MAAM,qMAAA,CAAA,OAAI;QAAC;QACvD;YAAE,MAAM,EAAE;YAAiB,MAAM;YAAa,MAAM,6MAAA,CAAA,WAAQ;QAAC;KAC9D;IAED,MAAM,eAAe;QACnB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;YAEZ,6BACC,6LAAC;gBACC,WAAU;gBACV,SAAS,IAAM,eAAe;0BAE9B,cAAA,6LAAC;oBAAI,WAAU;;;;;;;;;;;0BAKnB,6LAAC;gBAAI,WAAW,CAAC,oKAAoK,EACnL,cAAc,kBAAkB,oBAChC;;kCACA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,iNAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAEtB,6LAAC;wCAAK,WAAU;kDAA2D;;;;;;;;;;;;0CAE7E,6LAAC;gCACC,SAAS,IAAM,eAAe;gCAC9B,WAAU;0CAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAKjB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACZ,MAAM,yBACL,6LAAC;wCAAI,KAAK,KAAK,QAAQ;wCAAE,KAAK,MAAM,eAAe;wCAAQ,WAAU;;;;;6DAErE,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;8CAGpB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAqD,MAAM,eAAe,MAAM,OAAO,MAAM,IAAI,CAAC,EAAE,IAAI;;;;;;sDACvH,6LAAC;4CAAI,WAAU;sDAAsD;;;;;;;;;;;;;;;;;;;;;;;kCAM3E,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC;gCACf,MAAM,WAAW,aAAa,KAAK,IAAI,IAAI,SAAS,UAAU,CAAC,KAAK,IAAI,GAAG;gCAC3E,qBACE,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAW,CAAC,mFAAmF,EAC7F,WACI,sKACA,sHACJ;;sDAEF,6LAAC,KAAK,IAAI;4CAAC,WAAW,CAAC,aAAa,EAClC,WAAW,qCAAqC,6FAChD;;;;;;wCACD,KAAK,IAAI;;mCAXL,KAAK,IAAI;;;;;4BAcpB;;;;;;;;;;;kCAKJ,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAyD;;;;;;8CACvE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAmC;;;;;;8DACnD,6LAAC;oDAAK,WAAU;8DAA4C;;;;;;;;;;;;sDAE9D,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAmC;;;;;;8DACnD,6LAAC;oDAAK,WAAU;8DAA4C;;;;;;;;;;;;sDAE9D,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAmC;;;;;;8DACnD,6LAAC;oDAAK,WAAU;8DAA4C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOpE,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,SAAS;4BACT,WAAU;;8CAEV,6LAAC,6MAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;0BAOzC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAO,WAAU;kCAChB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,SAAS,IAAM,eAAe;4CAC9B,WAAU;sDAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAGlB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAG,WAAU;0DAAsD;;;;;;;;;;;;;;;;;8CAIxE,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,6LAAC;oDACC,MAAK;oDACL,aAAY;oDACZ,WAAU;;;;;;;;;;;;sDAKd,6LAAC;4CACC,SAAS;4CACT,WAAU;sDAET,UAAU,wBACT,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;qEAEhB,6LAAC,mMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;;;;;;sDAKnB,6LAAC;4CAAO,WAAU;;8DAChB,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,6LAAC;oDAAK,WAAU;;;;;;;;;;;;sDAIlB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;0DACZ,MAAM,yBACL,6LAAC;oDAAI,KAAK,KAAK,QAAQ;oDAAE,KAAK,MAAM,eAAe;oDAAQ,WAAU;;;;;yEAErE,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAS5B,6LAAC;wBAAK,WAAU;kCACb;;;;;;;;;;;;;;;;;;AAKX;GAvMgB;;QAEW,kIAAA,CAAA,UAAO;QACD,sIAAA,CAAA,cAAW;QACzB,qIAAA,CAAA,cAAW;;;KAJd", "debugId": null}}, {"offset": {"line": 2122, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/templete/pdf-exam-generator/src/components/layout/AppContent.tsx"], "sourcesContent": ["'use client';\n\nimport { useAuth } from '@/contexts/AuthContext';\nimport { useLanguage } from '@/contexts/LanguageContext';\nimport { usePathname } from 'next/navigation';\nimport { Navbar } from './Navbar';\nimport { Footer } from './Footer';\nimport { DashboardLayout } from './DashboardLayout';\n\nexport function AppContent({ children }: { children: React.ReactNode }) {\n  const { isAuthenticated, loading } = useAuth();\n  const { theme } = useLanguage();\n  const pathname = usePathname();\n\n  // Show loading spinner while checking authentication\n  if (loading) {\n    return (\n      <div className={`min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 ${theme === 'dark' ? 'dark' : ''}`}>\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600 dark:text-gray-400\">جاري التحميل...</p>\n        </div>\n      </div>\n    );\n  }\n\n  // Check if it's an auth route\n  const isAuthRoute = pathname.startsWith('/auth/');\n\n  // For auth routes, show without navbar/footer\n  if (isAuthRoute) {\n    return <>{children}</>;\n  }\n\n  // If not authenticated and not auth route, show with navbar for landing page\n  if (!isAuthenticated) {\n    return (\n      <div className={`min-h-screen flex flex-col ${theme === 'dark' ? 'dark' : ''}`}>\n        <Navbar />\n        <main className=\"flex-1\">\n          {children}\n        </main>\n        <Footer />\n      </div>\n    );\n  }\n\n  // If authenticated, check if it's a dashboard route\n  const isDashboardRoute = pathname.startsWith('/dashboard') || \n                          pathname === '/analyze' || \n                          pathname === '/exam' || \n                          pathname === '/results' || \n                          pathname === '/settings';\n\n  if (isDashboardRoute) {\n    return (\n      <div className={theme === 'dark' ? 'dark' : ''}>\n        <DashboardLayout>\n          {children}\n        </DashboardLayout>\n      </div>\n    );\n  }\n\n  // For other authenticated routes, use regular layout\n  return (\n    <div className={`min-h-screen flex flex-col ${theme === 'dark' ? 'dark' : ''}`}>\n      <Navbar />\n      <main className=\"flex-1\">\n        {children}\n      </main>\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AASO,SAAS,WAAW,EAAE,QAAQ,EAAiC;;IACpE,MAAM,EAAE,eAAe,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC3C,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,cAAW,AAAD;IAC5B,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,qDAAqD;IACrD,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAW,CAAC,0EAA0E,EAAE,UAAU,SAAS,SAAS,IAAI;sBAC3H,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAmC;;;;;;;;;;;;;;;;;IAIxD;IAEA,8BAA8B;IAC9B,MAAM,cAAc,SAAS,UAAU,CAAC;IAExC,8CAA8C;IAC9C,IAAI,aAAa;QACf,qBAAO;sBAAG;;IACZ;IAEA,6EAA6E;IAC7E,IAAI,CAAC,iBAAiB;QACpB,qBACE,6LAAC;YAAI,WAAW,CAAC,2BAA2B,EAAE,UAAU,SAAS,SAAS,IAAI;;8BAC5E,6LAAC,yIAAA,CAAA,SAAM;;;;;8BACP,6LAAC;oBAAK,WAAU;8BACb;;;;;;8BAEH,6LAAC,yIAAA,CAAA,SAAM;;;;;;;;;;;IAGb;IAEA,oDAAoD;IACpD,MAAM,mBAAmB,SAAS,UAAU,CAAC,iBACrB,aAAa,cACb,aAAa,WACb,aAAa,cACb,aAAa;IAErC,IAAI,kBAAkB;QACpB,qBACE,6LAAC;YAAI,WAAW,UAAU,SAAS,SAAS;sBAC1C,cAAA,6LAAC,kJAAA,CAAA,kBAAe;0BACb;;;;;;;;;;;IAIT;IAEA,qDAAqD;IACrD,qBACE,6LAAC;QAAI,WAAW,CAAC,2BAA2B,EAAE,UAAU,SAAS,SAAS,IAAI;;0BAC5E,6LAAC,yIAAA,CAAA,SAAM;;;;;0BACP,6LAAC;gBAAK,WAAU;0BACb;;;;;;0BAEH,6LAAC,yIAAA,CAAA,SAAM;;;;;;;;;;;AAGb;GAjEgB;;QACuB,kIAAA,CAAA,UAAO;QAC1B,sIAAA,CAAA,cAAW;QACZ,qIAAA,CAAA,cAAW;;;KAHd", "debugId": null}}]}