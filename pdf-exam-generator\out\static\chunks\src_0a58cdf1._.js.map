{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/templete/pdf-exam-generator/src/components/ui/FileUpload.tsx"], "sourcesContent": ["'use client';\n\nimport { useCallback, useState } from 'react';\nimport { useDropzone } from 'react-dropzone';\nimport { Upload, FileText, X, AlertCircle, CheckCircle } from 'lucide-react';\n\ninterface FileUploadProps {\n  onFileSelect: (file: File) => void;\n  maxSize?: number; // in MB\n  className?: string;\n}\n\nexport function FileUpload({ onFileSelect, maxSize = 10, className = '' }: FileUploadProps) {\n  const [uploadProgress, setUploadProgress] = useState(0);\n  const [uploadStatus, setUploadStatus] = useState<'idle' | 'uploading' | 'success' | 'error'>('idle');\n  const [errorMessage, setErrorMessage] = useState('');\n  const [selectedFile, setSelectedFile] = useState<File | null>(null);\n\n  const onDrop = useCallback((acceptedFiles: File[], rejectedFiles: any[]) => {\n    setErrorMessage('');\n    \n    if (rejectedFiles.length > 0) {\n      const rejection = rejectedFiles[0];\n      if (rejection.errors[0]?.code === 'file-too-large') {\n        setErrorMessage(`File is too large. Maximum size is ${maxSize}MB.`);\n      } else if (rejection.errors[0]?.code === 'file-invalid-type') {\n        setErrorMessage('Only PDF files are allowed.');\n      } else {\n        setErrorMessage('Invalid file. Please try again.');\n      }\n      setUploadStatus('error');\n      return;\n    }\n\n    if (acceptedFiles.length > 0) {\n      const file = acceptedFiles[0];\n      setSelectedFile(file);\n      setUploadStatus('uploading');\n      \n      // Simulate upload progress\n      let progress = 0;\n      const interval = setInterval(() => {\n        progress += Math.random() * 30;\n        if (progress >= 100) {\n          progress = 100;\n          clearInterval(interval);\n          setUploadStatus('success');\n          onFileSelect(file);\n        }\n        setUploadProgress(progress);\n      }, 200);\n    }\n  }, [maxSize, onFileSelect]);\n\n  const { getRootProps, getInputProps, isDragActive } = useDropzone({\n    onDrop,\n    accept: {\n      'application/pdf': ['.pdf']\n    },\n    maxSize: maxSize * 1024 * 1024, // Convert MB to bytes\n    multiple: false\n  });\n\n  const removeFile = () => {\n    setSelectedFile(null);\n    setUploadStatus('idle');\n    setUploadProgress(0);\n    setErrorMessage('');\n  };\n\n  const formatFileSize = (bytes: number) => {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  };\n\n  return (\n    <div className={`w-full ${className}`}>\n      {uploadStatus === 'idle' && (\n        <div\n          {...getRootProps()}\n          className={`\n            border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-all duration-200\n            ${isDragActive \n              ? 'border-blue-500 bg-blue-50 scale-105' \n              : 'border-gray-300 hover:border-gray-400 hover:bg-gray-50'\n            }\n          `}\n        >\n          <input {...getInputProps()} />\n          <div className=\"flex flex-col items-center space-y-4\">\n            <div className={`p-4 rounded-full ${isDragActive ? 'bg-blue-100' : 'bg-gray-100'}`}>\n              <Upload className={`w-8 h-8 ${isDragActive ? 'text-blue-600' : 'text-gray-600'}`} />\n            </div>\n            <div>\n              <p className=\"text-lg font-medium text-gray-900 mb-2\">\n                {isDragActive ? 'Drop your PDF here' : 'Upload your PDF document'}\n              </p>\n              <p className=\"text-sm text-gray-600 mb-4\">\n                Drag and drop your PDF file here, or click to browse\n              </p>\n              <div className=\"flex items-center justify-center space-x-4 text-xs text-gray-500\">\n                <span className=\"flex items-center\">\n                  <FileText className=\"w-4 h-4 mr-1\" />\n                  PDF only\n                </span>\n                <span>Max {maxSize}MB</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {uploadStatus === 'uploading' && selectedFile && (\n        <div className=\"border border-gray-200 rounded-lg p-6\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <div className=\"flex items-center space-x-3\">\n              <FileText className=\"w-8 h-8 text-blue-600\" />\n              <div>\n                <p className=\"font-medium text-gray-900\">{selectedFile.name}</p>\n                <p className=\"text-sm text-gray-600\">{formatFileSize(selectedFile.size)}</p>\n              </div>\n            </div>\n            <button\n              onClick={removeFile}\n              className=\"p-1 hover:bg-gray-100 rounded-full transition-colors\"\n            >\n              <X className=\"w-5 h-5 text-gray-500\" />\n            </button>\n          </div>\n          <div className=\"space-y-2\">\n            <div className=\"flex justify-between text-sm\">\n              <span className=\"text-gray-600\">Uploading...</span>\n              <span className=\"text-gray-900\">{Math.round(uploadProgress)}%</span>\n            </div>\n            <div className=\"w-full bg-gray-200 rounded-full h-2\">\n              <div \n                className=\"bg-blue-600 h-2 rounded-full transition-all duration-300\"\n                style={{ width: `${uploadProgress}%` }}\n              />\n            </div>\n          </div>\n        </div>\n      )}\n\n      {uploadStatus === 'success' && selectedFile && (\n        <div className=\"border border-green-200 bg-green-50 rounded-lg p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-3\">\n              <CheckCircle className=\"w-8 h-8 text-green-600\" />\n              <div>\n                <p className=\"font-medium text-green-900\">{selectedFile.name}</p>\n                <p className=\"text-sm text-green-700\">Upload successful • {formatFileSize(selectedFile.size)}</p>\n              </div>\n            </div>\n            <button\n              onClick={removeFile}\n              className=\"p-1 hover:bg-green-100 rounded-full transition-colors\"\n            >\n              <X className=\"w-5 h-5 text-green-600\" />\n            </button>\n          </div>\n        </div>\n      )}\n\n      {uploadStatus === 'error' && (\n        <div className=\"border border-red-200 bg-red-50 rounded-lg p-6\">\n          <div className=\"flex items-center space-x-3\">\n            <AlertCircle className=\"w-8 h-8 text-red-600\" />\n            <div>\n              <p className=\"font-medium text-red-900\">Upload failed</p>\n              <p className=\"text-sm text-red-700\">{errorMessage}</p>\n            </div>\n          </div>\n          <button\n            onClick={() => {\n              setUploadStatus('idle');\n              setErrorMessage('');\n            }}\n            className=\"mt-4 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors text-sm\"\n          >\n            Try Again\n          </button>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;;;AAJA;;;;AAYO,SAAS,WAAW,EAAE,YAAY,EAAE,UAAU,EAAE,EAAE,YAAY,EAAE,EAAmB;;IACxF,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA8C;IAC7F,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAE9D,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0CAAE,CAAC,eAAuB;YACjD,gBAAgB;YAEhB,IAAI,cAAc,MAAM,GAAG,GAAG;gBAC5B,MAAM,YAAY,aAAa,CAAC,EAAE;gBAClC,IAAI,UAAU,MAAM,CAAC,EAAE,EAAE,SAAS,kBAAkB;oBAClD,gBAAgB,CAAC,mCAAmC,EAAE,QAAQ,GAAG,CAAC;gBACpE,OAAO,IAAI,UAAU,MAAM,CAAC,EAAE,EAAE,SAAS,qBAAqB;oBAC5D,gBAAgB;gBAClB,OAAO;oBACL,gBAAgB;gBAClB;gBACA,gBAAgB;gBAChB;YACF;YAEA,IAAI,cAAc,MAAM,GAAG,GAAG;gBAC5B,MAAM,OAAO,aAAa,CAAC,EAAE;gBAC7B,gBAAgB;gBAChB,gBAAgB;gBAEhB,2BAA2B;gBAC3B,IAAI,WAAW;gBACf,MAAM,WAAW;+DAAY;wBAC3B,YAAY,KAAK,MAAM,KAAK;wBAC5B,IAAI,YAAY,KAAK;4BACnB,WAAW;4BACX,cAAc;4BACd,gBAAgB;4BAChB,aAAa;wBACf;wBACA,kBAAkB;oBACpB;8DAAG;YACL;QACF;yCAAG;QAAC;QAAS;KAAa;IAE1B,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,2KAAA,CAAA,cAAW,AAAD,EAAE;QAChE;QACA,QAAQ;YACN,mBAAmB;gBAAC;aAAO;QAC7B;QACA,SAAS,UAAU,OAAO;QAC1B,UAAU;IACZ;IAEA,MAAM,aAAa;QACjB,gBAAgB;QAChB,gBAAgB;QAChB,kBAAkB;QAClB,gBAAgB;IAClB;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,UAAU,GAAG,OAAO;QACxB,MAAM,IAAI;QACV,MAAM,QAAQ;YAAC;YAAS;YAAM;YAAM;SAAK;QACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAChD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;IACzE;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,OAAO,EAAE,WAAW;;YAClC,iBAAiB,wBAChB,6LAAC;gBACE,GAAG,cAAc;gBAClB,WAAW,CAAC;;YAEV,EAAE,eACE,yCACA,yDACH;UACH,CAAC;;kCAED,6LAAC;wBAAO,GAAG,eAAe;;;;;;kCAC1B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAW,CAAC,iBAAiB,EAAE,eAAe,gBAAgB,eAAe;0CAChF,cAAA,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAW,CAAC,QAAQ,EAAE,eAAe,kBAAkB,iBAAiB;;;;;;;;;;;0CAElF,6LAAC;;kDACC,6LAAC;wCAAE,WAAU;kDACV,eAAe,uBAAuB;;;;;;kDAEzC,6LAAC;wCAAE,WAAU;kDAA6B;;;;;;kDAG1C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;;kEACd,6LAAC,iNAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGvC,6LAAC;;oDAAK;oDAAK;oDAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAO5B,iBAAiB,eAAe,8BAC/B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAA6B,aAAa,IAAI;;;;;;0DAC3D,6LAAC;gDAAE,WAAU;0DAAyB,eAAe,aAAa,IAAI;;;;;;;;;;;;;;;;;;0CAG1E,6LAAC;gCACC,SAAS;gCACT,WAAU;0CAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAGjB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,6LAAC;wCAAK,WAAU;;4CAAiB,KAAK,KAAK,CAAC;4CAAgB;;;;;;;;;;;;;0CAE9D,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,WAAU;oCACV,OAAO;wCAAE,OAAO,GAAG,eAAe,CAAC,CAAC;oCAAC;;;;;;;;;;;;;;;;;;;;;;;YAO9C,iBAAiB,aAAa,8BAC7B,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,8NAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAA8B,aAAa,IAAI;;;;;;sDAC5D,6LAAC;4CAAE,WAAU;;gDAAyB;gDAAqB,eAAe,aAAa,IAAI;;;;;;;;;;;;;;;;;;;sCAG/F,6LAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;YAMpB,iBAAiB,yBAChB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,uNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;0CACvB,6LAAC;;kDACC,6LAAC;wCAAE,WAAU;kDAA2B;;;;;;kDACxC,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;kCAGzC,6LAAC;wBACC,SAAS;4BACP,gBAAgB;4BAChB,gBAAgB;wBAClB;wBACA,WAAU;kCACX;;;;;;;;;;;;;;;;;;AAOX;GAjLgB;;QA0CwC,2KAAA,CAAA,cAAW;;;KA1CnD", "debugId": null}}, {"offset": {"line": 491, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/templete/pdf-exam-generator/src/components/sections/HeroSection.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport Link from 'next/link';\nimport { FileUpload } from '@/components/ui/FileUpload';\nimport { Sparkles, Users, Clock, Shield, LogIn, UserPlus } from 'lucide-react';\n\nexport function HeroSection() {\n  const router = useRouter();\n  const [uploadedFile, setUploadedFile] = useState<File | null>(null);\n\n  const handleFileSelect = (file: File) => {\n    setUploadedFile(file);\n    // Store file in localStorage for now (in production, upload to server)\n    localStorage.setItem('uploadedPDF', JSON.stringify({\n      name: file.name,\n      size: file.size,\n      type: file.type,\n      lastModified: file.lastModified\n    }));\n    \n    // Navigate to analysis page after a short delay\n    setTimeout(() => {\n      router.push('/analyze');\n    }, 1500);\n  };\n\n  const features = [\n    {\n      icon: Sparkles,\n      title: 'AI-Powered',\n      description: 'Advanced AI analyzes your PDF and generates relevant questions'\n    },\n    {\n      icon: Users,\n      title: 'For Educators',\n      description: 'Perfect for teachers, trainers, and e-learning platforms'\n    },\n    {\n      icon: Clock,\n      title: 'Save Time',\n      description: 'Create comprehensive exams in minutes, not hours'\n    },\n    {\n      icon: Shield,\n      title: 'Secure',\n      description: 'Your documents are processed securely and privately'\n    }\n  ];\n\n  return (\n    <div className=\"relative overflow-hidden bg-gradient-to-br from-blue-50 via-white to-purple-50\">\n      {/* Background decoration */}\n      <div className=\"absolute inset-0 bg-grid-pattern opacity-5\"></div>\n      <div className=\"absolute top-0 left-1/2 transform -translate-x-1/2 w-96 h-96 bg-blue-200 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse-slow\"></div>\n      <div className=\"absolute bottom-0 right-1/4 w-72 h-72 bg-purple-200 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse-slow\" style={{ animationDelay: '1s' }}></div>\n\n      <div className=\"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 lg:py-24\">\n        <div className=\"text-center mb-16\">\n          {/* Badge */}\n          <div className=\"inline-flex items-center px-4 py-2 rounded-full bg-blue-100 text-blue-800 text-sm font-medium mb-8 animate-fade-in\">\n            <Sparkles className=\"w-4 h-4 mr-2\" />\n            AI-Powered Exam Generation\n          </div>\n\n          {/* Main heading */}\n          <h1 className=\"text-4xl md:text-6xl lg:text-7xl font-bold text-gray-900 mb-6 animate-slide-up\">\n            Transform PDFs into\n            <span className=\"block text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600\">\n              Interactive Exams\n            </span>\n          </h1>\n\n          {/* Subtitle */}\n          <p className=\"text-xl md:text-2xl text-gray-600 mb-12 max-w-4xl mx-auto leading-relaxed animate-slide-up\" style={{ animationDelay: '0.2s' }}>\n            Upload any PDF document and let our AI create comprehensive, interactive exams \n            with multiple question types. Perfect for educators, students, and training programs.\n          </p>\n\n          {/* File Upload */}\n          <div className=\"max-w-2xl mx-auto mb-16 animate-slide-up\" style={{ animationDelay: '0.4s' }}>\n            <FileUpload onFileSelect={handleFileSelect} />\n          </div>\n\n          {/* Features grid */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16\">\n            {features.map((feature, index) => {\n              const Icon = feature.icon;\n              return (\n                <div \n                  key={feature.title}\n                  className=\"text-center animate-slide-up\"\n                  style={{ animationDelay: `${0.6 + index * 0.1}s` }}\n                >\n                  <div className=\"inline-flex items-center justify-center w-16 h-16 bg-white rounded-2xl shadow-lg mb-4 group-hover:shadow-xl transition-shadow\">\n                    <Icon className=\"w-8 h-8 text-blue-600\" />\n                  </div>\n                  <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">{feature.title}</h3>\n                  <p className=\"text-gray-600 text-sm leading-relaxed\">{feature.description}</p>\n                </div>\n              );\n            })}\n          </div>\n\n          {/* CTA buttons */}\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center animate-slide-up\" style={{ animationDelay: '1s' }}>\n            <Link\n              href=\"/auth/register\"\n              className=\"inline-flex items-center px-8 py-4 bg-blue-600 text-white rounded-xl font-semibold hover:bg-blue-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-1\"\n            >\n              <UserPlus className=\"w-5 h-5 mr-2\" />\n              ابدأ مجاناً\n            </Link>\n            <Link\n              href=\"/auth/login\"\n              className=\"inline-flex items-center px-8 py-4 border-2 border-gray-300 text-gray-700 rounded-xl font-semibold hover:border-gray-400 hover:bg-gray-50 transition-all duration-200\"\n            >\n              <LogIn className=\"w-5 h-5 mr-2\" />\n              تسجيل الدخول\n            </Link>\n          </div>\n\n          {/* Trust indicators */}\n          <div className=\"mt-16 pt-8 border-t border-gray-200 animate-slide-up\" style={{ animationDelay: '1.2s' }}>\n            <p className=\"text-sm text-gray-500 mb-4\">Trusted by educators worldwide</p>\n            <div className=\"flex justify-center items-center space-x-8 opacity-60\">\n              <div className=\"text-2xl font-bold text-gray-400\">EDU+</div>\n              <div className=\"text-2xl font-bold text-gray-400\">LearnTech</div>\n              <div className=\"text-2xl font-bold text-gray-400\">EduAI</div>\n              <div className=\"text-2xl font-bold text-gray-400\">SmartClass</div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AANA;;;;;;AAQO,SAAS;;IACd,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAE9D,MAAM,mBAAmB,CAAC;QACxB,gBAAgB;QAChB,uEAAuE;QACvE,aAAa,OAAO,CAAC,eAAe,KAAK,SAAS,CAAC;YACjD,MAAM,KAAK,IAAI;YACf,MAAM,KAAK,IAAI;YACf,MAAM,KAAK,IAAI;YACf,cAAc,KAAK,YAAY;QACjC;QAEA,gDAAgD;QAChD,WAAW;YACT,OAAO,IAAI,CAAC;QACd,GAAG;IACL;IAEA,MAAM,WAAW;QACf;YACE,MAAM,6MAAA,CAAA,WAAQ;YACd,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,yMAAA,CAAA,SAAM;YACZ,OAAO;YACP,aAAa;QACf;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;gBAAmI,OAAO;oBAAE,gBAAgB;gBAAK;;;;;;0BAEhL,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAKvC,6LAAC;4BAAG,WAAU;;gCAAiF;8CAE7F,6LAAC;oCAAK,WAAU;8CAAmF;;;;;;;;;;;;sCAMrG,6LAAC;4BAAE,WAAU;4BAA6F,OAAO;gCAAE,gBAAgB;4BAAO;sCAAG;;;;;;sCAM7I,6LAAC;4BAAI,WAAU;4BAA2C,OAAO;gCAAE,gBAAgB;4BAAO;sCACxF,cAAA,6LAAC,yIAAA,CAAA,aAAU;gCAAC,cAAc;;;;;;;;;;;sCAI5B,6LAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,SAAS;gCACtB,MAAM,OAAO,QAAQ,IAAI;gCACzB,qBACE,6LAAC;oCAEC,WAAU;oCACV,OAAO;wCAAE,gBAAgB,GAAG,MAAM,QAAQ,IAAI,CAAC,CAAC;oCAAC;;sDAEjD,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;;;;;;;;;;;sDAElB,6LAAC;4CAAG,WAAU;sDAA4C,QAAQ,KAAK;;;;;;sDACvE,6LAAC;4CAAE,WAAU;sDAAyC,QAAQ,WAAW;;;;;;;mCARpE,QAAQ,KAAK;;;;;4BAWxB;;;;;;sCAIF,6LAAC;4BAAI,WAAU;4BAA+E,OAAO;gCAAE,gBAAgB;4BAAK;;8CAC1H,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,6LAAC,iNAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGvC,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,6LAAC,2MAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;sCAMtC,6LAAC;4BAAI,WAAU;4BAAuD,OAAO;gCAAE,gBAAgB;4BAAO;;8CACpG,6LAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAC1C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAmC;;;;;;sDAClD,6LAAC;4CAAI,WAAU;sDAAmC;;;;;;sDAClD,6LAAC;4CAAI,WAAU;sDAAmC;;;;;;sDAClD,6LAAC;4CAAI,WAAU;sDAAmC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOhE;GAjIgB;;QACC,qIAAA,CAAA,YAAS;;;KADV", "debugId": null}}, {"offset": {"line": 844, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/templete/pdf-exam-generator/src/components/sections/HowItWorksSection.tsx"], "sourcesContent": ["import { Upload, Brain, Settings, PlayCircle } from 'lucide-react';\n\nexport function HowItWorksSection() {\n  const steps = [\n    {\n      icon: Upload,\n      title: 'Upload Your PDF',\n      description: 'Simply drag and drop your PDF document or click to browse. We support documents up to 10MB.',\n      details: ['PDF format only', 'Maximum 10MB file size', 'Secure upload process', 'Instant processing']\n    },\n    {\n      icon: Brain,\n      title: 'AI Analysis',\n      description: 'Our advanced AI analyzes your document content and identifies key topics and concepts.',\n      details: ['Content extraction', 'Topic identification', 'Concept mapping', 'Question generation']\n    },\n    {\n      icon: Settings,\n      title: 'Configure Exam',\n      description: 'Customize your exam with question types, difficulty levels, and other preferences.',\n      details: ['Multiple question types', 'Difficulty selection', 'Language options', 'Time limits']\n    },\n    {\n      icon: PlayCircle,\n      title: 'Take the Exam',\n      description: 'Start your interactive exam with a clean, user-friendly interface and get instant results.',\n      details: ['Interactive interface', 'Auto-save progress', 'Instant feedback', 'Detailed results']\n    }\n  ];\n\n  return (\n    <section id=\"how-it-works\" className=\"py-20 bg-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section header */}\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6\">\n            How It Works\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            Transform your PDF documents into interactive exams in just four simple steps. \n            Our AI-powered platform makes exam creation effortless and efficient.\n          </p>\n        </div>\n\n        {/* Steps */}\n        <div className=\"relative\">\n          {/* Connection line */}\n          <div className=\"hidden lg:block absolute top-24 left-1/2 transform -translate-x-1/2 w-full max-w-4xl\">\n            <div className=\"relative h-1 bg-gradient-to-r from-blue-200 via-purple-200 to-green-200 rounded-full\">\n              <div className=\"absolute inset-0 bg-gradient-to-r from-blue-400 via-purple-400 to-green-400 rounded-full opacity-50 animate-pulse-slow\"></div>\n            </div>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-4\">\n            {steps.map((step, index) => {\n              const Icon = step.icon;\n              const colors = [\n                'from-blue-500 to-blue-600',\n                'from-purple-500 to-purple-600', \n                'from-indigo-500 to-indigo-600',\n                'from-green-500 to-green-600'\n              ];\n              \n              return (\n                <div key={step.title} className=\"relative group\">\n                  {/* Step number */}\n                  <div className=\"flex justify-center mb-6\">\n                    <div className={`relative w-16 h-16 bg-gradient-to-br ${colors[index]} rounded-2xl shadow-lg flex items-center justify-center transform group-hover:scale-110 transition-transform duration-200`}>\n                      <Icon className=\"w-8 h-8 text-white\" />\n                      <div className=\"absolute -top-2 -right-2 w-8 h-8 bg-white rounded-full shadow-md flex items-center justify-center\">\n                        <span className=\"text-sm font-bold text-gray-700\">{index + 1}</span>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Content */}\n                  <div className=\"text-center\">\n                    <h3 className=\"text-xl font-bold text-gray-900 mb-3\">{step.title}</h3>\n                    <p className=\"text-gray-600 mb-6 leading-relaxed\">{step.description}</p>\n                    \n                    {/* Details */}\n                    <div className=\"bg-gray-50 rounded-lg p-4 group-hover:bg-gray-100 transition-colors\">\n                      <ul className=\"space-y-2\">\n                        {step.details.map((detail, detailIndex) => (\n                          <li key={detailIndex} className=\"flex items-center text-sm text-gray-600\">\n                            <div className={`w-2 h-2 bg-gradient-to-r ${colors[index]} rounded-full mr-3 flex-shrink-0`}></div>\n                            {detail}\n                          </li>\n                        ))}\n                      </ul>\n                    </div>\n                  </div>\n                </div>\n              );\n            })}\n          </div>\n        </div>\n\n        {/* Bottom CTA */}\n        <div className=\"text-center mt-16\">\n          <div className=\"bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-8 md:p-12\">\n            <h3 className=\"text-2xl md:text-3xl font-bold text-gray-900 mb-4\">\n              Ready to Get Started?\n            </h3>\n            <p className=\"text-lg text-gray-600 mb-8 max-w-2xl mx-auto\">\n              Join thousands of educators who are already using our platform to create \n              engaging and effective exams from their PDF materials.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <button className=\"px-8 py-4 bg-blue-600 text-white rounded-xl font-semibold hover:bg-blue-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-1\">\n                Start Creating Exams\n              </button>\n              <button className=\"px-8 py-4 border-2 border-gray-300 text-gray-700 rounded-xl font-semibold hover:border-gray-400 hover:bg-white transition-all duration-200\">\n                Watch Demo Video\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AAAA;;;AAEO,SAAS;IACd,MAAM,QAAQ;QACZ;YACE,MAAM,yMAAA,CAAA,SAAM;YACZ,OAAO;YACP,aAAa;YACb,SAAS;gBAAC;gBAAmB;gBAA0B;gBAAyB;aAAqB;QACvG;QACA;YACE,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO;YACP,aAAa;YACb,SAAS;gBAAC;gBAAsB;gBAAwB;gBAAmB;aAAsB;QACnG;QACA;YACE,MAAM,6MAAA,CAAA,WAAQ;YACd,OAAO;YACP,aAAa;YACb,SAAS;gBAAC;gBAA2B;gBAAwB;gBAAoB;aAAc;QACjG;QACA;YACE,MAAM,qNAAA,CAAA,aAAU;YAChB,OAAO;YACP,aAAa;YACb,SAAS;gBAAC;gBAAyB;gBAAsB;gBAAoB;aAAmB;QAClG;KACD;IAED,qBACE,6LAAC;QAAQ,IAAG;QAAe,WAAU;kBACnC,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAgE;;;;;;sCAG9E,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAOzD,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;sCAInB,6LAAC;4BAAI,WAAU;sCACZ,MAAM,GAAG,CAAC,CAAC,MAAM;gCAChB,MAAM,OAAO,KAAK,IAAI;gCACtB,MAAM,SAAS;oCACb;oCACA;oCACA;oCACA;iCACD;gCAED,qBACE,6LAAC;oCAAqB,WAAU;;sDAE9B,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAW,CAAC,qCAAqC,EAAE,MAAM,CAAC,MAAM,CAAC,yHAAyH,CAAC;;kEAC9L,6LAAC;wDAAK,WAAU;;;;;;kEAChB,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAK,WAAU;sEAAmC,QAAQ;;;;;;;;;;;;;;;;;;;;;;sDAMjE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAwC,KAAK,KAAK;;;;;;8DAChE,6LAAC;oDAAE,WAAU;8DAAsC,KAAK,WAAW;;;;;;8DAGnE,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAG,WAAU;kEACX,KAAK,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,4BACzB,6LAAC;gEAAqB,WAAU;;kFAC9B,6LAAC;wEAAI,WAAW,CAAC,yBAAyB,EAAE,MAAM,CAAC,MAAM,CAAC,gCAAgC,CAAC;;;;;;oEAC1F;;+DAFM;;;;;;;;;;;;;;;;;;;;;;mCApBT,KAAK,KAAK;;;;;4BA8BxB;;;;;;;;;;;;8BAKJ,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAGlE,6LAAC;gCAAE,WAAU;0CAA+C;;;;;;0CAI5D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAO,WAAU;kDAAmK;;;;;;kDAGrL,6LAAC;wCAAO,WAAU;kDAA6I;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS7K;KAvHgB", "debugId": null}}, {"offset": {"line": 1162, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/templete/pdf-exam-generator/src/components/sections/FeaturesSection.tsx"], "sourcesContent": ["import { Brain, Zap, BarChart3, Users, Shield, Globe, Clock, Target } from 'lucide-react';\n\nexport function FeaturesSection() {\n  const features = [\n    {\n      icon: Brain,\n      title: 'ذكاء اصطناعي متقدم',\n      description: 'تحليل ذكي للمحتوى وإنشاء أسئلة متنوعة ومناسبة لمستوى الطلاب',\n      color: 'bg-blue-500'\n    },\n    {\n      icon: Zap,\n      title: 'سرعة فائقة',\n      description: 'تحويل ملفات PDF إلى اختبارات تفاعلية في دقائق معدودة',\n      color: 'bg-yellow-500'\n    },\n    {\n      icon: BarChart3,\n      title: 'تحليلات شاملة',\n      description: 'تقارير مفصلة عن الأداء والتقدم مع رؤى قابلة للتنفيذ',\n      color: 'bg-green-500'\n    },\n    {\n      icon: Users,\n      title: 'إدارة الفصول',\n      description: 'إدارة سهلة للطلاب والمجموعات مع تتبع التقدم الفردي',\n      color: 'bg-purple-500'\n    },\n    {\n      icon: Shield,\n      title: 'أمان وخصوصية',\n      description: 'حماية عالية للبيانات مع التزام كامل بمعايير الخصوصية',\n      color: 'bg-red-500'\n    },\n    {\n      icon: Globe,\n      title: 'دعم متعدد اللغات',\n      description: 'دعم كامل للغة العربية والإنجليزية مع إمكانية التوسع',\n      color: 'bg-indigo-500'\n    },\n    {\n      icon: Clock,\n      title: 'توقيت مرن',\n      description: 'إعدادات زمنية قابلة للتخصيص مع تنبيهات ذكية',\n      color: 'bg-orange-500'\n    },\n    {\n      icon: Target,\n      title: 'دقة عالية',\n      description: 'خوارزميات متطورة لضمان دقة الأسئلة وملاءمتها للمحتوى',\n      color: 'bg-teal-500'\n    }\n  ];\n\n  return (\n    <section className=\"py-20 bg-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-4xl font-bold text-gray-900 mb-4\">\n            مميزات استثنائية لتجربة تعليمية متطورة\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            اكتشف كيف يمكن لمنصتنا أن تحول طريقة إنشاء وإدارة الاختبارات التعليمية\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {features.map((feature, index) => (\n            <div\n              key={index}\n              className=\"group p-6 bg-white rounded-xl border border-gray-200 hover:border-gray-300 hover:shadow-lg transition-all duration-300\"\n            >\n              <div className={`inline-flex items-center justify-center w-12 h-12 ${feature.color} rounded-lg mb-4 group-hover:scale-110 transition-transform duration-300`}>\n                <feature.icon className=\"w-6 h-6 text-white\" />\n              </div>\n              \n              <h3 className=\"text-lg font-semibold text-gray-900 mb-3\">\n                {feature.title}\n              </h3>\n              \n              <p className=\"text-gray-600 text-sm leading-relaxed\">\n                {feature.description}\n              </p>\n            </div>\n          ))}\n        </div>\n\n        {/* Stats Section */}\n        <div className=\"mt-20 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 text-white\">\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8 text-center\">\n            <div>\n              <div className=\"text-4xl font-bold mb-2\">10,000+</div>\n              <div className=\"text-blue-100\">اختبار تم إنشاؤه</div>\n            </div>\n            <div>\n              <div className=\"text-4xl font-bold mb-2\">50,000+</div>\n              <div className=\"text-blue-100\">طالب نشط</div>\n            </div>\n            <div>\n              <div className=\"text-4xl font-bold mb-2\">1,200+</div>\n              <div className=\"text-blue-100\">معلم ومدرب</div>\n            </div>\n            <div>\n              <div className=\"text-4xl font-bold mb-2\">98%</div>\n              <div className=\"text-blue-100\">معدل الرضا</div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAEO,SAAS;IACd,MAAM,WAAW;QACf;YACE,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,MAAM,mMAAA,CAAA,MAAG;YACT,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,MAAM,qNAAA,CAAA,YAAS;YACf,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,MAAM,yMAAA,CAAA,SAAM;YACZ,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,MAAM,yMAAA,CAAA,SAAM;YACZ,OAAO;YACP,aAAa;YACb,OAAO;QACT;KACD;IAED,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAKzD,6LAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC;4BAEC,WAAU;;8CAEV,6LAAC;oCAAI,WAAW,CAAC,kDAAkD,EAAE,QAAQ,KAAK,CAAC,wEAAwE,CAAC;8CAC1J,cAAA,6LAAC,QAAQ,IAAI;wCAAC,WAAU;;;;;;;;;;;8CAG1B,6LAAC;oCAAG,WAAU;8CACX,QAAQ,KAAK;;;;;;8CAGhB,6LAAC;oCAAE,WAAU;8CACV,QAAQ,WAAW;;;;;;;2BAZjB;;;;;;;;;;8BAmBX,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAI,WAAU;kDAA0B;;;;;;kDACzC,6LAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;0CAEjC,6LAAC;;kDACC,6LAAC;wCAAI,WAAU;kDAA0B;;;;;;kDACzC,6LAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;0CAEjC,6LAAC;;kDACC,6LAAC;wCAAI,WAAU;kDAA0B;;;;;;kDACzC,6LAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;0CAEjC,6LAAC;;kDACC,6LAAC;wCAAI,WAAU;kDAA0B;;;;;;kDACzC,6LAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO7C;KA7GgB", "debugId": null}}, {"offset": {"line": 1439, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/templete/pdf-exam-generator/src/components/sections/CTASection.tsx"], "sourcesContent": ["import Link from 'next/link';\nimport { ArrowLeft, CheckCircle } from 'lucide-react';\n\nexport function CTASection() {\n  const benefits = [\n    'إنشاء اختبارات غير محدودة',\n    'تحليلات متقدمة ومفصلة',\n    'دعم فني على مدار الساعة',\n    'تحديثات مجانية مدى الحياة',\n    'أمان وخصوصية عالية',\n    'واجهة سهلة الاستخدام'\n  ];\n\n  return (\n    <section className=\"py-20 bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 text-white relative overflow-hidden\">\n      {/* Background Pattern */}\n      <div className=\"absolute inset-0 opacity-10\">\n        <div className=\"absolute inset-0\" style={{\n          backgroundImage: `url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")`,\n        }} />\n      </div>\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">\n          {/* Left Content */}\n          <div>\n            <h2 className=\"text-4xl lg:text-5xl font-bold mb-6 leading-tight\">\n              ابدأ رحلتك التعليمية\n              <span className=\"block text-blue-300\">المتطورة اليوم</span>\n            </h2>\n            \n            <p className=\"text-xl text-gray-300 mb-8 leading-relaxed\">\n              انضم إلى آلاف المعلمين والطلاب الذين يستخدمون منصتنا لتحسين تجربة التعلم والتقييم\n            </p>\n\n            <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4 mb-8\">\n              {benefits.map((benefit, index) => (\n                <div key={index} className=\"flex items-center space-x-3 rtl:space-x-reverse\">\n                  <CheckCircle className=\"w-5 h-5 text-green-400 flex-shrink-0\" />\n                  <span className=\"text-gray-300\">{benefit}</span>\n                </div>\n              ))}\n            </div>\n\n            <div className=\"flex flex-col sm:flex-row gap-4\">\n              <Link\n                href=\"/login\"\n                className=\"inline-flex items-center justify-center px-8 py-4 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-colors group\"\n              >\n                ابدأ الآن مجاناً\n                <ArrowLeft className=\"w-5 h-5 mr-2 group-hover:translate-x-1 transition-transform\" />\n              </Link>\n              \n              <button className=\"inline-flex items-center justify-center px-8 py-4 border-2 border-white text-white font-semibold rounded-lg hover:bg-white hover:text-gray-900 transition-colors\">\n                شاهد العرض التوضيحي\n              </button>\n            </div>\n          </div>\n\n          {/* Right Content - Feature Highlights */}\n          <div className=\"relative\">\n            <div className=\"bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20\">\n              <h3 className=\"text-2xl font-bold mb-6 text-center\">ما يميزنا</h3>\n              \n              <div className=\"space-y-6\">\n                <div className=\"flex items-start space-x-4 rtl:space-x-reverse\">\n                  <div className=\"w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center flex-shrink-0\">\n                    <span className=\"text-white font-bold\">AI</span>\n                  </div>\n                  <div>\n                    <h4 className=\"font-semibold mb-2\">ذكاء اصطناعي متطور</h4>\n                    <p className=\"text-gray-300 text-sm\">تحليل ذكي للمحتوى وإنشاء أسئلة عالية الجودة</p>\n                  </div>\n                </div>\n\n                <div className=\"flex items-start space-x-4 rtl:space-x-reverse\">\n                  <div className=\"w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center flex-shrink-0\">\n                    <span className=\"text-white font-bold\">⚡</span>\n                  </div>\n                  <div>\n                    <h4 className=\"font-semibold mb-2\">سرعة استثنائية</h4>\n                    <p className=\"text-gray-300 text-sm\">تحويل المحتوى إلى اختبارات في دقائق</p>\n                  </div>\n                </div>\n\n                <div className=\"flex items-start space-x-4 rtl:space-x-reverse\">\n                  <div className=\"w-12 h-12 bg-purple-500 rounded-lg flex items-center justify-center flex-shrink-0\">\n                    <span className=\"text-white font-bold\">📊</span>\n                  </div>\n                  <div>\n                    <h4 className=\"font-semibold mb-2\">تحليلات شاملة</h4>\n                    <p className=\"text-gray-300 text-sm\">تقارير مفصلة ورؤى قابلة للتنفيذ</p>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"mt-8 p-4 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-lg border border-blue-400/30\">\n                <div className=\"text-center\">\n                  <div className=\"text-3xl font-bold text-blue-300 mb-1\">مجاني</div>\n                  <div className=\"text-sm text-gray-300\">للاستخدام الأساسي</div>\n                </div>\n              </div>\n            </div>\n\n            {/* Floating Elements */}\n            <div className=\"absolute -top-4 -right-4 w-24 h-24 bg-blue-500/20 rounded-full blur-xl\"></div>\n            <div className=\"absolute -bottom-4 -left-4 w-32 h-32 bg-purple-500/20 rounded-full blur-xl\"></div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;;;;AAEO,SAAS;IACd,MAAM,WAAW;QACf;QACA;QACA;QACA;QACA;QACA;KACD;IAED,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;oBAAmB,OAAO;wBACvC,iBAAiB,CAAC,gQAAgQ,CAAC;oBACrR;;;;;;;;;;;0BAGF,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;;wCAAoD;sDAEhE,6LAAC;4CAAK,WAAU;sDAAsB;;;;;;;;;;;;8CAGxC,6LAAC;oCAAE,WAAU;8CAA6C;;;;;;8CAI1D,6LAAC;oCAAI,WAAU;8CACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC;4CAAgB,WAAU;;8DACzB,6LAAC,8NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;8DACvB,6LAAC;oDAAK,WAAU;8DAAiB;;;;;;;2CAFzB;;;;;;;;;;8CAOd,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;;gDACX;8DAEC,6LAAC,mNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;;;;;;;sDAGvB,6LAAC;4CAAO,WAAU;sDAAmK;;;;;;;;;;;;;;;;;;sCAOzL,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAsC;;;;;;sDAEpD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAK,WAAU;0EAAuB;;;;;;;;;;;sEAEzC,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAAqB;;;;;;8EACnC,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;8DAIzC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAK,WAAU;0EAAuB;;;;;;;;;;;sEAEzC,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAAqB;;;;;;8EACnC,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;8DAIzC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAK,WAAU;0EAAuB;;;;;;;;;;;sEAEzC,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAAqB;;;;;;8EACnC,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;;;;;;;sDAK3C,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAwC;;;;;;kEACvD,6LAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;;;;;;8CAM7C,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM3B;KA7GgB", "debugId": null}}, {"offset": {"line": 1838, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/templete/pdf-exam-generator/src/components/pages/LandingPage.tsx"], "sourcesContent": ["import { HeroSection } from '@/components/sections/HeroSection';\nimport { HowItWorksSection } from '@/components/sections/HowItWorksSection';\nimport { FeaturesSection } from '@/components/sections/FeaturesSection';\nimport { CTASection } from '@/components/sections/CTASection';\n\nexport function LandingPage() {\n  return (\n    <div className=\"min-h-screen\">\n      <HeroSection />\n      <FeaturesSection />\n      <HowItWorksSection />\n      <CTASection />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;;AAEO,SAAS;IACd,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,gJAAA,CAAA,cAAW;;;;;0BACZ,6LAAC,oJAAA,CAAA,kBAAe;;;;;0BAChB,6LAAC,sJAAA,CAAA,oBAAiB;;;;;0BAClB,6LAAC,+IAAA,CAAA,aAAU;;;;;;;;;;;AAGjB;KATgB", "debugId": null}}, {"offset": {"line": 1894, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/templete/pdf-exam-generator/src/components/ui/LoadingSpinner.tsx"], "sourcesContent": ["interface LoadingSpinnerProps {\n  size?: 'sm' | 'md' | 'lg';\n  color?: 'blue' | 'green' | 'gray';\n  text?: string;\n  fullScreen?: boolean;\n}\n\nexport default function LoadingSpinner({ \n  size = 'md', \n  color = 'blue', \n  text,\n  fullScreen = false \n}: LoadingSpinnerProps) {\n  const sizeClasses = {\n    sm: 'h-4 w-4',\n    md: 'h-8 w-8',\n    lg: 'h-12 w-12'\n  };\n\n  const colorClasses = {\n    blue: 'border-blue-600',\n    green: 'border-green-600',\n    gray: 'border-gray-600'\n  };\n\n  const spinner = (\n    <div className=\"flex flex-col items-center justify-center\">\n      <div\n        className={`animate-spin rounded-full border-2 border-gray-200 ${colorClasses[color]} border-t-transparent ${sizeClasses[size]}`}\n      />\n      {text && (\n        <p className=\"mt-2 text-sm text-gray-600\">{text}</p>\n      )}\n    </div>\n  );\n\n  if (fullScreen) {\n    return (\n      <div className=\"fixed inset-0 bg-white bg-opacity-75 flex items-center justify-center z-50\">\n        {spinner}\n      </div>\n    );\n  }\n\n  return spinner;\n}\n"], "names": [], "mappings": ";;;;;AAOe,SAAS,eAAe,EACrC,OAAO,IAAI,EACX,QAAQ,MAAM,EACd,IAAI,EACJ,aAAa,KAAK,EACE;IACpB,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,eAAe;QACnB,MAAM;QACN,OAAO;QACP,MAAM;IACR;IAEA,MAAM,wBACJ,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACC,WAAW,CAAC,mDAAmD,EAAE,YAAY,CAAC,MAAM,CAAC,sBAAsB,EAAE,WAAW,CAAC,KAAK,EAAE;;;;;;YAEjI,sBACC,6LAAC;gBAAE,WAAU;0BAA8B;;;;;;;;;;;;IAKjD,IAAI,YAAY;QACd,qBACE,6LAAC;YAAI,WAAU;sBACZ;;;;;;IAGP;IAEA,OAAO;AACT;KAtCwB", "debugId": null}}, {"offset": {"line": 1958, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/templete/pdf-exam-generator/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { LandingPage } from '@/components/pages/LandingPage';\nimport LoadingSpinner from '@/components/ui/LoadingSpinner';\n\nexport default function Home() {\n  const { isAuthenticated, loading } = useAuth();\n  const router = useRouter();\n\n  useEffect(() => {\n    if (!loading && isAuthenticated) {\n      router.push('/dashboard');\n    }\n  }, [isAuthenticated, loading, router]);\n\n  if (loading) {\n    return <LoadingSpinner fullScreen text=\"جاري التحميل...\" />;\n  }\n\n  if (isAuthenticated) {\n    return <LoadingSpinner fullScreen text=\"جاري التوجيه إلى لوحة التحكم...\" />;\n  }\n\n  return <LandingPage />;\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAQe,SAAS;;IACtB,MAAM,EAAE,eAAe,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,IAAI,CAAC,WAAW,iBAAiB;gBAC/B,OAAO,IAAI,CAAC;YACd;QACF;yBAAG;QAAC;QAAiB;QAAS;KAAO;IAErC,IAAI,SAAS;QACX,qBAAO,6LAAC,6IAAA,CAAA,UAAc;YAAC,UAAU;YAAC,MAAK;;;;;;IACzC;IAEA,IAAI,iBAAiB;QACnB,qBAAO,6LAAC,6IAAA,CAAA,UAAc;YAAC,UAAU;YAAC,MAAK;;;;;;IACzC;IAEA,qBAAO,6LAAC,6IAAA,CAAA,cAAW;;;;;AACrB;GAnBwB;;QACe,kIAAA,CAAA,UAAO;QAC7B,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}]}