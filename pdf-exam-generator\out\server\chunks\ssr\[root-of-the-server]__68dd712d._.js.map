{"version": 3, "sources": [], "sections": [{"offset": {"line": 135, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/templete/pdf-exam-generator/src/lib/firebase.ts"], "sourcesContent": ["import { initializeApp, getApps } from 'firebase/app';\nimport { getAuth, GoogleAuthProvider, FacebookAuthProvider } from 'firebase/auth';\nimport { getFirestore } from 'firebase/firestore';\nimport { getStorage } from 'firebase/storage';\n\nconst firebaseConfig = {\n  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY || 'demo-key',\n  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN || 'demo-project.firebaseapp.com',\n  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID || 'demo-project',\n  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET || 'demo-project.appspot.com',\n  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID || '123456789',\n  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID || '1:123456789:web:abcdef',\n  measurementId: process.env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID || 'G-ABCDEF'\n};\n\n// Initialize Firebase only if it hasn't been initialized\nconst app = getApps().length === 0 ? initializeApp(firebaseConfig) : getApps()[0];\n\n// Initialize Firebase Authentication and get a reference to the service\nexport const auth = getAuth(app);\n\n// Initialize Cloud Firestore and get a reference to the service\nexport const db = getFirestore(app);\n\n// Initialize Cloud Storage and get a reference to the service\nexport const storage = getStorage(app);\n\n// Initialize providers\nexport const googleProvider = new GoogleAuthProvider();\nexport const facebookProvider = new FacebookAuthProvider();\n\n// Configure providers\ngoogleProvider.setCustomParameters({\n  prompt: 'select_account'\n});\n\nfacebookProvider.setCustomParameters({\n  display: 'popup'\n});\n\nexport default app;\n"], "names": [], "mappings": ";;;;;;;;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AAAA;;;;;AAEA,MAAM,iBAAiB;IACrB,QAAQ,wCAA4C;IACpD,YAAY,wCAAgD;IAC5D,WAAW,wCAA+C;IAC1D,eAAe,wCAAmD;IAClE,mBAAmB,wCAAwD;IAC3E,OAAO,wCAA2C;IAClD,eAAe,QAAQ,GAAG,CAAC,mCAAmC,IAAI;AACpE;AAEA,yDAAyD;AACzD,MAAM,MAAM,CAAA,GAAA,oLAAA,CAAA,UAAO,AAAD,IAAI,MAAM,KAAK,IAAI,CAAA,GAAA,oLAAA,CAAA,gBAAa,AAAD,EAAE,kBAAkB,CAAA,GAAA,oLAAA,CAAA,UAAO,AAAD,GAAG,CAAC,EAAE;AAG1E,MAAM,OAAO,CAAA,GAAA,6MAAA,CAAA,UAAO,AAAD,EAAE;AAGrB,MAAM,KAAK,CAAA,GAAA,iKAAA,CAAA,eAAY,AAAD,EAAE;AAGxB,MAAM,UAAU,CAAA,GAAA,oLAAA,CAAA,aAAU,AAAD,EAAE;AAG3B,MAAM,iBAAiB,IAAI,wNAAA,CAAA,qBAAkB;AAC7C,MAAM,mBAAmB,IAAI,0NAAA,CAAA,uBAAoB;AAExD,sBAAsB;AACtB,eAAe,mBAAmB,CAAC;IACjC,QAAQ;AACV;AAEA,iBAAiB,mBAAmB,CAAC;IACnC,SAAS;AACX;uCAEe", "debugId": null}}, {"offset": {"line": 187, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/templete/pdf-exam-generator/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';\nimport {\n  User as FirebaseUser,\n  signInWithEmailAndPassword,\n  createUserWithEmailAndPassword,\n  signOut,\n  onAuthStateChanged,\n  signInWithPopup,\n  sendPasswordResetEmail,\n  updateProfile,\n  GoogleAuthProvider,\n  FacebookAuthProvider\n} from 'firebase/auth';\nimport { doc, setDoc, getDoc } from 'firebase/firestore';\nimport { auth, db, googleProvider, facebookProvider } from '@/lib/firebase';\n\ninterface UserProfile {\n  uid: string;\n  email: string;\n  displayName: string;\n  photoURL?: string;\n  role: 'teacher' | 'student' | 'admin';\n  createdAt: Date;\n  lastLoginAt: Date;\n  preferences: {\n    language: 'ar' | 'en';\n    theme: 'light' | 'dark';\n    notifications: boolean;\n  };\n  stats: {\n    totalExams: number;\n    averageScore: number;\n    totalQuestions: number;\n    studyTime: number; // in minutes\n  };\n}\n\ninterface AuthContextType {\n  user: FirebaseUser | null;\n  userProfile: UserProfile | null;\n  loading: boolean;\n  isAuthenticated: boolean;\n  signIn: (email: string, password: string) => Promise<void>;\n  signUp: (email: string, password: string, displayName: string, role: 'teacher' | 'student') => Promise<void>;\n  signInWithGoogle: () => Promise<void>;\n  signInWithFacebook: () => Promise<void>;\n  logout: () => Promise<void>;\n  resetPassword: (email: string) => Promise<void>;\n  updateUserProfile: (data: Partial<UserProfile>) => Promise<void>;\n  // Legacy compatibility\n  login: (email: string, password: string) => Promise<boolean>;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {\n  const [user, setUser] = useState<FirebaseUser | null>(null);\n  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);\n  const [loading, setLoading] = useState(true);\n\n  // Create or update user profile in Firestore\n  const createUserProfile = async (user: FirebaseUser, additionalData: any = {}) => {\n    if (!user) return;\n\n    const userRef = doc(db, 'users', user.uid);\n    const userSnap = await getDoc(userRef);\n\n    if (!userSnap.exists()) {\n      const { displayName, email, photoURL } = user;\n      const createdAt = new Date();\n\n      const defaultProfile: UserProfile = {\n        uid: user.uid,\n        email: email || '',\n        displayName: displayName || '',\n        photoURL: photoURL || undefined,\n        role: additionalData.role || 'student',\n        createdAt,\n        lastLoginAt: createdAt,\n        preferences: {\n          language: 'ar',\n          theme: 'light',\n          notifications: true\n        },\n        stats: {\n          totalExams: 0,\n          averageScore: 0,\n          totalQuestions: 0,\n          studyTime: 0\n        },\n        ...additionalData\n      };\n\n      try {\n        await setDoc(userRef, defaultProfile);\n        setUserProfile(defaultProfile);\n      } catch (error) {\n        console.error('Error creating user profile:', error);\n      }\n    } else {\n      // Update last login time\n      const existingProfile = userSnap.data() as UserProfile;\n      const updatedProfile = {\n        ...existingProfile,\n        lastLoginAt: new Date()\n      };\n\n      await setDoc(userRef, updatedProfile, { merge: true });\n      setUserProfile(updatedProfile);\n    }\n  };\n\n  // Sign in with email and password\n  const signIn = async (email: string, password: string) => {\n    setLoading(true);\n    try {\n      const result = await signInWithEmailAndPassword(auth, email, password);\n      await createUserProfile(result.user);\n    } catch (error) {\n      console.error('Error signing in:', error);\n      throw error;\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Legacy login function for compatibility\n  const login = async (email: string, password: string): Promise<boolean> => {\n    try {\n      await signIn(email, password);\n      return true;\n    } catch (error) {\n      return false;\n    }\n  };\n\n  // Sign up with email and password\n  const signUp = async (email: string, password: string, displayName: string, role: 'teacher' | 'student') => {\n    setLoading(true);\n    try {\n      const result = await createUserWithEmailAndPassword(auth, email, password);\n\n      // Update display name\n      await updateProfile(result.user, { displayName });\n\n      // Create user profile\n      await createUserProfile(result.user, { role, displayName });\n    } catch (error) {\n      console.error('Error signing up:', error);\n      throw error;\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Sign in with Google\n  const signInWithGoogle = async () => {\n    setLoading(true);\n    try {\n      const result = await signInWithPopup(auth, googleProvider);\n      await createUserProfile(result.user);\n    } catch (error) {\n      console.error('Error signing in with Google:', error);\n      throw error;\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Sign in with Facebook\n  const signInWithFacebook = async () => {\n    setLoading(true);\n    try {\n      const result = await signInWithPopup(auth, facebookProvider);\n      await createUserProfile(result.user);\n    } catch (error) {\n      console.error('Error signing in with Facebook:', error);\n      throw error;\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Logout\n  const logout = async () => {\n    setLoading(true);\n    try {\n      await signOut(auth);\n      setUser(null);\n      setUserProfile(null);\n      // Clear all exam data\n      localStorage.removeItem('examResults');\n      localStorage.removeItem('examAnswers');\n      localStorage.removeItem('uploadedPDF');\n      localStorage.removeItem('examConfig');\n    } catch (error) {\n      console.error('Error signing out:', error);\n      throw error;\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Reset password\n  const resetPassword = async (email: string) => {\n    try {\n      await sendPasswordResetEmail(auth, email);\n    } catch (error) {\n      console.error('Error sending password reset email:', error);\n      throw error;\n    }\n  };\n\n  // Update user profile\n  const updateUserProfile = async (data: Partial<UserProfile>) => {\n    if (!user) return;\n\n    try {\n      const userRef = doc(db, 'users', user.uid);\n      await setDoc(userRef, data, { merge: true });\n\n      if (userProfile) {\n        setUserProfile({ ...userProfile, ...data });\n      }\n    } catch (error) {\n      console.error('Error updating user profile:', error);\n      throw error;\n    }\n  };\n\n  // Listen for authentication state changes\n  useEffect(() => {\n    const unsubscribe = onAuthStateChanged(auth, async (user) => {\n      if (user) {\n        setUser(user);\n        await createUserProfile(user);\n      } else {\n        setUser(null);\n        setUserProfile(null);\n      }\n      setLoading(false);\n    });\n\n    return unsubscribe;\n  }, []);\n\n  const value: AuthContextType = {\n    user,\n    userProfile,\n    loading,\n    isAuthenticated: !!user,\n    signIn,\n    signUp,\n    signInWithGoogle,\n    signInWithFacebook,\n    logout,\n    resetPassword,\n    updateUserProfile,\n    login // Legacy compatibility\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AAAA;AACA;AAhBA;;;;;;AAuDA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,MAAM,UAAU;IACrB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAEO,MAAM,eAAkD,CAAC,EAAE,QAAQ,EAAE;IAC1E,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IACtD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IACnE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,6CAA6C;IAC7C,MAAM,oBAAoB,OAAO,MAAoB,iBAAsB,CAAC,CAAC;QAC3E,IAAI,CAAC,MAAM;QAEX,MAAM,UAAU,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,SAAS,KAAK,GAAG;QACzC,MAAM,WAAW,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE;QAE9B,IAAI,CAAC,SAAS,MAAM,IAAI;YACtB,MAAM,EAAE,WAAW,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG;YACzC,MAAM,YAAY,IAAI;YAEtB,MAAM,iBAA8B;gBAClC,KAAK,KAAK,GAAG;gBACb,OAAO,SAAS;gBAChB,aAAa,eAAe;gBAC5B,UAAU,YAAY;gBACtB,MAAM,eAAe,IAAI,IAAI;gBAC7B;gBACA,aAAa;gBACb,aAAa;oBACX,UAAU;oBACV,OAAO;oBACP,eAAe;gBACjB;gBACA,OAAO;oBACL,YAAY;oBACZ,cAAc;oBACd,gBAAgB;oBAChB,WAAW;gBACb;gBACA,GAAG,cAAc;YACnB;YAEA,IAAI;gBACF,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,SAAS;gBACtB,eAAe;YACjB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,gCAAgC;YAChD;QACF,OAAO;YACL,yBAAyB;YACzB,MAAM,kBAAkB,SAAS,IAAI;YACrC,MAAM,iBAAiB;gBACrB,GAAG,eAAe;gBAClB,aAAa,IAAI;YACnB;YAEA,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,SAAS,gBAAgB;gBAAE,OAAO;YAAK;YACpD,eAAe;QACjB;IACF;IAEA,kCAAkC;IAClC,MAAM,SAAS,OAAO,OAAe;QACnC,WAAW;QACX,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,iOAAA,CAAA,6BAA0B,AAAD,EAAE,sHAAA,CAAA,OAAI,EAAE,OAAO;YAC7D,MAAM,kBAAkB,OAAO,IAAI;QACrC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,0CAA0C;IAC1C,MAAM,QAAQ,OAAO,OAAe;QAClC,IAAI;YACF,MAAM,OAAO,OAAO;YACpB,OAAO;QACT,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;IAEA,kCAAkC;IAClC,MAAM,SAAS,OAAO,OAAe,UAAkB,aAAqB;QAC1E,WAAW;QACX,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,qOAAA,CAAA,iCAA8B,AAAD,EAAE,sHAAA,CAAA,OAAI,EAAE,OAAO;YAEjE,sBAAsB;YACtB,MAAM,CAAA,GAAA,oNAAA,CAAA,gBAAa,AAAD,EAAE,OAAO,IAAI,EAAE;gBAAE;YAAY;YAE/C,sBAAsB;YACtB,MAAM,kBAAkB,OAAO,IAAI,EAAE;gBAAE;gBAAM;YAAY;QAC3D,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,sBAAsB;IACtB,MAAM,mBAAmB;QACvB,WAAW;QACX,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,qNAAA,CAAA,kBAAe,AAAD,EAAE,sHAAA,CAAA,OAAI,EAAE,sHAAA,CAAA,iBAAc;YACzD,MAAM,kBAAkB,OAAO,IAAI;QACrC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,wBAAwB;IACxB,MAAM,qBAAqB;QACzB,WAAW;QACX,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,qNAAA,CAAA,kBAAe,AAAD,EAAE,sHAAA,CAAA,OAAI,EAAE,sHAAA,CAAA,mBAAgB;YAC3D,MAAM,kBAAkB,OAAO,IAAI;QACrC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,SAAS;IACT,MAAM,SAAS;QACb,WAAW;QACX,IAAI;YACF,MAAM,CAAA,GAAA,6MAAA,CAAA,UAAO,AAAD,EAAE,sHAAA,CAAA,OAAI;YAClB,QAAQ;YACR,eAAe;YACf,sBAAsB;YACtB,aAAa,UAAU,CAAC;YACxB,aAAa,UAAU,CAAC;YACxB,aAAa,UAAU,CAAC;YACxB,aAAa,UAAU,CAAC;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;YACpC,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,iBAAiB;IACjB,MAAM,gBAAgB,OAAO;QAC3B,IAAI;YACF,MAAM,CAAA,GAAA,6NAAA,CAAA,yBAAsB,AAAD,EAAE,sHAAA,CAAA,OAAI,EAAE;QACrC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;YACrD,MAAM;QACR;IACF;IAEA,sBAAsB;IACtB,MAAM,oBAAoB,OAAO;QAC/B,IAAI,CAAC,MAAM;QAEX,IAAI;YACF,MAAM,UAAU,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,SAAS,KAAK,GAAG;YACzC,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,SAAS,MAAM;gBAAE,OAAO;YAAK;YAE1C,IAAI,aAAa;gBACf,eAAe;oBAAE,GAAG,WAAW;oBAAE,GAAG,IAAI;gBAAC;YAC3C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,MAAM;QACR;IACF;IAEA,0CAA0C;IAC1C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,cAAc,CAAA,GAAA,wNAAA,CAAA,qBAAkB,AAAD,EAAE,sHAAA,CAAA,OAAI,EAAE,OAAO;YAClD,IAAI,MAAM;gBACR,QAAQ;gBACR,MAAM,kBAAkB;YAC1B,OAAO;gBACL,QAAQ;gBACR,eAAe;YACjB;YACA,WAAW;QACb;QAEA,OAAO;IACT,GAAG,EAAE;IAEL,MAAM,QAAyB;QAC7B;QACA;QACA;QACA,iBAAiB,CAAC,CAAC;QACnB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 453, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/templete/pdf-exam-generator/src/components/layout/Navbar.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { useState } from 'react';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { Menu, X, FileText, Settings, Home, BarChart3, User, LogOut, BookOpen } from 'lucide-react';\n\nexport function Navbar() {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [showUserMenu, setShowUserMenu] = useState(false);\n  const pathname = usePathname();\n  const { user, logout } = useAuth();\n\n  const navigation = user ? [\n    { name: 'لوحة التحكم', href: '/dashboard', icon: Home },\n    { name: 'رفع ملف', href: '/dashboard/upload', icon: FileText },\n    { name: 'السجل', href: '/dashboard/history', icon: BarChart3 },\n    { name: 'الإعدادات', href: '/settings', icon: Settings },\n  ] : [\n    { name: 'الرئيسية', href: '/', icon: Home },\n  ];\n\n  const handleLogout = () => {\n    logout();\n    setShowUserMenu(false);\n  };\n\n  const isActive = (href: string) => {\n    if (href === '/') {\n      return pathname === '/';\n    }\n    return pathname.startsWith(href);\n  };\n\n  return (\n    <nav className=\"bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between h-16\">\n          {/* Logo and brand */}\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"flex items-center space-x-2 rtl:space-x-reverse\">\n              <div className=\"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n                <BookOpen className=\"w-5 h-5 text-white\" />\n              </div>\n              <span className=\"text-xl font-bold text-gray-900 hidden sm:block\">\n                منصة الاختبارات التفاعلية\n              </span>\n              <span className=\"text-xl font-bold text-gray-900 sm:hidden\">\n                الاختبارات\n              </span>\n            </Link>\n          </div>\n\n          {/* Desktop navigation */}\n          <div className=\"hidden md:flex items-center space-x-8 rtl:space-x-reverse\">\n            {navigation.map((item) => {\n              const Icon = item.icon;\n              return (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className={`flex items-center space-x-1 rtl:space-x-reverse px-3 py-2 rounded-md text-sm font-medium transition-colors ${\n                    isActive(item.href)\n                      ? 'text-blue-600 bg-blue-50'\n                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'\n                  }`}\n                >\n                  <Icon className=\"w-4 h-4\" />\n                  <span>{item.name}</span>\n                </Link>\n              );\n            })}\n\n            {/* User Menu */}\n            {user ? (\n              <div className=\"relative\">\n                <button\n                  onClick={() => setShowUserMenu(!showUserMenu)}\n                  className=\"flex items-center space-x-2 rtl:space-x-reverse px-3 py-2 rounded-md text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50\"\n                >\n                  <div className=\"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center\">\n                    {user.photoURL ? (\n                      <img src={user.photoURL} alt={user?.displayName || 'User'} className=\"w-8 h-8 rounded-full object-cover\" />\n                    ) : (\n                      <User className=\"w-4 h-4 text-white\" />\n                    )}\n                  </div>\n                  <span>{user?.displayName || user?.email?.split('@')[0] || 'المستخدم'}</span>\n                </button>\n\n                {showUserMenu && (\n                  <div className=\"absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-50\">\n                    <div className=\"py-1\">\n                      <Link\n                        href=\"/dashboard\"\n                        className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50\"\n                        onClick={() => setShowUserMenu(false)}\n                      >\n                        لوحة التحكم\n                      </Link>\n                      <Link\n                        href=\"/settings\"\n                        className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50\"\n                        onClick={() => setShowUserMenu(false)}\n                      >\n                        الإعدادات\n                      </Link>\n                      <button\n                        onClick={handleLogout}\n                        className=\"w-full text-right block px-4 py-2 text-sm text-red-700 hover:bg-red-50\"\n                      >\n                        تسجيل الخروج\n                      </button>\n                    </div>\n                  </div>\n                )}\n              </div>\n            ) : (\n              <Link\n                href=\"/login\"\n                className=\"bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 transition-colors\"\n              >\n                تسجيل الدخول\n              </Link>\n            )}\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden flex items-center\">\n            <button\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n              className=\"p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              aria-label=\"Toggle menu\"\n            >\n              {isMenuOpen ? (\n                <X className=\"w-6 h-6\" />\n              ) : (\n                <Menu className=\"w-6 h-6\" />\n              )}\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile menu */}\n      {isMenuOpen && (\n        <div className=\"md:hidden border-t border-gray-200 bg-white\">\n          <div className=\"px-2 pt-2 pb-3 space-y-1\">\n            {navigation.map((item) => {\n              const Icon = item.icon;\n              return (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  onClick={() => setIsMenuOpen(false)}\n                  className={`flex items-center space-x-2 rtl:space-x-reverse px-3 py-2 rounded-md text-base font-medium transition-colors ${\n                    isActive(item.href)\n                      ? 'text-blue-600 bg-blue-50'\n                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'\n                  }`}\n                >\n                  <Icon className=\"w-5 h-5\" />\n                  <span>{item.name}</span>\n                </Link>\n              );\n            })}\n\n            {/* Mobile User Menu */}\n            {user ? (\n              <div className=\"border-t border-gray-200 pt-3 mt-3\">\n                <div className=\"flex items-center px-3 py-2 mb-2\">\n                  <div className=\"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center ml-3\">\n                    {user.photoURL ? (\n                      <img src={user.photoURL} alt={user?.displayName || 'User'} className=\"w-8 h-8 rounded-full object-cover\" />\n                    ) : (\n                      <User className=\"w-4 h-4 text-white\" />\n                    )}\n                  </div>\n                  <div>\n                    <div className=\"text-sm font-medium text-gray-900\">{user?.displayName || user?.email?.split('@')[0] || 'المستخدم'}</div>\n                    <div className=\"text-xs text-gray-500 capitalize\">مستخدم</div>\n                  </div>\n                </div>\n                <Link\n                  href=\"/dashboard\"\n                  onClick={() => setIsMenuOpen(false)}\n                  className=\"flex items-center space-x-2 rtl:space-x-reverse px-3 py-2 rounded-md text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50\"\n                >\n                  <BarChart3 className=\"w-5 h-5\" />\n                  <span>لوحة التحكم</span>\n                </Link>\n                <button\n                  onClick={() => {\n                    handleLogout();\n                    setIsMenuOpen(false);\n                  }}\n                  className=\"w-full flex items-center space-x-2 rtl:space-x-reverse px-3 py-2 rounded-md text-base font-medium text-red-600 hover:text-red-900 hover:bg-red-50\"\n                >\n                  <LogOut className=\"w-5 h-5\" />\n                  <span>تسجيل الخروج</span>\n                </button>\n              </div>\n            ) : (\n              <div className=\"border-t border-gray-200 pt-3 mt-3\">\n                <Link\n                  href=\"/login\"\n                  onClick={() => setIsMenuOpen(false)}\n                  className=\"block px-3 py-2 rounded-md text-base font-medium bg-blue-600 text-white hover:bg-blue-700 text-center\"\n                >\n                  تسجيل الدخول\n                </Link>\n              </div>\n            )}\n          </div>\n        </div>\n      )}\n    </nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AANA;;;;;;;AAQO,SAAS;IACd,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAE/B,MAAM,aAAa,OAAO;QACxB;YAAE,MAAM;YAAe,MAAM;YAAc,MAAM,mMAAA,CAAA,OAAI;QAAC;QACtD;YAAE,MAAM;YAAW,MAAM;YAAqB,MAAM,8MAAA,CAAA,WAAQ;QAAC;QAC7D;YAAE,MAAM;YAAS,MAAM;YAAsB,MAAM,kNAAA,CAAA,YAAS;QAAC;QAC7D;YAAE,MAAM;YAAa,MAAM;YAAa,MAAM,0MAAA,CAAA,WAAQ;QAAC;KACxD,GAAG;QACF;YAAE,MAAM;YAAY,MAAM;YAAK,MAAM,mMAAA,CAAA,OAAI;QAAC;KAC3C;IAED,MAAM,eAAe;QACnB;QACA,gBAAgB;IAClB;IAEA,MAAM,WAAW,CAAC;QAChB,IAAI,SAAS,KAAK;YAChB,OAAO,aAAa;QACtB;QACA,OAAO,SAAS,UAAU,CAAC;IAC7B;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAEtB,8OAAC;wCAAK,WAAU;kDAAkD;;;;;;kDAGlE,8OAAC;wCAAK,WAAU;kDAA4C;;;;;;;;;;;;;;;;;sCAOhE,8OAAC;4BAAI,WAAU;;gCACZ,WAAW,GAAG,CAAC,CAAC;oCACf,MAAM,OAAO,KAAK,IAAI;oCACtB,qBACE,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAW,CAAC,2GAA2G,EACrH,SAAS,KAAK,IAAI,IACd,6BACA,sDACJ;;0DAEF,8OAAC;gDAAK,WAAU;;;;;;0DAChB,8OAAC;0DAAM,KAAK,IAAI;;;;;;;uCATX,KAAK,IAAI;;;;;gCAYpB;gCAGC,qBACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS,IAAM,gBAAgB,CAAC;4CAChC,WAAU;;8DAEV,8OAAC;oDAAI,WAAU;8DACZ,KAAK,QAAQ,iBACZ,8OAAC;wDAAI,KAAK,KAAK,QAAQ;wDAAE,KAAK,MAAM,eAAe;wDAAQ,WAAU;;;;;6EAErE,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;8DAGpB,8OAAC;8DAAM,MAAM,eAAe,MAAM,OAAO,MAAM,IAAI,CAAC,EAAE,IAAI;;;;;;;;;;;;wCAG3D,8BACC,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,4JAAA,CAAA,UAAI;wDACH,MAAK;wDACL,WAAU;wDACV,SAAS,IAAM,gBAAgB;kEAChC;;;;;;kEAGD,8OAAC,4JAAA,CAAA,UAAI;wDACH,MAAK;wDACL,WAAU;wDACV,SAAS,IAAM,gBAAgB;kEAChC;;;;;;kEAGD,8OAAC;wDACC,SAAS;wDACT,WAAU;kEACX;;;;;;;;;;;;;;;;;;;;;;yDAQT,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;sCAOL,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,SAAS,IAAM,cAAc,CAAC;gCAC9B,WAAU;gCACV,cAAW;0CAEV,2BACC,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;yDAEb,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQzB,4BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;wBACZ,WAAW,GAAG,CAAC,CAAC;4BACf,MAAM,OAAO,KAAK,IAAI;4BACtB,qBACE,8OAAC,4JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,SAAS,IAAM,cAAc;gCAC7B,WAAW,CAAC,6GAA6G,EACvH,SAAS,KAAK,IAAI,IACd,6BACA,sDACJ;;kDAEF,8OAAC;wCAAK,WAAU;;;;;;kDAChB,8OAAC;kDAAM,KAAK,IAAI;;;;;;;+BAVX,KAAK,IAAI;;;;;wBAapB;wBAGC,qBACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACZ,KAAK,QAAQ,iBACZ,8OAAC;gDAAI,KAAK,KAAK,QAAQ;gDAAE,KAAK,MAAM,eAAe;gDAAQ,WAAU;;;;;qEAErE,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAGpB,8OAAC;;8DACC,8OAAC;oDAAI,WAAU;8DAAqC,MAAM,eAAe,MAAM,OAAO,MAAM,IAAI,CAAC,EAAE,IAAI;;;;;;8DACvG,8OAAC;oDAAI,WAAU;8DAAmC;;;;;;;;;;;;;;;;;;8CAGtD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,SAAS,IAAM,cAAc;oCAC7B,WAAU;;sDAEV,8OAAC,kNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;sDACrB,8OAAC;sDAAK;;;;;;;;;;;;8CAER,8OAAC;oCACC,SAAS;wCACP;wCACA,cAAc;oCAChB;oCACA,WAAU;;sDAEV,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;sDAAK;;;;;;;;;;;;;;;;;iDAIV,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,SAAS,IAAM,cAAc;gCAC7B,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB", "debugId": null}}, {"offset": {"line": 941, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/templete/pdf-exam-generator/src/components/layout/Footer.tsx"], "sourcesContent": ["import Link from 'next/link';\nimport { FileText, Mail, Shield, FileCheck } from 'lucide-react';\n\nexport function Footer() {\n  const currentYear = new Date().getFullYear();\n\n  const footerLinks = {\n    product: [\n      { name: 'Features', href: '#features' },\n      { name: 'How it Works', href: '#how-it-works' },\n      { name: 'Pricing', href: '#pricing' },\n      { name: 'API Documentation', href: '#api' },\n    ],\n    legal: [\n      { name: 'Privacy Policy', href: '/privacy' },\n      { name: 'Terms of Service', href: '/terms' },\n      { name: 'Cookie Policy', href: '/cookies' },\n      { name: 'GDPR Compliance', href: '/gdpr' },\n    ],\n    support: [\n      { name: 'Help Center', href: '/help' },\n      { name: 'Contact Us', href: '/contact' },\n      { name: 'Bug Reports', href: '/bugs' },\n      { name: 'Feature Requests', href: '/features' },\n    ],\n  };\n\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {/* Brand section */}\n          <div className=\"lg:col-span-1\">\n            <div className=\"flex items-center space-x-2 mb-4\">\n              <div className=\"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n                <FileText className=\"w-5 h-5 text-white\" />\n              </div>\n              <span className=\"text-xl font-bold\">PDF Exam Generator</span>\n            </div>\n            <p className=\"text-gray-400 text-sm mb-4\">\n              Transform your PDF documents into interactive exams using AI. \n              Perfect for educators, students, and e-learning platforms.\n            </p>\n            <div className=\"flex items-center space-x-2 text-sm text-gray-400\">\n              <Shield className=\"w-4 h-4\" />\n              <span>Secure & Privacy-focused</span>\n            </div>\n          </div>\n\n          {/* Product links */}\n          <div>\n            <h3 className=\"text-sm font-semibold text-gray-300 uppercase tracking-wider mb-4\">\n              Product\n            </h3>\n            <ul className=\"space-y-2\">\n              {footerLinks.product.map((link) => (\n                <li key={link.name}>\n                  <Link\n                    href={link.href}\n                    className=\"text-gray-400 hover:text-white text-sm transition-colors\"\n                  >\n                    {link.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Legal links */}\n          <div>\n            <h3 className=\"text-sm font-semibold text-gray-300 uppercase tracking-wider mb-4\">\n              Legal\n            </h3>\n            <ul className=\"space-y-2\">\n              {footerLinks.legal.map((link) => (\n                <li key={link.name}>\n                  <Link\n                    href={link.href}\n                    className=\"text-gray-400 hover:text-white text-sm transition-colors\"\n                  >\n                    {link.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Support links */}\n          <div>\n            <h3 className=\"text-sm font-semibold text-gray-300 uppercase tracking-wider mb-4\">\n              Support\n            </h3>\n            <ul className=\"space-y-2\">\n              {footerLinks.support.map((link) => (\n                <li key={link.name}>\n                  <Link\n                    href={link.href}\n                    className=\"text-gray-400 hover:text-white text-sm transition-colors\"\n                  >\n                    {link.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n        </div>\n\n        {/* Bottom section */}\n        <div className=\"mt-8 pt-8 border-t border-gray-800\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center\">\n            <div className=\"flex items-center space-x-4 text-sm text-gray-400\">\n              <span>© {currentYear} PDF Exam Generator. All rights reserved.</span>\n            </div>\n            <div className=\"flex items-center space-x-4 mt-4 md:mt-0\">\n              <div className=\"flex items-center space-x-2 text-sm text-gray-400\">\n                <FileCheck className=\"w-4 h-4\" />\n                <span>AI-Powered</span>\n              </div>\n              <div className=\"flex items-center space-x-2 text-sm text-gray-400\">\n                <Mail className=\"w-4 h-4\" />\n                <Link href=\"mailto:<EMAIL>\" className=\"hover:text-white transition-colors\">\n                  <EMAIL>\n                </Link>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;;;;AAEO,SAAS;IACd,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,MAAM,cAAc;QAClB,SAAS;YACP;gBAAE,MAAM;gBAAY,MAAM;YAAY;YACtC;gBAAE,MAAM;gBAAgB,MAAM;YAAgB;YAC9C;gBAAE,MAAM;gBAAW,MAAM;YAAW;YACpC;gBAAE,MAAM;gBAAqB,MAAM;YAAO;SAC3C;QACD,OAAO;YACL;gBAAE,MAAM;gBAAkB,MAAM;YAAW;YAC3C;gBAAE,MAAM;gBAAoB,MAAM;YAAS;YAC3C;gBAAE,MAAM;gBAAiB,MAAM;YAAW;YAC1C;gBAAE,MAAM;gBAAmB,MAAM;YAAQ;SAC1C;QACD,SAAS;YACP;gBAAE,MAAM;gBAAe,MAAM;YAAQ;YACrC;gBAAE,MAAM;gBAAc,MAAM;YAAW;YACvC;gBAAE,MAAM;gBAAe,MAAM;YAAQ;YACrC;gBAAE,MAAM;gBAAoB,MAAM;YAAY;SAC/C;IACH;IAEA,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,8OAAC;4CAAK,WAAU;sDAAoB;;;;;;;;;;;;8CAEtC,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAI1C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;sCAKV,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAoE;;;;;;8CAGlF,8OAAC;oCAAG,WAAU;8CACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAoE;;;;;;8CAGlF,8OAAC;oCAAG,WAAU;8CACX,YAAY,KAAK,CAAC,GAAG,CAAC,CAAC,qBACtB,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAoE;;;;;;8CAGlF,8OAAC;oCAAG,WAAU;8CACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;8BAc1B,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;;wCAAK;wCAAG;wCAAY;;;;;;;;;;;;0CAEvB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,gNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;0DACrB,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAgC,WAAU;0DAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU1G", "debugId": null}}, {"offset": {"line": 1319, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/templete/pdf-exam-generator/src/components/layout/DashboardLayout.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { usePathname } from 'next/navigation';\nimport Link from 'next/link';\nimport { \n  LayoutDashboard, \n  FileText, \n  BookOpen, \n  BarChart3, \n  Settings, \n  LogOut, \n  Menu, \n  X,\n  User,\n  Bell,\n  Search,\n  Upload,\n  History,\n  Users,\n  Award\n} from 'lucide-react';\n\nexport function DashboardLayout({ children }: { children: React.ReactNode }) {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const { user, logout } = useAuth();\n  const pathname = usePathname();\n\n  const navigation = [\n    { name: 'لوحة التحكم', href: '/dashboard', icon: LayoutDashboard },\n    { name: 'رفع ملف جديد', href: '/dashboard/upload', icon: Upload },\n    { name: 'الاختبارات', href: '/dashboard/exams', icon: BookOpen },\n    { name: 'التحليلات', href: '/dashboard/analytics', icon: BarChart3 },\n    { name: 'السجل', href: '/dashboard/history', icon: History },\n    { name: 'الطلاب', href: '/dashboard/students', icon: Users },\n    { name: 'الملف الشخصي', href: '/profile', icon: User },\n    { name: 'الإعدادات', href: '/settings', icon: Settings },\n  ];\n\n  const handleLogout = () => {\n    logout();\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 flex\">\n      {/* Mobile sidebar overlay */}\n      {sidebarOpen && (\n        <div \n          className=\"fixed inset-0 z-40 lg:hidden\"\n          onClick={() => setSidebarOpen(false)}\n        >\n          <div className=\"absolute inset-0 bg-gray-600 opacity-75\"></div>\n        </div>\n      )}\n\n      {/* Sidebar */}\n      <div className={`fixed inset-y-0 right-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0 ${\n        sidebarOpen ? 'translate-x-0' : 'translate-x-full'\n      }`}>\n        <div className=\"flex items-center justify-between h-16 px-6 border-b border-gray-200\">\n          <div className=\"flex items-center\">\n            <div className=\"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n              <BookOpen className=\"w-5 h-5 text-white\" />\n            </div>\n            <span className=\"mr-3 text-lg font-semibold text-gray-900\">منصة الاختبارات</span>\n          </div>\n          <button\n            onClick={() => setSidebarOpen(false)}\n            className=\"lg:hidden text-gray-500 hover:text-gray-700\"\n          >\n            <X className=\"w-6 h-6\" />\n          </button>\n        </div>\n\n        {/* User Profile */}\n        <div className=\"p-6 border-b border-gray-200\">\n          <div className=\"flex items-center\">\n            <div className=\"w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center\">\n              {user?.photoURL ? (\n                <img src={user.photoURL} alt={user?.displayName || 'User'} className=\"w-12 h-12 rounded-full object-cover\" />\n              ) : (\n                <User className=\"w-6 h-6 text-white\" />\n              )}\n            </div>\n            <div className=\"mr-3\">\n              <div className=\"text-sm font-medium text-gray-900\">{user?.displayName || user?.email?.split('@')[0] || 'المستخدم'}</div>\n              <div className=\"text-xs text-gray-500 capitalize\">مستخدم</div>\n            </div>\n          </div>\n        </div>\n\n        {/* Navigation */}\n        <nav className=\"mt-6 px-3\">\n          <div className=\"space-y-1\">\n            {navigation.map((item) => {\n              const isActive = pathname === item.href || pathname.startsWith(item.href + '/');\n              return (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className={`group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors ${\n                    isActive\n                      ? 'bg-blue-50 text-blue-700 border-l-4 border-blue-700'\n                      : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'\n                  }`}\n                >\n                  <item.icon className={`ml-3 w-5 h-5 ${\n                    isActive ? 'text-blue-700' : 'text-gray-400 group-hover:text-gray-500'\n                  }`} />\n                  {item.name}\n                </Link>\n              );\n            })}\n          </div>\n        </nav>\n\n        {/* User Stats */}\n        <div className=\"mt-8 px-6\">\n          <div className=\"bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-4\">\n            <h3 className=\"text-sm font-medium text-gray-900 mb-3\">إحصائياتك</h3>\n            <div className=\"space-y-2\">\n              <div className=\"flex justify-between text-xs\">\n                <span className=\"text-gray-600\">الاختبارات</span>\n                <span className=\"font-medium text-gray-900\">24</span>\n              </div>\n              <div className=\"flex justify-between text-xs\">\n                <span className=\"text-gray-600\">المعدل</span>\n                <span className=\"font-medium text-gray-900\">85%</span>\n              </div>\n              <div className=\"flex justify-between text-xs\">\n                <span className=\"text-gray-600\">الأسئلة</span>\n                <span className=\"font-medium text-gray-900\">342</span>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Logout */}\n        <div className=\"absolute bottom-6 left-0 right-0 px-6\">\n          <button\n            onClick={handleLogout}\n            className=\"w-full flex items-center px-3 py-2 text-sm font-medium text-red-700 hover:bg-red-50 rounded-lg transition-colors\"\n          >\n            <LogOut className=\"ml-3 w-5 h-5\" />\n            تسجيل الخروج\n          </button>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"flex-1 flex flex-col lg:mr-64\">\n        {/* Top bar */}\n        <header className=\"bg-white shadow-sm border-b border-gray-200\">\n          <div className=\"flex items-center justify-between h-16 px-6\">\n            <div className=\"flex items-center\">\n              <button\n                onClick={() => setSidebarOpen(true)}\n                className=\"lg:hidden text-gray-500 hover:text-gray-700\"\n              >\n                <Menu className=\"w-6 h-6\" />\n              </button>\n              \n              <div className=\"lg:hidden mr-4\">\n                <h1 className=\"text-lg font-semibold text-gray-900\">لوحة التحكم</h1>\n              </div>\n            </div>\n\n            <div className=\"flex items-center space-x-4 rtl:space-x-reverse\">\n              {/* Search */}\n              <div className=\"hidden md:block relative\">\n                <Search className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\" />\n                <input\n                  type=\"text\"\n                  placeholder=\"البحث...\"\n                  className=\"w-64 pl-4 pr-10 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm\"\n                />\n              </div>\n\n              {/* Notifications */}\n              <button className=\"relative p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg\">\n                <Bell className=\"w-5 h-5\" />\n                <span className=\"absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full\"></span>\n              </button>\n\n              {/* User menu */}\n              <div className=\"flex items-center\">\n                <div className=\"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center\">\n                  {user?.photoURL ? (\n                    <img src={user.photoURL} alt={user?.displayName || 'User'} className=\"w-8 h-8 rounded-full object-cover\" />\n                  ) : (\n                    <User className=\"w-4 h-4 text-white\" />\n                  )}\n                </div>\n              </div>\n            </div>\n          </div>\n        </header>\n\n        {/* Page content */}\n        <main className=\"flex-1 overflow-auto\">\n          {children}\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AANA;;;;;;;AAwBO,SAAS,gBAAgB,EAAE,QAAQ,EAAiC;IACzE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,aAAa;QACjB;YAAE,MAAM;YAAe,MAAM;YAAc,MAAM,4NAAA,CAAA,kBAAe;QAAC;QACjE;YAAE,MAAM;YAAgB,MAAM;YAAqB,MAAM,sMAAA,CAAA,SAAM;QAAC;QAChE;YAAE,MAAM;YAAc,MAAM;YAAoB,MAAM,8MAAA,CAAA,WAAQ;QAAC;QAC/D;YAAE,MAAM;YAAa,MAAM;YAAwB,MAAM,kNAAA,CAAA,YAAS;QAAC;QACnE;YAAE,MAAM;YAAS,MAAM;YAAsB,MAAM,wMAAA,CAAA,UAAO;QAAC;QAC3D;YAAE,MAAM;YAAU,MAAM;YAAuB,MAAM,oMAAA,CAAA,QAAK;QAAC;QAC3D;YAAE,MAAM;YAAgB,MAAM;YAAY,MAAM,kMAAA,CAAA,OAAI;QAAC;QACrD;YAAE,MAAM;YAAa,MAAM;YAAa,MAAM,0MAAA,CAAA,WAAQ;QAAC;KACxD;IAED,MAAM,eAAe;QACnB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;YAEZ,6BACC,8OAAC;gBACC,WAAU;gBACV,SAAS,IAAM,eAAe;0BAE9B,cAAA,8OAAC;oBAAI,WAAU;;;;;;;;;;;0BAKnB,8OAAC;gBAAI,WAAW,CAAC,mJAAmJ,EAClK,cAAc,kBAAkB,oBAChC;;kCACA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAEtB,8OAAC;wCAAK,WAAU;kDAA2C;;;;;;;;;;;;0CAE7D,8OAAC;gCACC,SAAS,IAAM,eAAe;gCAC9B,WAAU;0CAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAKjB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACZ,MAAM,yBACL,8OAAC;wCAAI,KAAK,KAAK,QAAQ;wCAAE,KAAK,MAAM,eAAe;wCAAQ,WAAU;;;;;6DAErE,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;8CAGpB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAqC,MAAM,eAAe,MAAM,OAAO,MAAM,IAAI,CAAC,EAAE,IAAI;;;;;;sDACvG,8OAAC;4CAAI,WAAU;sDAAmC;;;;;;;;;;;;;;;;;;;;;;;kCAMxD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC;gCACf,MAAM,WAAW,aAAa,KAAK,IAAI,IAAI,SAAS,UAAU,CAAC,KAAK,IAAI,GAAG;gCAC3E,qBACE,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAW,CAAC,mFAAmF,EAC7F,WACI,wDACA,sDACJ;;sDAEF,8OAAC,KAAK,IAAI;4CAAC,WAAW,CAAC,aAAa,EAClC,WAAW,kBAAkB,2CAC7B;;;;;;wCACD,KAAK,IAAI;;mCAXL,KAAK,IAAI;;;;;4BAcpB;;;;;;;;;;;kCAKJ,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,8OAAC;oDAAK,WAAU;8DAA4B;;;;;;;;;;;;sDAE9C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,8OAAC;oDAAK,WAAU;8DAA4B;;;;;;;;;;;;sDAE9C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,8OAAC;oDAAK,WAAU;8DAA4B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOpD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,SAAS;4BACT,WAAU;;8CAEV,8OAAC,0MAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;0BAOzC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAO,WAAU;kCAChB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS,IAAM,eAAe;4CAC9B,WAAU;sDAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAGlB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAG,WAAU;0DAAsC;;;;;;;;;;;;;;;;;8CAIxD,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;oDACC,MAAK;oDACL,aAAY;oDACZ,WAAU;;;;;;;;;;;;sDAKd,8OAAC;4CAAO,WAAU;;8DAChB,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC;oDAAK,WAAU;;;;;;;;;;;;sDAIlB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACZ,MAAM,yBACL,8OAAC;oDAAI,KAAK,KAAK,QAAQ;oDAAE,KAAK,MAAM,eAAe;oDAAQ,WAAU;;;;;yEAErE,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAS5B,8OAAC;wBAAK,WAAU;kCACb;;;;;;;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 1880, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/templete/pdf-exam-generator/src/components/layout/AppContent.tsx"], "sourcesContent": ["'use client';\n\nimport { useAuth } from '@/contexts/AuthContext';\nimport { usePathname } from 'next/navigation';\nimport { Navbar } from './Navbar';\nimport { Footer } from './Footer';\nimport { DashboardLayout } from './DashboardLayout';\n\nexport function AppContent({ children }: { children: React.ReactNode }) {\n  const { isAuthenticated, loading } = useAuth();\n  const pathname = usePathname();\n\n  // Show loading spinner while checking authentication\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600\">جاري التحميل...</p>\n        </div>\n      </div>\n    );\n  }\n\n  // Check if it's an auth route\n  const isAuthRoute = pathname.startsWith('/auth/');\n\n  // For auth routes, show without navbar/footer\n  if (isAuthRoute) {\n    return <>{children}</>;\n  }\n\n  // If not authenticated and not auth route, show with navbar for landing page\n  if (!isAuthenticated) {\n    return (\n      <div className=\"min-h-screen flex flex-col\">\n        <Navbar />\n        <main className=\"flex-1\">\n          {children}\n        </main>\n        <Footer />\n      </div>\n    );\n  }\n\n  // If authenticated, check if it's a dashboard route\n  const isDashboardRoute = pathname.startsWith('/dashboard') || \n                          pathname === '/analyze' || \n                          pathname === '/exam' || \n                          pathname === '/results' || \n                          pathname === '/settings';\n\n  if (isDashboardRoute) {\n    return (\n      <DashboardLayout>\n        {children}\n      </DashboardLayout>\n    );\n  }\n\n  // For other authenticated routes, use regular layout\n  return (\n    <div className=\"min-h-screen flex flex-col\">\n      <Navbar />\n      <main className=\"flex-1\">\n        {children}\n      </main>\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQO,SAAS,WAAW,EAAE,QAAQ,EAAiC;IACpE,MAAM,EAAE,eAAe,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC3C,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,qDAAqD;IACrD,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,8BAA8B;IAC9B,MAAM,cAAc,SAAS,UAAU,CAAC;IAExC,8CAA8C;IAC9C,IAAI,aAAa;QACf,qBAAO;sBAAG;;IACZ;IAEA,6EAA6E;IAC7E,IAAI,CAAC,iBAAiB;QACpB,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,sIAAA,CAAA,SAAM;;;;;8BACP,8OAAC;oBAAK,WAAU;8BACb;;;;;;8BAEH,8OAAC,sIAAA,CAAA,SAAM;;;;;;;;;;;IAGb;IAEA,oDAAoD;IACpD,MAAM,mBAAmB,SAAS,UAAU,CAAC,iBACrB,aAAa,cACb,aAAa,WACb,aAAa,cACb,aAAa;IAErC,IAAI,kBAAkB;QACpB,qBACE,8OAAC,+IAAA,CAAA,kBAAe;sBACb;;;;;;IAGP;IAEA,qDAAqD;IACrD,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,sIAAA,CAAA,SAAM;;;;;0BACP,8OAAC;gBAAK,WAAU;0BACb;;;;;;0BAEH,8OAAC,sIAAA,CAAA,SAAM;;;;;;;;;;;AAGb", "debugId": null}}]}