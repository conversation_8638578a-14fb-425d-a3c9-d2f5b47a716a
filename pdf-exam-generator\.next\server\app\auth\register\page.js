(()=>{var e={};e.id=983,e.ids=[983],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12597:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},13861:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},16937:(e,r,t)=>{Promise.resolve().then(t.bind(t,24507))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},23026:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("user-plus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},24507:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>g});var s=t(60687),a=t(43210),i=t(16189),l=t(85814),n=t.n(l),o=t(7613),d=t(34393),c=t(23026),u=t(58869),m=t(41550),x=t(64021),p=t(12597),h=t(13861);function g(){let[e,r]=(0,a.useState)({displayName:"",email:"",password:"",confirmPassword:"",role:"student"}),[t,l]=(0,a.useState)(!1),[g,f]=(0,a.useState)(!1),[y,b]=(0,a.useState)(""),[v,w]=(0,a.useState)(!1),{signUp:j,signInWithGoogle:N,signInWithFacebook:k}=(0,o.A)(),{t:q}=(0,d.o)(),A=(0,i.useRouter)(),C=t=>{r({...e,[t.target.name]:t.target.value})},P=async r=>{if(r.preventDefault(),b(""),e.password!==e.confirmPassword)return void b("كلمات المرور غير متطابقة");if(e.password.length<6)return void b("كلمة المرور يجب أن تكون 6 أحرف على الأقل");w(!0);try{await j(e.email,e.password,e.displayName,e.role),A.push("/dashboard")}catch(e){b(z(e.code||"unknown-error"))}finally{w(!1)}},M=async()=>{b(""),w(!0);try{await N(),A.push("/dashboard")}catch(e){b(z(e.code||"unknown-error"))}finally{w(!1)}},_=async()=>{b(""),w(!0);try{await k(),A.push("/dashboard")}catch(e){b(z(e.code||"unknown-error"))}finally{w(!1)}},z=e=>{switch(e){case"auth/email-already-in-use":return"هذا البريد الإلكتروني مستخدم بالفعل";case"auth/invalid-email":return"البريد الإلكتروني غير صالح";case"auth/weak-password":return"كلمة المرور ضعيفة جداً";case"auth/operation-not-allowed":return"تسجيل الحسابات الجديدة غير مفعل حالياً";default:return"حدث خطأ أثناء إنشاء الحساب. حاول مرة أخرى"}};return(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-green-50 to-emerald-100 flex items-center justify-center p-4",children:(0,s.jsx)("div",{className:"max-w-md w-full space-y-8",children:(0,s.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl p-8",children:[(0,s.jsxs)("div",{className:"text-center mb-8",children:[(0,s.jsx)("div",{className:"mx-auto h-12 w-12 bg-green-600 rounded-xl flex items-center justify-center mb-4",children:(0,s.jsx)(c.A,{className:"h-6 w-6 text-white"})}),(0,s.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-2",children:"إنشاء حساب جديد"}),(0,s.jsx)("p",{className:"text-gray-600",children:"انضم إلينا وابدأ رحلتك التعليمية"})]}),y&&(0,s.jsx)("div",{className:"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg",children:(0,s.jsx)("p",{className:"text-red-600 text-sm text-center",children:y})}),(0,s.jsxs)("form",{onSubmit:P,className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"displayName",className:"block text-sm font-medium text-gray-700 mb-2",children:"الاسم الكامل"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:(0,s.jsx)(u.A,{className:"h-5 w-5 text-gray-400"})}),(0,s.jsx)("input",{id:"displayName",name:"displayName",type:"text",required:!0,value:e.displayName,onChange:C,className:"block w-full pr-10 pl-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 text-right",placeholder:"أدخل اسمك الكامل",dir:"rtl"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"البريد الإلكتروني"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:(0,s.jsx)(m.A,{className:"h-5 w-5 text-gray-400"})}),(0,s.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:e.email,onChange:C,className:"block w-full pr-10 pl-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 text-right",placeholder:"أدخل بريدك الإلكتروني",dir:"rtl"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"role",className:"block text-sm font-medium text-gray-700 mb-2",children:"نوع الحساب"}),(0,s.jsxs)("select",{id:"role",name:"role",value:e.role,onChange:C,className:"block w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 text-right",dir:"rtl",children:[(0,s.jsx)("option",{value:"student",children:"طالب"}),(0,s.jsx)("option",{value:"teacher",children:"معلم"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-2",children:"كلمة المرور"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:(0,s.jsx)(x.A,{className:"h-5 w-5 text-gray-400"})}),(0,s.jsx)("input",{id:"password",name:"password",type:t?"text":"password",autoComplete:"new-password",required:!0,value:e.password,onChange:C,className:"block w-full pr-10 pl-10 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 text-right",placeholder:"أدخل كلمة المرور",dir:"rtl"}),(0,s.jsx)("button",{type:"button",className:"absolute inset-y-0 left-0 pl-3 flex items-center",onClick:()=>l(!t),children:t?(0,s.jsx)(p.A,{className:"h-5 w-5 text-gray-400 hover:text-gray-600"}):(0,s.jsx)(h.A,{className:"h-5 w-5 text-gray-400 hover:text-gray-600"})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700 mb-2",children:"تأكيد كلمة المرور"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:(0,s.jsx)(x.A,{className:"h-5 w-5 text-gray-400"})}),(0,s.jsx)("input",{id:"confirmPassword",name:"confirmPassword",type:g?"text":"password",autoComplete:"new-password",required:!0,value:e.confirmPassword,onChange:C,className:"block w-full pr-10 pl-10 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 text-right",placeholder:"أعد إدخال كلمة المرور",dir:"rtl"}),(0,s.jsx)("button",{type:"button",className:"absolute inset-y-0 left-0 pl-3 flex items-center",onClick:()=>f(!g),children:g?(0,s.jsx)(p.A,{className:"h-5 w-5 text-gray-400 hover:text-gray-600"}):(0,s.jsx)(h.A,{className:"h-5 w-5 text-gray-400 hover:text-gray-600"})})]})]}),(0,s.jsx)("button",{type:"submit",disabled:v,className:"w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:v?(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"}),"جاري إنشاء الحساب..."]}):"إنشاء حساب"})]}),(0,s.jsx)("div",{className:"mt-6",children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,s.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,s.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,s.jsx)("span",{className:"px-2 bg-white text-gray-500",children:"أو"})})]})}),(0,s.jsxs)("div",{className:"mt-6 space-y-3",children:[(0,s.jsxs)("button",{onClick:M,disabled:v,className:"w-full flex justify-center items-center py-3 px-4 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:[(0,s.jsxs)("svg",{className:"w-5 h-5 ml-2",viewBox:"0 0 24 24",children:[(0,s.jsx)("path",{fill:"#4285F4",d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"}),(0,s.jsx)("path",{fill:"#34A853",d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"}),(0,s.jsx)("path",{fill:"#FBBC05",d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"}),(0,s.jsx)("path",{fill:"#EA4335",d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"})]}),"التسجيل بـ Google"]}),(0,s.jsxs)("button",{onClick:_,disabled:v,className:"w-full flex justify-center items-center py-3 px-4 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:[(0,s.jsx)("svg",{className:"w-5 h-5 ml-2",fill:"#1877F2",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{d:"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"})}),"التسجيل بـ Facebook"]})]}),(0,s.jsx)("div",{className:"mt-6 text-center",children:(0,s.jsxs)("p",{className:"text-sm text-gray-600",children:["لديك حساب بالفعل؟"," ",(0,s.jsx)(n(),{href:"/auth/login",className:"font-medium text-green-600 hover:text-green-500",children:"تسجيل الدخول"})]})})]})})})}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37366:e=>{"use strict";e.exports=require("dns")},52510:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>l.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var s=t(65239),a=t(48088),i=t(88170),l=t.n(i),n=t(30893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);t.d(r,o);let d={children:["",{children:["auth",{children:["register",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,59401)),"D:\\templete\\pdf-exam-generator\\src\\app\\auth\\register\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:"/manifest.webmanifest"}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"D:\\templete\\pdf-exam-generator\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:"/manifest.webmanifest"}}]}.children,c=["D:\\templete\\pdf-exam-generator\\src\\app\\auth\\register\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/auth/register/page",pathname:"/auth/register",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},55511:e=>{"use strict";e.exports=require("crypto")},59401:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\templete\\\\pdf-exam-generator\\\\src\\\\app\\\\auth\\\\register\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\templete\\pdf-exam-generator\\src\\app\\auth\\register\\page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64021:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},70440:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75081:(e,r,t)=>{Promise.resolve().then(t.bind(t,59401))},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,248,658,647],()=>t(52510));module.exports=s})();