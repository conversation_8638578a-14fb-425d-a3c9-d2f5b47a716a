{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { NextResponse } from 'next/server';\nimport type { NextRequest } from 'next/server';\n\n// Define protected routes that require authentication\nconst protectedRoutes = [\n  '/dashboard',\n  '/exam',\n  '/analyze',\n  '/results',\n  '/settings'\n];\n\n// Define auth routes that should redirect to dashboard if user is already authenticated\nconst authRoutes = [\n  '/auth/login',\n  '/auth/register',\n  '/auth/forgot-password'\n];\n\nexport function middleware(request: NextRequest) {\n  const { pathname } = request.nextUrl;\n  \n  // Check if the current path is a protected route\n  const isProtectedRoute = protectedRoutes.some(route => \n    pathname.startsWith(route)\n  );\n  \n  // Check if the current path is an auth route\n  const isAuthRoute = authRoutes.some(route => \n    pathname.startsWith(route)\n  );\n\n  // Get the authentication token from cookies\n  // Note: This is a simplified check. In a real app, you'd verify the token\n  const authToken = request.cookies.get('auth-token')?.value;\n  \n  // For Firebase Auth, we'll check for the Firebase Auth cookie\n  // Firebase sets this automatically when using Firebase Auth\n  const firebaseAuthCookie = request.cookies.get('__session')?.value;\n  \n  // Check if user is authenticated\n  const isAuthenticated = authToken || firebaseAuthCookie;\n\n  // If trying to access protected route without authentication\n  if (isProtectedRoute && !isAuthenticated) {\n    const loginUrl = new URL('/auth/login', request.url);\n    loginUrl.searchParams.set('redirect', pathname);\n    return NextResponse.redirect(loginUrl);\n  }\n\n  // If trying to access auth routes while authenticated\n  if (isAuthRoute && isAuthenticated) {\n    return NextResponse.redirect(new URL('/dashboard', request.url));\n  }\n\n  // If accessing root path while authenticated, redirect to dashboard\n  if (pathname === '/' && isAuthenticated) {\n    return NextResponse.redirect(new URL('/dashboard', request.url));\n  }\n\n  return NextResponse.next();\n}\n\n// Configure which paths the middleware should run on\nexport const config = {\n  matcher: [\n    /*\n     * Match all request paths except for the ones starting with:\n     * - api (API routes)\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     * - public folder files\n     */\n    '/((?!api|_next/static|_next/image|favicon.ico|.*\\\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',\n  ],\n};\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;AAGA,sDAAsD;AACtD,MAAM,kBAAkB;IACtB;IACA;IACA;IACA;IACA;CACD;AAED,wFAAwF;AACxF,MAAM,aAAa;IACjB;IACA;IACA;CACD;AAEM,SAAS,WAAW,OAAoB;IAC7C,MAAM,EAAE,QAAQ,EAAE,GAAG,QAAQ,OAAO;IAEpC,iDAAiD;IACjD,MAAM,mBAAmB,gBAAgB,IAAI,CAAC,CAAA,QAC5C,SAAS,UAAU,CAAC;IAGtB,6CAA6C;IAC7C,MAAM,cAAc,WAAW,IAAI,CAAC,CAAA,QAClC,SAAS,UAAU,CAAC;IAGtB,4CAA4C;IAC5C,0EAA0E;IAC1E,MAAM,YAAY,QAAQ,OAAO,CAAC,GAAG,CAAC,eAAe;IAErD,8DAA8D;IAC9D,4DAA4D;IAC5D,MAAM,qBAAqB,QAAQ,OAAO,CAAC,GAAG,CAAC,cAAc;IAE7D,iCAAiC;IACjC,MAAM,kBAAkB,aAAa;IAErC,6DAA6D;IAC7D,IAAI,oBAAoB,CAAC,iBAAiB;QACxC,MAAM,WAAW,IAAI,IAAI,eAAe,QAAQ,GAAG;QACnD,SAAS,YAAY,CAAC,GAAG,CAAC,YAAY;QACtC,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;IAC/B;IAEA,sDAAsD;IACtD,IAAI,eAAe,iBAAiB;QAClC,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,cAAc,QAAQ,GAAG;IAChE;IAEA,oEAAoE;IACpE,IAAI,aAAa,OAAO,iBAAiB;QACvC,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,cAAc,QAAQ,GAAG;IAChE;IAEA,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;AAC1B;AAGO,MAAM,SAAS;IACpB,SAAS;QACP;;;;;;;KAOC,GACD;KACD;AACH"}}]}