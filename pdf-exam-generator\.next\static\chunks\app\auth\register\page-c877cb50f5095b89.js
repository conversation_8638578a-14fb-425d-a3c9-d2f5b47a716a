(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[983],{844:(e,r,t)=>{"use strict";t.d(r,{AuthProvider:()=>p,A:()=>y});var s=t(5155),a=t(2115),l=t(6203),o=t(5317),i=t(3915),n=t(858);let c={apiKey:"demo-key",authDomain:"demo-project.firebaseapp.com",projectId:"demo-project",storageBucket:"demo-project.appspot.com",messagingSenderId:"*********",appId:"1:*********:web:abcdef",measurementId:t(9509).env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID||"G-ABCDEF"},d=0===(0,i.Dk)().length?(0,i.Wp)(c):(0,i.Dk)()[0],u=(0,l.xI)(d),m=(0,o.aU)(d);(0,n.c7)(d);let h=new l.HF,x=new l.sk;h.setCustomParameters({prompt:"select_account"}),x.setCustomParameters({display:"popup"});let g=(0,a.createContext)(void 0),y=()=>{let e=(0,a.useContext)(g);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e},p=e=>{let{children:r}=e,[t,i]=(0,a.useState)(null),[n,c]=(0,a.useState)(null),[d,y]=(0,a.useState)(!0),p=async function(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!e)return;let t=(0,o.H9)(m,"users",e.uid),s=await (0,o.x7)(t);if(s.exists()){let e={...s.data(),lastLoginAt:new Date};await (0,o.BN)(t,e,{merge:!0}),c(e)}else{let{displayName:s,email:a,photoURL:l}=e,i=new Date,n={uid:e.uid,email:a||"",displayName:s||"",photoURL:l||void 0,role:r.role||"student",createdAt:i,lastLoginAt:i,preferences:{language:"ar",theme:"light",notifications:!0},stats:{totalExams:0,averageScore:0,totalQuestions:0,studyTime:0},...r};try{await (0,o.BN)(t,n),c(n)}catch(e){console.error("Error creating user profile:",e)}}},f=async(e,r)=>{y(!0);try{let t=await (0,l.x9)(u,e,r);await p(t.user)}catch(e){throw console.error("Error signing in:",e),e}finally{y(!1)}},w=async(e,r)=>{try{return await f(e,r),!0}catch(e){return!1}},b=async(e,r,t,s)=>{y(!0);try{let a=await (0,l.eJ)(u,e,r);await (0,l.r7)(a.user,{displayName:t}),await p(a.user,{role:s,displayName:t})}catch(e){throw console.error("Error signing up:",e),e}finally{y(!1)}},v=async()=>{y(!0);try{let e=await (0,l.df)(u,h);await p(e.user)}catch(e){throw console.error("Error signing in with Google:",e),e}finally{y(!1)}},j=async()=>{y(!0);try{let e=await (0,l.df)(u,x);await p(e.user)}catch(e){throw console.error("Error signing in with Facebook:",e),e}finally{y(!1)}},N=async()=>{y(!0);try{await (0,l.CI)(u),i(null),c(null),localStorage.removeItem("examResults"),localStorage.removeItem("examAnswers"),localStorage.removeItem("uploadedPDF"),localStorage.removeItem("examConfig")}catch(e){throw console.error("Error signing out:",e),e}finally{y(!1)}},k=async e=>{try{await (0,l.J1)(u,e)}catch(e){throw console.error("Error sending password reset email:",e),e}},A=async e=>{if(t)try{let r=(0,o.H9)(m,"users",t.uid);await (0,o.BN)(r,e,{merge:!0}),n&&c({...n,...e})}catch(e){throw console.error("Error updating user profile:",e),e}};return(0,a.useEffect)(()=>(0,l.hg)(u,async e=>{e?(i(e),await p(e)):(i(null),c(null)),y(!1)}),[]),(0,s.jsx)(g.Provider,{value:{user:t,userProfile:n,loading:d,isAuthenticated:!!t,signIn:f,signUp:b,signInWithGoogle:v,signInWithFacebook:j,logout:N,resetPassword:k,updateUserProfile:A,login:w},children:r})}},1007:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},2318:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(9946).A)("user-plus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},2657:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},2919:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(9946).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},4247:(e,r,t)=>{Promise.resolve().then(t.bind(t,6117))},5695:(e,r,t)=>{"use strict";var s=t(8999);t.o(s,"usePathname")&&t.d(r,{usePathname:function(){return s.usePathname}}),t.o(s,"useRouter")&&t.d(r,{useRouter:function(){return s.useRouter}})},6117:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>g});var s=t(5155),a=t(2115),l=t(5695),o=t(6874),i=t.n(o),n=t(844),c=t(2318),d=t(1007),u=t(8883),m=t(2919),h=t(8749),x=t(2657);function g(){let[e,r]=(0,a.useState)({displayName:"",email:"",password:"",confirmPassword:"",role:"student"}),[t,o]=(0,a.useState)(!1),[g,y]=(0,a.useState)(!1),[p,f]=(0,a.useState)(""),[w,b]=(0,a.useState)(!1),{signUp:v,signInWithGoogle:j,signInWithFacebook:N}=(0,n.A)(),k=(0,l.useRouter)(),A=t=>{r({...e,[t.target.name]:t.target.value})},C=async r=>{if(r.preventDefault(),f(""),e.password!==e.confirmPassword)return void f("كلمات المرور غير متطابقة");if(e.password.length<6)return void f("كلمة المرور يجب أن تكون 6 أحرف على الأقل");b(!0);try{await v(e.email,e.password,e.displayName,e.role),k.push("/dashboard")}catch(e){f(S(e.code||"unknown-error"))}finally{b(!1)}},E=async()=>{f(""),b(!0);try{await j(),k.push("/dashboard")}catch(e){f(S(e.code||"unknown-error"))}finally{b(!1)}},P=async()=>{f(""),b(!0);try{await N(),k.push("/dashboard")}catch(e){f(S(e.code||"unknown-error"))}finally{b(!1)}},S=e=>{switch(e){case"auth/email-already-in-use":return"هذا البريد الإلكتروني مستخدم بالفعل";case"auth/invalid-email":return"البريد الإلكتروني غير صالح";case"auth/weak-password":return"كلمة المرور ضعيفة جداً";case"auth/operation-not-allowed":return"تسجيل الحسابات الجديدة غير مفعل حالياً";default:return"حدث خطأ أثناء إنشاء الحساب. حاول مرة أخرى"}};return(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-green-50 to-emerald-100 flex items-center justify-center p-4",children:(0,s.jsx)("div",{className:"max-w-md w-full space-y-8",children:(0,s.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl p-8",children:[(0,s.jsxs)("div",{className:"text-center mb-8",children:[(0,s.jsx)("div",{className:"mx-auto h-12 w-12 bg-green-600 rounded-xl flex items-center justify-center mb-4",children:(0,s.jsx)(c.A,{className:"h-6 w-6 text-white"})}),(0,s.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-2",children:"إنشاء حساب جديد"}),(0,s.jsx)("p",{className:"text-gray-600",children:"انضم إلينا وابدأ رحلتك التعليمية"})]}),p&&(0,s.jsx)("div",{className:"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg",children:(0,s.jsx)("p",{className:"text-red-600 text-sm text-center",children:p})}),(0,s.jsxs)("form",{onSubmit:C,className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"displayName",className:"block text-sm font-medium text-gray-700 mb-2",children:"الاسم الكامل"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:(0,s.jsx)(d.A,{className:"h-5 w-5 text-gray-400"})}),(0,s.jsx)("input",{id:"displayName",name:"displayName",type:"text",required:!0,value:e.displayName,onChange:A,className:"block w-full pr-10 pl-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 text-right",placeholder:"أدخل اسمك الكامل",dir:"rtl"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"البريد الإلكتروني"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:(0,s.jsx)(u.A,{className:"h-5 w-5 text-gray-400"})}),(0,s.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:e.email,onChange:A,className:"block w-full pr-10 pl-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 text-right",placeholder:"أدخل بريدك الإلكتروني",dir:"rtl"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"role",className:"block text-sm font-medium text-gray-700 mb-2",children:"نوع الحساب"}),(0,s.jsxs)("select",{id:"role",name:"role",value:e.role,onChange:A,className:"block w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 text-right",dir:"rtl",children:[(0,s.jsx)("option",{value:"student",children:"طالب"}),(0,s.jsx)("option",{value:"teacher",children:"معلم"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-2",children:"كلمة المرور"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:(0,s.jsx)(m.A,{className:"h-5 w-5 text-gray-400"})}),(0,s.jsx)("input",{id:"password",name:"password",type:t?"text":"password",autoComplete:"new-password",required:!0,value:e.password,onChange:A,className:"block w-full pr-10 pl-10 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 text-right",placeholder:"أدخل كلمة المرور",dir:"rtl"}),(0,s.jsx)("button",{type:"button",className:"absolute inset-y-0 left-0 pl-3 flex items-center",onClick:()=>o(!t),children:t?(0,s.jsx)(h.A,{className:"h-5 w-5 text-gray-400 hover:text-gray-600"}):(0,s.jsx)(x.A,{className:"h-5 w-5 text-gray-400 hover:text-gray-600"})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700 mb-2",children:"تأكيد كلمة المرور"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:(0,s.jsx)(m.A,{className:"h-5 w-5 text-gray-400"})}),(0,s.jsx)("input",{id:"confirmPassword",name:"confirmPassword",type:g?"text":"password",autoComplete:"new-password",required:!0,value:e.confirmPassword,onChange:A,className:"block w-full pr-10 pl-10 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 text-right",placeholder:"أعد إدخال كلمة المرور",dir:"rtl"}),(0,s.jsx)("button",{type:"button",className:"absolute inset-y-0 left-0 pl-3 flex items-center",onClick:()=>y(!g),children:g?(0,s.jsx)(h.A,{className:"h-5 w-5 text-gray-400 hover:text-gray-600"}):(0,s.jsx)(x.A,{className:"h-5 w-5 text-gray-400 hover:text-gray-600"})})]})]}),(0,s.jsx)("button",{type:"submit",disabled:w,className:"w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:w?(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"}),"جاري إنشاء الحساب..."]}):"إنشاء حساب"})]}),(0,s.jsx)("div",{className:"mt-6",children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,s.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,s.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,s.jsx)("span",{className:"px-2 bg-white text-gray-500",children:"أو"})})]})}),(0,s.jsxs)("div",{className:"mt-6 space-y-3",children:[(0,s.jsxs)("button",{onClick:E,disabled:w,className:"w-full flex justify-center items-center py-3 px-4 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:[(0,s.jsxs)("svg",{className:"w-5 h-5 ml-2",viewBox:"0 0 24 24",children:[(0,s.jsx)("path",{fill:"#4285F4",d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"}),(0,s.jsx)("path",{fill:"#34A853",d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"}),(0,s.jsx)("path",{fill:"#FBBC05",d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"}),(0,s.jsx)("path",{fill:"#EA4335",d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"})]}),"التسجيل بـ Google"]}),(0,s.jsxs)("button",{onClick:P,disabled:w,className:"w-full flex justify-center items-center py-3 px-4 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:[(0,s.jsx)("svg",{className:"w-5 h-5 ml-2",fill:"#1877F2",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{d:"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"})}),"التسجيل بـ Facebook"]})]}),(0,s.jsx)("div",{className:"mt-6 text-center",children:(0,s.jsxs)("p",{className:"text-sm text-gray-600",children:["لديك حساب بالفعل؟"," ",(0,s.jsx)(i(),{href:"/auth/login",className:"font-medium text-green-600 hover:text-green-500",children:"تسجيل الدخول"})]})})]})})})}},8749:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(9946).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},8883:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(9946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])}},e=>{var r=r=>e(e.s=r);e.O(0,[992,811,100,470,874,441,684,358],()=>r(4247)),_N_E=e.O()}]);