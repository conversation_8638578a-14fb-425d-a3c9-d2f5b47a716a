# PDF to Interactive Exam Generator

## 🎯 نظام تحويل PDF إلى امتحانات تفاعلية

تطبيق ويب متطور يحول ملفات PDF إلى امتحانات تفاعلية باستخدام الذكاء الاصطناعي، مع نظام مصادقة متكامل ولوحة تحكم شاملة.

### ✨ المميزات الرئيسية

- 🔐 **نظام مصادقة متكامل** مع Firebase Authentication
- 📄 **تحليل PDF ذكي** وتحويله إلى أسئلة تفاعلية
- 🎨 **واجهة مستخدم عصرية** مع دعم اللغة العربية والإنجليزية
- 📊 **تحليلات شاملة** ومتابعة الأداء
- 👥 **إدارة المستخدمين** (معلمين وطلاب)
- 🌙 **دعم الوضع الداكن** والفاتح
- 📱 **تصميم متجاوب** لجميع الأجهزة

### 🚀 التقنيات المستخدمة

- **Frontend**: Next.js 15, React 19, TypeScript
- **Styling**: Tailwind CSS
- **Authentication**: Firebase Auth (Email, Google, Facebook)
- **Database**: Firestore
- **Storage**: Firebase Storage
- **Icons**: Lucide React
- **Forms**: React Hook Form + Zod

## 🛠️ التثبيت والإعداد

### 1. استنساخ المشروع

```bash
git clone <repository-url>
cd pdf-exam-generator
```

### 2. تثبيت التبعيات

```bash
npm install
```

### 3. إعداد Firebase

1. اذهب إلى [Firebase Console](https://console.firebase.google.com/)
2. أنشئ مشروع جديد أو استخدم مشروع موجود
3. فعّل Authentication و Firestore و Storage
4. احصل على إعدادات المشروع من Project Settings

### 4. إعداد متغيرات البيئة

أضف إعدادات Firebase إلى ملف `.env.local`:

```env
NEXT_PUBLIC_FIREBASE_API_KEY=your_actual_api_key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project_id.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_project_id.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id
```

### 5. تشغيل المشروع

```bash
npm run dev
```

افتح [http://localhost:3000](http://localhost:3000) في متصفحك.

## 📱 الصفحات والوظائف

### 🏠 الصفحة الرئيسية
- عرض تقديمي للمنتج
- رفع ملفات PDF
- دعم السحب والإفلات

### 🔐 نظام المصادقة
- **تسجيل الدخول**: `/auth/login`
- **إنشاء حساب**: `/auth/register`
- **إعادة تعيين كلمة المرور**: `/auth/forgot-password`

### 📊 لوحة التحكم
- **الرئيسية**: `/dashboard` - نظرة عامة وإحصائيات
- **رفع ملف**: `/dashboard/upload` - رفع ملفات PDF جديدة
- **الامتحانات**: `/dashboard/exams` - إدارة الامتحانات
- **التحليلات**: `/dashboard/analytics` - تحليلات مفصلة
- **السجل**: `/dashboard/history` - سجل الأنشطة
- **الطلاب**: `/dashboard/students` - إدارة الطلاب (للمعلمين)

### 👤 إدارة الحساب
- **الملف الشخصي**: `/profile` - عرض وتحديث المعلومات الشخصية
- **الإعدادات**: `/settings` - تخصيص التفضيلات

### 📝 الامتحانات
- **أخذ الامتحان**: `/exam` - واجهة الامتحان التفاعلية
- **النتائج**: `/results` - عرض النتائج والتحليلات

## 🎨 المميزات التقنية

### 🔒 الأمان
- مصادقة آمنة مع Firebase
- حماية الصفحات بـ Middleware
- تشفير البيانات الحساسة

### 🌐 دعم متعدد اللغات
- العربية (RTL)
- الإنجليزية (LTR)
- تبديل سهل بين اللغات

### 📱 تصميم متجاوب
- دعم كامل للهواتف المحمولة
- تخطيط مرن للأجهزة اللوحية
- واجهة محسنة لأجهزة سطح المكتب

## 🚀 النشر على Firebase

### 1. تثبيت Firebase CLI
```bash
npm install -g firebase-tools
```

### 2. تسجيل الدخول إلى Firebase
```bash
firebase login
```

### 3. ربط المشروع
```bash
firebase init
# اختر Hosting, Firestore, Storage
# اختر مشروع Firebase الخاص بك
```

### 4. النشر
```bash
npm run deploy
```

أو خطوة بخطوة:
```bash
npm run build
firebase deploy
```

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. Commit التغييرات
4. Push إلى Branch
5. فتح Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT.

## 📞 الدعم

للدعم والاستفسارات:
- فتح Issue في GitHub
- التواصل عبر البريد الإلكتروني

---

**تم تطويره بـ ❤️ باستخدام Next.js و Firebase**
