# دليل البدء السريع - PDF Exam Generator

## ✅ التحقق من المتطلبات

تأكد من تثبيت:
- Node.js (v18 أو أحدث)
- npm أو yarn
- Git

## 🚀 البدء السريع (5 دقائق)

### 1. تثبيت المشروع
```bash
# استنساخ المشروع
git clone <repository-url>
cd pdf-exam-generator

# تثبيت التبعيات
npm install
```

### 2. إعداد Firebase (سريع)
```bash
# انسخ ملف البيئة
cp .env.example .env.local
```

**للاختبار السريع**: استخدم إعدادات Firebase التجريبية:
```env
NEXT_PUBLIC_FIREBASE_API_KEY=demo-key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=demo-project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=demo-project
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=demo-project.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=123456789
NEXT_PUBLIC_FIREBASE_APP_ID=1:123456789:web:abcdef
```

**للإنتاج**: اتبع [FIREBASE_SETUP.md](./FIREBASE_SETUP.md)

### 3. تشغيل المشروع
```bash
npm run dev
```

🎉 افتح http://localhost:3000

## 🧪 اختبار سريع

### تسجيل حساب جديد:
1. اذهب إلى `/auth/register`
2. أدخل بيانات تجريبية
3. اختر نوع الحساب (طالب/معلم)

### تجربة الوظائف:
- ✅ تسجيل الدخول/الخروج
- ✅ رفع ملف PDF
- ✅ إنشاء امتحان
- ✅ عرض النتائج

## 🔧 حل المشاكل الشائعة

### خطأ Firebase
```bash
# تأكد من صحة متغيرات البيئة
cat .env.local
```

### خطأ في التثبيت
```bash
# امسح node_modules وأعد التثبيت
rm -rf node_modules package-lock.json
npm install
```

### خطأ في المنفذ
```bash
# غير المنفذ
npm run dev -- -p 3001
```

## 📁 الملفات المهمة

```
pdf-exam-generator/
├── .env.local              # إعدادات Firebase
├── src/
│   ├── app/auth/           # صفحات المصادقة
│   ├── contexts/AuthContext.tsx  # إدارة المصادقة
│   ├── lib/firebase.ts     # إعدادات Firebase
│   └── middleware.ts       # حماية الصفحات
├── FIREBASE_SETUP.md       # دليل Firebase مفصل
└── README.md              # الدليل الكامل
```

## 🎯 الخطوات التالية

1. **إعداد Firebase الحقيقي**: اتبع [FIREBASE_SETUP.md](./FIREBASE_SETUP.md)
2. **تخصيص التصميم**: عدل ملفات Tailwind CSS
3. **إضافة ميزات**: استخدم مكونات React الموجودة
4. **النشر**: استخدم Firebase Hosting أو Vercel

## 📞 المساعدة

- 📖 [README.md](./README.md) - الدليل الكامل
- 🔥 [FIREBASE_SETUP.md](./FIREBASE_SETUP.md) - إعداد Firebase
- 🐛 GitHub Issues - للمشاكل التقنية

---

**نصيحة**: ابدأ بالإعدادات التجريبية للاختبار، ثم انتقل للإعدادات الحقيقية للإنتاج.
