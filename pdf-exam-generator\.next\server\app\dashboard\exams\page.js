(()=>{var e={};e.id=378,e.ids=[378],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13861:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},14260:(e,t,s)=>{Promise.resolve().then(s.bind(s,96056))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28559:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31158:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37366:e=>{"use strict";e.exports=require("dns")},40228:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},44638:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>j});var r=s(60687),a=s(43210),l=s(7613),i=s(28559),d=s(31158),c=s(96474),n=s(10022),o=s(53411);let x=(0,s(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]]);var m=s(41312),u=s(99270),h=s(81904),p=s(40228),g=s(13861),f=s(81620),b=s(85814),y=s.n(b);function j(){let{user:e}=(0,l.A)(),[t,s]=(0,a.useState)(""),[b,j]=(0,a.useState)("all"),v=[{id:1,title:"اختبار الرياضيات - الجبر المتقدم",subject:"رياضيات",createdDate:"2024-01-15",lastModified:"2024-01-20",questions:20,duration:45,attempts:15,averageScore:85,status:"published",difficulty:"متوسط"},{id:2,title:"اختبار الفيزياء - قوانين الحركة",subject:"فيزياء",createdDate:"2024-01-12",lastModified:"2024-01-19",questions:25,duration:60,attempts:12,averageScore:92,status:"published",difficulty:"صعب"},{id:3,title:"اختبار الكيمياء - الجدول الدوري",subject:"كيمياء",createdDate:"2024-01-10",lastModified:"2024-01-18",questions:15,duration:30,attempts:8,averageScore:78,status:"draft",difficulty:"سهل"},{id:4,title:"اختبار الأحياء - الخلية والوراثة",subject:"أحياء",createdDate:"2024-01-08",lastModified:"2024-01-17",questions:22,duration:50,attempts:18,averageScore:88,status:"published",difficulty:"متوسط"},{id:5,title:"اختبار التاريخ - الحضارة الإسلامية",subject:"تاريخ",createdDate:"2024-01-05",lastModified:"2024-01-16",questions:18,duration:40,attempts:10,averageScore:76,status:"archived",difficulty:"متوسط"}],N=v.filter(e=>{let s=e.title.toLowerCase().includes(t.toLowerCase())||e.subject.toLowerCase().includes(t.toLowerCase()),r="all"===b||e.status===b;return s&&r}),w=e=>{switch(e){case"published":return"bg-green-100 text-green-800";case"draft":return"bg-yellow-100 text-yellow-800";default:return"bg-gray-100 text-gray-800"}},A=e=>{switch(e){case"published":return"منشور";case"draft":return"مسودة";case"archived":return"مؤرشف";default:return"غير محدد"}},k=e=>{switch(e){case"سهل":return"text-green-600 bg-green-100";case"متوسط":return"text-yellow-600 bg-yellow-100";case"صعب":return"text-red-600 bg-red-100";default:return"text-gray-600 bg-gray-100"}},q={total:v.length,published:v.filter(e=>"published"===e.status).length,draft:v.filter(e=>"draft"===e.status).length,totalAttempts:v.reduce((e,t)=>e+t.attempts,0)};return(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("div",{className:"flex items-center",children:(0,r.jsxs)(y(),{href:"/dashboard",className:"flex items-center text-gray-600 hover:text-gray-900 transition-colors ml-4",children:[(0,r.jsx)(i.A,{className:"w-5 h-5 ml-2"}),"العودة للوحة التحكم"]})}),(0,r.jsxs)("div",{className:"flex items-center space-x-4 rtl:space-x-reverse",children:[(0,r.jsxs)("button",{className:"flex items-center px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,r.jsx)(d.A,{className:"w-4 h-4 ml-2"}),"تصدير"]}),(0,r.jsxs)(y(),{href:"/dashboard/upload",className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,r.jsx)(c.A,{className:"w-4 h-4 ml-2"}),"إنشاء اختبار جديد"]})]})]}),(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"إدارة الاختبارات"}),(0,r.jsx)("p",{className:"text-gray-600",children:"إنشاء وإدارة ومراقبة جميع الاختبارات الخاصة بك"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center ml-4",children:(0,r.jsx)(n.A,{className:"w-6 h-6 text-blue-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"إجمالي الاختبارات"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:q.total})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center ml-4",children:(0,r.jsx)(o.A,{className:"w-6 h-6 text-green-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"منشورة"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:q.published})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center ml-4",children:(0,r.jsx)(x,{className:"w-6 h-6 text-yellow-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"مسودات"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:q.draft})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center ml-4",children:(0,r.jsx)(m.A,{className:"w-6 h-6 text-purple-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"إجمالي المحاولات"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:q.totalAttempts})]})]})})]}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6",children:(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row items-start sm:items-center space-y-4 sm:space-y-0 sm:space-x-4 rtl:space-x-reverse",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(u.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),(0,r.jsx)("input",{type:"text",placeholder:"البحث في الاختبارات...",value:t,onChange:e=>s(e.target.value),className:"w-64 pl-4 pr-10 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]}),(0,r.jsxs)("select",{value:b,onChange:e=>j(e.target.value),className:"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[(0,r.jsx)("option",{value:"all",children:"جميع الحالات"}),(0,r.jsx)("option",{value:"published",children:"منشورة"}),(0,r.jsx)("option",{value:"draft",children:"مسودات"}),(0,r.jsx)("option",{value:"archived",children:"مؤرشفة"})]})]}),(0,r.jsxs)("div",{className:"text-sm text-gray-600",children:["عرض ",N.length," من ",v.length," اختبار"]})]})}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:N.map(e=>(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow",children:[(0,r.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center ml-3",children:(0,r.jsx)(n.A,{className:"w-5 h-5 text-blue-600"})}),(0,r.jsx)("div",{children:(0,r.jsx)("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${w(e.status)}`,children:A(e.status)})})]}),(0,r.jsx)("button",{className:"text-gray-400 hover:text-gray-600",children:(0,r.jsx)(h.A,{className:"w-5 h-5"})})]}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2 line-clamp-2",children:e.title}),(0,r.jsxs)("div",{className:"flex items-center space-x-4 rtl:space-x-reverse text-sm text-gray-500 mb-4",children:[(0,r.jsx)("span",{children:e.subject}),(0,r.jsx)("span",{children:"•"}),(0,r.jsx)("span",{className:`px-2 py-1 rounded-full text-xs ${k(e.difficulty)}`,children:e.difficulty})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4 mb-4",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:e.questions}),(0,r.jsx)("div",{className:"text-xs text-gray-500",children:"سؤال"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:e.duration}),(0,r.jsx)("div",{className:"text-xs text-gray-500",children:"دقيقة"})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-500 mb-4",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(m.A,{className:"w-4 h-4 ml-1"}),(0,r.jsxs)("span",{children:[e.attempts," محاولة"]})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(o.A,{className:"w-4 h-4 ml-1"}),(0,r.jsxs)("span",{children:[e.averageScore,"% متوسط"]})]})]}),(0,r.jsxs)("div",{className:"flex items-center text-xs text-gray-500 mb-4",children:[(0,r.jsx)(p.A,{className:"w-3 h-3 ml-1"}),(0,r.jsxs)("span",{children:["آخر تعديل: ",new Date(e.lastModified).toLocaleDateString("ar-SA")]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 rtl:space-x-reverse",children:[(0,r.jsxs)("button",{className:"flex-1 flex items-center justify-center px-3 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors",children:[(0,r.jsx)(g.A,{className:"w-4 h-4 ml-1"}),"عرض"]}),(0,r.jsx)("button",{className:"flex items-center justify-center px-3 py-2 bg-gray-100 text-gray-700 text-sm rounded-lg hover:bg-gray-200 transition-colors",children:(0,r.jsx)(x,{className:"w-4 h-4"})}),(0,r.jsx)("button",{className:"flex items-center justify-center px-3 py-2 bg-gray-100 text-gray-700 text-sm rounded-lg hover:bg-gray-200 transition-colors",children:(0,r.jsx)(f.A,{className:"w-4 h-4"})})]})]},e.id))})]})}},50644:(e,t,s)=>{Promise.resolve().then(s.bind(s,44638))},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},77724:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>n});var r=s(65239),a=s(48088),l=s(88170),i=s.n(l),d=s(30893),c={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>d[e]);s.d(t,c);let n={children:["",{children:["dashboard",{children:["exams",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,96056)),"D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\exams\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:"/manifest.webmanifest"}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"D:\\templete\\pdf-exam-generator\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:"/manifest.webmanifest"}}]}.children,o=["D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\exams\\page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/exams/page",pathname:"/dashboard/exams",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:n}})},79551:e=>{"use strict";e.exports=require("url")},81620:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("share-2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]])},81630:e=>{"use strict";e.exports=require("http")},81904:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("ellipsis-vertical",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"12",cy:"5",r:"1",key:"gxeob9"}],["circle",{cx:"12",cy:"19",r:"1",key:"lyex9k"}]])},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96056:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\templete\\\\pdf-exam-generator\\\\src\\\\app\\\\dashboard\\\\exams\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\exams\\page.tsx","default")},96474:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,248,658,647],()=>s(77724));module.exports=r})();