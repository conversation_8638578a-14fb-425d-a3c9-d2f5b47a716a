# دليل النشر على Firebase

## 📋 المتطلبات

1. **مشروع Firebase**: أنشئ مشروع في [Firebase Console](https://console.firebase.google.com/)
2. **Firebase CLI**: `npm install -g firebase-tools`
3. **إعدادات المشروع**: أضف إعدادات Firebase إلى `.env.local`

## 🔧 الإعداد الأولي

### 1. إنشاء مشروع Firebase
```bash
# اذهب إلى https://console.firebase.google.com/
# اضغط "Create a project"
# اتبع الخطوات لإنشاء المشروع
```

### 2. تفعيل الخدمات المطلوبة
في Firebase Console:
- **Authentication**: فعّل Email/Password, Google, Facebook
- **Firestore Database**: أنشئ قاعدة بيانات في production mode
- **Storage**: فعّل Firebase Storage

### 3. الحصول على إعدادات المشروع
```bash
# في Firebase Console > Project Settings > General
# انسخ Firebase SDK snippet (Config)
```

### 4. تحديث متغيرات البيئة
```env
# .env.local
NEXT_PUBLIC_FIREBASE_API_KEY=your_actual_api_key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project_id.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_project_id.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id
```

## 🚀 النشر

### الطريقة السريعة
```bash
# تسجيل الدخول
firebase login

# تحديث project ID في .firebaserc
# غيّر "your-firebase-project-id" إلى project ID الحقيقي

# النشر
npm run deploy
```

### الطريقة المفصلة

#### 1. تسجيل الدخول إلى Firebase
```bash
firebase login
```

#### 2. ربط المشروع
```bash
firebase use your-project-id
```

#### 3. بناء المشروع
```bash
npm run build
```

#### 4. النشر
```bash
firebase deploy
```

## 🔒 إعداد قواعد الأمان

### Firestore Rules
```javascript
// firestore.rules
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    match /exams/{examId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && resource.data.createdBy == request.auth.uid;
    }
  }
}
```

### Storage Rules
```javascript
// storage.rules
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    match /pdfs/{userId}/{allPaths=**} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
  }
}
```

## 🌐 إعداد النطاق المخصص

### 1. في Firebase Console
- اذهب إلى Hosting
- اضغط "Add custom domain"
- اتبع التعليمات لإضافة نطاقك

### 2. تحديث DNS
```
# أضف CNAME record في إعدادات النطاق
Type: CNAME
Name: www (أو subdomain)
Value: your-project-id.web.app
```

## 🔧 استكشاف الأخطاء

### خطأ في البناء
```bash
# امسح cache وأعد البناء
rm -rf .next out
npm run build
```

### خطأ في النشر
```bash
# تحقق من تسجيل الدخول
firebase login --reauth

# تحقق من project ID
firebase projects:list
firebase use your-project-id
```

### خطأ في الصلاحيات
```bash
# تحقق من قواعد Firestore و Storage
firebase deploy --only firestore:rules
firebase deploy --only storage
```

## 📊 مراقبة الأداء

### Firebase Analytics
```bash
# في Firebase Console > Analytics
# راقب عدد المستخدمين والجلسات
```

### Performance Monitoring
```bash
# في Firebase Console > Performance
# راقب سرعة التحميل والأداء
```

## 🔄 التحديثات

### نشر تحديث جديد
```bash
# بناء ونشر
npm run deploy

# أو نشر hosting فقط
firebase deploy --only hosting
```

### نشر قواعد فقط
```bash
firebase deploy --only firestore:rules,storage
```

## 📞 الدعم

إذا واجهت مشاكل:
1. تحقق من [Firebase Documentation](https://firebase.google.com/docs)
2. راجع [Firebase Status](https://status.firebase.google.com/)
3. افتح issue في GitHub

---

**ملاحظة**: تأكد من تحديث جميع الإعدادات قبل النشر للإنتاج.
