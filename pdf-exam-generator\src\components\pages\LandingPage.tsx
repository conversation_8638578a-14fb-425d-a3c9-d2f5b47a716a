'use client';

import dynamic from 'next/dynamic';
import { Header } from '@/components/landing/Header';
import { Hero } from '@/components/landing/Hero';

// Lazy load heavy components for better performance
const Features = dynamic(() => import('@/components/landing/Features').then(mod => ({ default: mod.Features })), {
  loading: () => <div className="h-96 bg-gray-50 dark:bg-gray-800 animate-pulse rounded-lg" />
});

const Pricing = dynamic(() => import('@/components/landing/Pricing').then(mod => ({ default: mod.Pricing })), {
  loading: () => <div className="h-96 bg-gray-50 dark:bg-gray-800 animate-pulse rounded-lg" />
});

const Footer = dynamic(() => import('@/components/landing/Footer').then(mod => ({ default: mod.Footer })), {
  loading: () => <div className="h-32 bg-gray-50 dark:bg-gray-800 animate-pulse" />
});
import { LanguageProvider } from '@/contexts/LanguageContext';

export function LandingPage() {
  return (
    <LanguageProvider>
      <div className="min-h-screen bg-white dark:bg-gray-900">
        <Header />
        <main>
          <Hero />
          <Features />
          <Pricing />
        </main>
        <Footer />
      </div>
    </LanguageProvider>
  );
}
