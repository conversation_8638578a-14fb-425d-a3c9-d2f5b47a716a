'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import LoadingSpinner from '@/components/ui/LoadingSpinner';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: 'student' | 'teacher' | 'admin';
  redirectTo?: string;
}

export default function ProtectedRoute({ 
  children, 
  requiredRole,
  redirectTo = '/auth/login' 
}: ProtectedRouteProps) {
  const { user, userProfile, loading, isAuthenticated } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading) {
      // If not authenticated, redirect to login
      if (!isAuthenticated) {
        router.push(redirectTo);
        return;
      }

      // If role is required and user doesn't have it, redirect to dashboard
      if (requiredRole && userProfile && userProfile.role !== requiredRole) {
        router.push('/dashboard');
        return;
      }
    }
  }, [user, userProfile, loading, isAuthenticated, requiredRole, router, redirectTo]);

  // Show loading spinner while checking authentication
  if (loading) {
    return <LoadingSpinner />;
  }

  // If not authenticated, don't render children
  if (!isAuthenticated) {
    return <LoadingSpinner />;
  }

  // If role is required and user doesn't have it, don't render children
  if (requiredRole && userProfile && userProfile.role !== requiredRole) {
    return <LoadingSpinner />;
  }

  // If everything is good, render children
  return <>{children}</>;
}
