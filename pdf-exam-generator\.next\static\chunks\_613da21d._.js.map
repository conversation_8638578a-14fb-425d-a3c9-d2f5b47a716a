{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/templete/pdf-exam-generator/src/utils/firebase-test.ts"], "sourcesContent": ["/**\n * Firebase Connection Test Utility\n * أداة اختبار الاتصال بـ Firebase\n */\n\nimport { auth, db, storage } from '@/lib/firebase';\nimport { connectAuthEmulator, signInAnonymously } from 'firebase/auth';\nimport { connectFirestoreEmulator, doc, setDoc, getDoc } from 'firebase/firestore';\nimport { connectStorageEmulator } from 'firebase/storage';\n\ninterface TestResult {\n  service: string;\n  status: 'success' | 'error' | 'warning';\n  message: string;\n  details?: any;\n}\n\nclass FirebaseTestSuite {\n  private results: TestResult[] = [];\n  private isEmulator = false;\n\n  constructor() {\n    // Check if running in development with emulator\n    this.isEmulator = process.env.NODE_ENV === 'development' && \n                     process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID === 'demo-project';\n  }\n\n  private addResult(service: string, status: 'success' | 'error' | 'warning', message: string, details?: any) {\n    this.results.push({ service, status, message, details });\n  }\n\n  /**\n   * Test Firebase Authentication\n   */\n  async testAuth(): Promise<void> {\n    try {\n      // Connect to emulator if in development\n      if (this.isEmulator && !auth.config.emulator) {\n        connectAuthEmulator(auth, 'http://localhost:9099');\n      }\n\n      // Test anonymous sign in\n      const userCredential = await signInAnonymously(auth);\n      \n      if (userCredential.user) {\n        this.addResult('Authentication', 'success', 'تم الاتصال بـ Firebase Auth بنجاح', {\n          uid: userCredential.user.uid,\n          isAnonymous: userCredential.user.isAnonymous\n        });\n        \n        // Sign out after test\n        await auth.signOut();\n      } else {\n        this.addResult('Authentication', 'error', 'فشل في إنشاء مستخدم مجهول');\n      }\n    } catch (error: any) {\n      this.addResult('Authentication', 'error', `خطأ في Firebase Auth: ${error.message}`, error);\n    }\n  }\n\n  /**\n   * Test Firestore Database\n   */\n  async testFirestore(): Promise<void> {\n    try {\n      // Connect to emulator if in development\n      if (this.isEmulator && !db._delegate._databaseId.projectId.includes('localhost')) {\n        connectFirestoreEmulator(db, 'localhost', 8080);\n      }\n\n      // Test document write\n      const testDocRef = doc(db, 'test', 'connection-test');\n      const testData = {\n        timestamp: new Date(),\n        message: 'Firebase connection test',\n        success: true\n      };\n\n      await setDoc(testDocRef, testData);\n      \n      // Test document read\n      const docSnap = await getDoc(testDocRef);\n      \n      if (docSnap.exists()) {\n        this.addResult('Firestore', 'success', 'تم الاتصال بـ Firestore بنجاح', {\n          documentId: docSnap.id,\n          data: docSnap.data()\n        });\n      } else {\n        this.addResult('Firestore', 'error', 'فشل في قراءة المستند من Firestore');\n      }\n    } catch (error: any) {\n      this.addResult('Firestore', 'error', `خطأ في Firestore: ${error.message}`, error);\n    }\n  }\n\n  /**\n   * Test Firebase Storage\n   */\n  async testStorage(): Promise<void> {\n    try {\n      // Connect to emulator if in development\n      if (this.isEmulator && !storage.app.options.storageBucket?.includes('localhost')) {\n        connectStorageEmulator(storage, 'localhost', 9199);\n      }\n\n      // Test storage reference creation\n      const { ref } = await import('firebase/storage');\n      const testRef = ref(storage, 'test/connection-test.txt');\n      \n      if (testRef) {\n        this.addResult('Storage', 'success', 'تم الاتصال بـ Firebase Storage بنجاح', {\n          bucket: storage.app.options.storageBucket,\n          path: testRef.fullPath\n        });\n      } else {\n        this.addResult('Storage', 'error', 'فشل في إنشاء مرجع التخزين');\n      }\n    } catch (error: any) {\n      this.addResult('Storage', 'error', `خطأ في Firebase Storage: ${error.message}`, error);\n    }\n  }\n\n  /**\n   * Test Firebase Configuration\n   */\n  testConfig(): void {\n    const config = {\n      apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,\n      authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,\n      projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,\n      storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,\n      messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,\n      appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID\n    };\n\n    const missingVars = Object.entries(config)\n      .filter(([key, value]) => !value || value.includes('your_') || value.includes('demo-'))\n      .map(([key]) => key);\n\n    if (missingVars.length === 0) {\n      this.addResult('Configuration', 'success', 'جميع متغيرات Firebase مُعرَّفة', config);\n    } else if (config.projectId === 'demo-project') {\n      this.addResult('Configuration', 'warning', 'تستخدم إعدادات تجريبية - قم بتحديثها للإنتاج', {\n        missingVars,\n        isDemo: true\n      });\n    } else {\n      this.addResult('Configuration', 'error', 'متغيرات Firebase مفقودة أو غير صحيحة', {\n        missingVars\n      });\n    }\n  }\n\n  /**\n   * Run all tests\n   */\n  async runAllTests(): Promise<TestResult[]> {\n    console.log('🔥 بدء اختبار Firebase...\\n');\n\n    // Test configuration first\n    this.testConfig();\n\n    // Only run service tests if config is valid\n    const configResult = this.results.find(r => r.service === 'Configuration');\n    if (configResult && configResult.status !== 'error') {\n      await this.testAuth();\n      await this.testFirestore();\n      await this.testStorage();\n    }\n\n    return this.results;\n  }\n\n  /**\n   * Display test results\n   */\n  displayResults(): void {\n    console.log('📊 نتائج اختبار Firebase:\\n');\n\n    this.results.forEach(result => {\n      const icon = result.status === 'success' ? '✅' : \n                   result.status === 'warning' ? '⚠️' : '❌';\n      \n      console.log(`${icon} ${result.service}: ${result.message}`);\n      \n      if (result.details && process.env.NODE_ENV === 'development') {\n        console.log(`   التفاصيل:`, result.details);\n      }\n    });\n\n    const summary = {\n      success: this.results.filter(r => r.status === 'success').length,\n      warning: this.results.filter(r => r.status === 'warning').length,\n      error: this.results.filter(r => r.status === 'error').length\n    };\n\n    console.log('\\n📈 الملخص:');\n    console.log(`✅ نجح: ${summary.success}`);\n    console.log(`⚠️ تحذيرات: ${summary.warning}`);\n    console.log(`❌ أخطاء: ${summary.error}`);\n\n    if (summary.error === 0 && summary.warning === 0) {\n      console.log('\\n🎉 Firebase جاهز للاستخدام!');\n    } else if (summary.error === 0) {\n      console.log('\\n👍 Firebase يعمل مع بعض التحذيرات');\n    } else {\n      console.log('\\n🔧 يحتاج Firebase إلى إصلاح قبل الاستخدام');\n    }\n  }\n}\n\n// Export for use in components or pages\nexport { FirebaseTestSuite };\n\n// Export a simple test function\nexport async function testFirebaseConnection(): Promise<boolean> {\n  const testSuite = new FirebaseTestSuite();\n  const results = await testSuite.runAllTests();\n  testSuite.displayResults();\n  \n  return results.every(result => result.status !== 'error');\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAoBqB;AAlBtB;AACA;AAAA;AAAA;AACA;AAAA;AACA;AAAA;;;;;AASA,MAAM;IACI,UAAwB,EAAE,CAAC;IAC3B,aAAa,MAAM;IAE3B,aAAc;QACZ,gDAAgD;QAChD,IAAI,CAAC,UAAU,GAAG,oDAAyB,iBAC1B,wDAAgD;IACnE;IAEQ,UAAU,OAAe,EAAE,MAAuC,EAAE,OAAe,EAAE,OAAa,EAAE;QAC1G,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;YAAE;YAAS;YAAQ;YAAS;QAAQ;IACxD;IAEA;;GAEC,GACD,MAAM,WAA0B;QAC9B,IAAI;YACF,wCAAwC;YACxC,IAAI,IAAI,CAAC,UAAU,IAAI,CAAC,yHAAA,CAAA,OAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;gBAC5C,CAAA,GAAA,yNAAA,CAAA,sBAAmB,AAAD,EAAE,yHAAA,CAAA,OAAI,EAAE;YAC5B;YAEA,yBAAyB;YACzB,MAAM,iBAAiB,MAAM,CAAA,GAAA,wNAAA,CAAA,oBAAiB,AAAD,EAAE,yHAAA,CAAA,OAAI;YAEnD,IAAI,eAAe,IAAI,EAAE;gBACvB,IAAI,CAAC,SAAS,CAAC,kBAAkB,WAAW,qCAAqC;oBAC/E,KAAK,eAAe,IAAI,CAAC,GAAG;oBAC5B,aAAa,eAAe,IAAI,CAAC,WAAW;gBAC9C;gBAEA,sBAAsB;gBACtB,MAAM,yHAAA,CAAA,OAAI,CAAC,OAAO;YACpB,OAAO;gBACL,IAAI,CAAC,SAAS,CAAC,kBAAkB,SAAS;YAC5C;QACF,EAAE,OAAO,OAAY;YACnB,IAAI,CAAC,SAAS,CAAC,kBAAkB,SAAS,CAAC,sBAAsB,EAAE,MAAM,OAAO,EAAE,EAAE;QACtF;IACF;IAEA;;GAEC,GACD,MAAM,gBAA+B;QACnC,IAAI;YACF,wCAAwC;YACxC,IAAI,IAAI,CAAC,UAAU,IAAI,CAAC,yHAAA,CAAA,KAAE,CAAC,SAAS,CAAC,WAAW,CAAC,SAAS,CAAC,QAAQ,CAAC,cAAc;gBAChF,CAAA,GAAA,sKAAA,CAAA,2BAAwB,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,aAAa;YAC5C;YAEA,sBAAsB;YACtB,MAAM,aAAa,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,QAAQ;YACnC,MAAM,WAAW;gBACf,WAAW,IAAI;gBACf,SAAS;gBACT,SAAS;YACX;YAEA,MAAM,CAAA,GAAA,sKAAA,CAAA,SAAM,AAAD,EAAE,YAAY;YAEzB,qBAAqB;YACrB,MAAM,UAAU,MAAM,CAAA,GAAA,sKAAA,CAAA,SAAM,AAAD,EAAE;YAE7B,IAAI,QAAQ,MAAM,IAAI;gBACpB,IAAI,CAAC,SAAS,CAAC,aAAa,WAAW,iCAAiC;oBACtE,YAAY,QAAQ,EAAE;oBACtB,MAAM,QAAQ,IAAI;gBACpB;YACF,OAAO;gBACL,IAAI,CAAC,SAAS,CAAC,aAAa,SAAS;YACvC;QACF,EAAE,OAAO,OAAY;YACnB,IAAI,CAAC,SAAS,CAAC,aAAa,SAAS,CAAC,kBAAkB,EAAE,MAAM,OAAO,EAAE,EAAE;QAC7E;IACF;IAEA;;GAEC,GACD,MAAM,cAA6B;QACjC,IAAI;YACF,wCAAwC;YACxC,IAAI,IAAI,CAAC,UAAU,IAAI,CAAC,yHAAA,CAAA,UAAO,CAAC,GAAG,CAAC,OAAO,CAAC,aAAa,EAAE,SAAS,cAAc;gBAChF,CAAA,GAAA,oKAAA,CAAA,yBAAsB,AAAD,EAAE,yHAAA,CAAA,UAAO,EAAE,aAAa;YAC/C;YAEA,kCAAkC;YAClC,MAAM,EAAE,GAAG,EAAE,GAAG;YAChB,MAAM,UAAU,IAAI,yHAAA,CAAA,UAAO,EAAE;YAE7B,IAAI,SAAS;gBACX,IAAI,CAAC,SAAS,CAAC,WAAW,WAAW,wCAAwC;oBAC3E,QAAQ,yHAAA,CAAA,UAAO,CAAC,GAAG,CAAC,OAAO,CAAC,aAAa;oBACzC,MAAM,QAAQ,QAAQ;gBACxB;YACF,OAAO;gBACL,IAAI,CAAC,SAAS,CAAC,WAAW,SAAS;YACrC;QACF,EAAE,OAAO,OAAY;YACnB,IAAI,CAAC,SAAS,CAAC,WAAW,SAAS,CAAC,yBAAyB,EAAE,MAAM,OAAO,EAAE,EAAE;QAClF;IACF;IAEA;;GAEC,GACD,aAAmB;QACjB,MAAM,SAAS;YACb,MAAM;YACN,UAAU;YACV,SAAS;YACT,aAAa;YACb,iBAAiB;YACjB,KAAK;QACP;QAEA,MAAM,cAAc,OAAO,OAAO,CAAC,QAChC,MAAM,CAAC,CAAC,CAAC,KAAK,MAAM,GAAK,CAAC,SAAS,MAAM,QAAQ,CAAC,YAAY,MAAM,QAAQ,CAAC,UAC7E,GAAG,CAAC,CAAC,CAAC,IAAI,GAAK;QAElB,IAAI,YAAY,MAAM,KAAK,GAAG;YAC5B,IAAI,CAAC,SAAS,CAAC,iBAAiB,WAAW,kCAAkC;QAC/E,OAAO,IAAI,OAAO,SAAS,KAAK,gBAAgB;YAC9C,IAAI,CAAC,SAAS,CAAC,iBAAiB,WAAW,gDAAgD;gBACzF;gBACA,QAAQ;YACV;QACF,OAAO;YACL,IAAI,CAAC,SAAS,CAAC,iBAAiB,SAAS,wCAAwC;gBAC/E;YACF;QACF;IACF;IAEA;;GAEC,GACD,MAAM,cAAqC;QACzC,QAAQ,GAAG,CAAC;QAEZ,2BAA2B;QAC3B,IAAI,CAAC,UAAU;QAEf,4CAA4C;QAC5C,MAAM,eAAe,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,OAAO,KAAK;QAC1D,IAAI,gBAAgB,aAAa,MAAM,KAAK,SAAS;YACnD,MAAM,IAAI,CAAC,QAAQ;YACnB,MAAM,IAAI,CAAC,aAAa;YACxB,MAAM,IAAI,CAAC,WAAW;QACxB;QAEA,OAAO,IAAI,CAAC,OAAO;IACrB;IAEA;;GAEC,GACD,iBAAuB;QACrB,QAAQ,GAAG,CAAC;QAEZ,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;YACnB,MAAM,OAAO,OAAO,MAAM,KAAK,YAAY,MAC9B,OAAO,MAAM,KAAK,YAAY,OAAO;YAElD,QAAQ,GAAG,CAAC,GAAG,KAAK,CAAC,EAAE,OAAO,OAAO,CAAC,EAAE,EAAE,OAAO,OAAO,EAAE;YAE1D,IAAI,OAAO,OAAO,IAAI,oDAAyB,eAAe;gBAC5D,QAAQ,GAAG,CAAC,CAAC,YAAY,CAAC,EAAE,OAAO,OAAO;YAC5C;QACF;QAEA,MAAM,UAAU;YACd,SAAS,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW,MAAM;YAChE,SAAS,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW,MAAM;YAChE,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,SAAS,MAAM;QAC9D;QAEA,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ,OAAO,EAAE;QACvC,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,QAAQ,OAAO,EAAE;QAC5C,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,QAAQ,KAAK,EAAE;QAEvC,IAAI,QAAQ,KAAK,KAAK,KAAK,QAAQ,OAAO,KAAK,GAAG;YAChD,QAAQ,GAAG,CAAC;QACd,OAAO,IAAI,QAAQ,KAAK,KAAK,GAAG;YAC9B,QAAQ,GAAG,CAAC;QACd,OAAO;YACL,QAAQ,GAAG,CAAC;QACd;IACF;AACF;;AAMO,eAAe;IACpB,MAAM,YAAY,IAAI;IACtB,MAAM,UAAU,MAAM,UAAU,WAAW;IAC3C,UAAU,cAAc;IAExB,OAAO,QAAQ,KAAK,CAAC,CAAA,SAAU,OAAO,MAAM,KAAK;AACnD", "debugId": null}}, {"offset": {"line": 204, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/templete/pdf-exam-generator/src/app/test-firebase/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { FirebaseTestSuite } from '@/utils/firebase-test';\nimport { CheckCircle, XCircle, AlertTriangle, Play, RefreshCw } from 'lucide-react';\n\ninterface TestResult {\n  service: string;\n  status: 'success' | 'error' | 'warning';\n  message: string;\n  details?: any;\n}\n\nexport default function TestFirebasePage() {\n  const [results, setResults] = useState<TestResult[]>([]);\n  const [isRunning, setIsRunning] = useState(false);\n  const [hasRun, setHasRun] = useState(false);\n\n  const runTests = async () => {\n    setIsRunning(true);\n    setResults([]);\n    \n    try {\n      const testSuite = new FirebaseTestSuite();\n      const testResults = await testSuite.runAllTests();\n      setResults(testResults);\n      setHasRun(true);\n    } catch (error) {\n      console.error('Error running tests:', error);\n      setResults([{\n        service: 'Test Suite',\n        status: 'error',\n        message: 'فشل في تشغيل الاختبارات',\n        details: error\n      }]);\n    } finally {\n      setIsRunning(false);\n    }\n  };\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'success':\n        return <CheckCircle className=\"h-5 w-5 text-green-500\" />;\n      case 'warning':\n        return <AlertTriangle className=\"h-5 w-5 text-yellow-500\" />;\n      case 'error':\n        return <XCircle className=\"h-5 w-5 text-red-500\" />;\n      default:\n        return null;\n    }\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'success':\n        return 'border-green-200 bg-green-50';\n      case 'warning':\n        return 'border-yellow-200 bg-yellow-50';\n      case 'error':\n        return 'border-red-200 bg-red-50';\n      default:\n        return 'border-gray-200 bg-gray-50';\n    }\n  };\n\n  const summary = {\n    success: results.filter(r => r.status === 'success').length,\n    warning: results.filter(r => r.status === 'warning').length,\n    error: results.filter(r => r.status === 'error').length\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 py-8\">\n      <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Header */}\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">\n            اختبار اتصال Firebase\n          </h1>\n          <p className=\"text-gray-600\">\n            تحقق من صحة إعدادات Firebase والاتصال بالخدمات\n          </p>\n        </div>\n\n        {/* Test Controls */}\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h2 className=\"text-lg font-semibold text-gray-900 mb-1\">\n                تشغيل الاختبارات\n              </h2>\n              <p className=\"text-sm text-gray-600\">\n                اختبار الاتصال بـ Authentication و Firestore و Storage\n              </p>\n            </div>\n            <button\n              onClick={runTests}\n              disabled={isRunning}\n              className=\"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n            >\n              {isRunning ? (\n                <>\n                  <RefreshCw className=\"h-4 w-4 mr-2 animate-spin\" />\n                  جاري الاختبار...\n                </>\n              ) : (\n                <>\n                  <Play className=\"h-4 w-4 mr-2\" />\n                  {hasRun ? 'إعادة الاختبار' : 'بدء الاختبار'}\n                </>\n              )}\n            </button>\n          </div>\n        </div>\n\n        {/* Results Summary */}\n        {hasRun && (\n          <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">ملخص النتائج</h3>\n            <div className=\"grid grid-cols-3 gap-4\">\n              <div className=\"text-center p-4 bg-green-50 rounded-lg border border-green-200\">\n                <div className=\"text-2xl font-bold text-green-600\">{summary.success}</div>\n                <div className=\"text-sm text-green-700\">نجح</div>\n              </div>\n              <div className=\"text-center p-4 bg-yellow-50 rounded-lg border border-yellow-200\">\n                <div className=\"text-2xl font-bold text-yellow-600\">{summary.warning}</div>\n                <div className=\"text-sm text-yellow-700\">تحذيرات</div>\n              </div>\n              <div className=\"text-center p-4 bg-red-50 rounded-lg border border-red-200\">\n                <div className=\"text-2xl font-bold text-red-600\">{summary.error}</div>\n                <div className=\"text-sm text-red-700\">أخطاء</div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Test Results */}\n        {results.length > 0 && (\n          <div className=\"space-y-4\">\n            <h3 className=\"text-lg font-semibold text-gray-900\">نتائج الاختبارات</h3>\n            {results.map((result, index) => (\n              <div\n                key={index}\n                className={`border rounded-lg p-4 ${getStatusColor(result.status)}`}\n              >\n                <div className=\"flex items-start\">\n                  <div className=\"flex-shrink-0 mt-0.5\">\n                    {getStatusIcon(result.status)}\n                  </div>\n                  <div className=\"mr-3 flex-1\">\n                    <h4 className=\"font-medium text-gray-900\">{result.service}</h4>\n                    <p className=\"text-sm text-gray-700 mt-1\">{result.message}</p>\n                    {result.details && (\n                      <details className=\"mt-2\">\n                        <summary className=\"text-xs text-gray-500 cursor-pointer hover:text-gray-700\">\n                          عرض التفاصيل\n                        </summary>\n                        <pre className=\"mt-2 text-xs bg-gray-100 p-2 rounded overflow-auto max-h-32\">\n                          {JSON.stringify(result.details, null, 2)}\n                        </pre>\n                      </details>\n                    )}\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        )}\n\n        {/* Instructions */}\n        {!hasRun && (\n          <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-6\">\n            <h3 className=\"text-lg font-semibold text-blue-900 mb-2\">\n              تعليمات الاختبار\n            </h3>\n            <ul className=\"text-sm text-blue-800 space-y-1\">\n              <li>• تأكد من وجود ملف .env.local مع إعدادات Firebase</li>\n              <li>• تحقق من صحة متغيرات البيئة</li>\n              <li>• تأكد من تفعيل خدمات Firebase في Console</li>\n              <li>• للإعدادات التجريبية، استخدم demo-project كـ project ID</li>\n            </ul>\n          </div>\n        )}\n\n        {/* Overall Status */}\n        {hasRun && (\n          <div className=\"mt-6 text-center\">\n            {summary.error === 0 && summary.warning === 0 && (\n              <div className=\"text-green-600 font-semibold\">\n                🎉 Firebase جاهز للاستخدام!\n              </div>\n            )}\n            {summary.error === 0 && summary.warning > 0 && (\n              <div className=\"text-yellow-600 font-semibold\">\n                👍 Firebase يعمل مع بعض التحذيرات\n              </div>\n            )}\n            {summary.error > 0 && (\n              <div className=\"text-red-600 font-semibold\">\n                🔧 يحتاج Firebase إلى إصلاح قبل الاستخدام\n              </div>\n            )}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;;;AAJA;;;;AAae,SAAS;;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,WAAW;QACf,aAAa;QACb,WAAW,EAAE;QAEb,IAAI;YACF,MAAM,YAAY,IAAI,mIAAA,CAAA,oBAAiB;YACvC,MAAM,cAAc,MAAM,UAAU,WAAW;YAC/C,WAAW;YACX,UAAU;QACZ,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,WAAW;gBAAC;oBACV,SAAS;oBACT,QAAQ;oBACR,SAAS;oBACT,SAAS;gBACX;aAAE;QACJ,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,2NAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC,KAAK;gBACH,qBAAO,6LAAC,+MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5B;gBACE,OAAO;QACX;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,UAAU;QACd,SAAS,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW,MAAM;QAC3D,SAAS,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW,MAAM;QAC3D,OAAO,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,SAAS,MAAM;IACzD;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;8BAM/B,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA2C;;;;;;kDAGzD,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAIvC,6LAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;0CAET,0BACC;;sDACE,6LAAC,mNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAA8B;;iEAIrD;;sDACE,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCACf,SAAS,mBAAmB;;;;;;;;;;;;;;;;;;;gBAQtC,wBACC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA2C;;;;;;sCACzD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAqC,QAAQ,OAAO;;;;;;sDACnE,6LAAC;4CAAI,WAAU;sDAAyB;;;;;;;;;;;;8CAE1C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAsC,QAAQ,OAAO;;;;;;sDACpE,6LAAC;4CAAI,WAAU;sDAA0B;;;;;;;;;;;;8CAE3C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAmC,QAAQ,KAAK;;;;;;sDAC/D,6LAAC;4CAAI,WAAU;sDAAuB;;;;;;;;;;;;;;;;;;;;;;;;gBAO7C,QAAQ,MAAM,GAAG,mBAChB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAsC;;;;;;wBACnD,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,6LAAC;gCAEC,WAAW,CAAC,sBAAsB,EAAE,eAAe,OAAO,MAAM,GAAG;0CAEnE,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACZ,cAAc,OAAO,MAAM;;;;;;sDAE9B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAA6B,OAAO,OAAO;;;;;;8DACzD,6LAAC;oDAAE,WAAU;8DAA8B,OAAO,OAAO;;;;;;gDACxD,OAAO,OAAO,kBACb,6LAAC;oDAAQ,WAAU;;sEACjB,6LAAC;4DAAQ,WAAU;sEAA2D;;;;;;sEAG9E,6LAAC;4DAAI,WAAU;sEACZ,KAAK,SAAS,CAAC,OAAO,OAAO,EAAE,MAAM;;;;;;;;;;;;;;;;;;;;;;;;+BAhB3C;;;;;;;;;;;gBA4BZ,CAAC,wBACA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA2C;;;;;;sCAGzD,6LAAC;4BAAG,WAAU;;8CACZ,6LAAC;8CAAG;;;;;;8CACJ,6LAAC;8CAAG;;;;;;8CACJ,6LAAC;8CAAG;;;;;;8CACJ,6LAAC;8CAAG;;;;;;;;;;;;;;;;;;gBAMT,wBACC,6LAAC;oBAAI,WAAU;;wBACZ,QAAQ,KAAK,KAAK,KAAK,QAAQ,OAAO,KAAK,mBAC1C,6LAAC;4BAAI,WAAU;sCAA+B;;;;;;wBAI/C,QAAQ,KAAK,KAAK,KAAK,QAAQ,OAAO,GAAG,mBACxC,6LAAC;4BAAI,WAAU;sCAAgC;;;;;;wBAIhD,QAAQ,KAAK,GAAG,mBACf,6LAAC;4BAAI,WAAU;sCAA6B;;;;;;;;;;;;;;;;;;;;;;;AAS1D;GAnMwB;KAAA", "debugId": null}}, {"offset": {"line": 721, "column": 0}, "map": {"version": 3, "file": "circle-check-big.js", "sources": ["file:///D:/templete/pdf-exam-generator/node_modules/lucide-react/src/icons/circle-check-big.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M21.801 10A10 10 0 1 1 17 3.335', key: 'yps3ct' }],\n  ['path', { d: 'm9 11 3 3L22 4', key: '1pflzl' }],\n];\n\n/**\n * @component @name CircleCheckBig\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEuODAxIDEwQTEwIDEwIDAgMSAxIDE3IDMuMzM1IiAvPgogIDxwYXRoIGQ9Im05IDExIDMgM0wyMiA0IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/circle-check-big\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CircleCheckBig = createLucideIcon('circle-check-big', __iconNode);\n\nexport default CircleCheckBig;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAmC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAChE;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CACjD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,cAAA,CAAiB,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAoB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 767, "column": 0}, "map": {"version": 3, "file": "circle-x.js", "sources": ["file:///D:/templete/pdf-exam-generator/node_modules/lucide-react/src/icons/circle-x.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['path', { d: 'm15 9-6 6', key: '1uzhvr' }],\n  ['path', { d: 'm9 9 6 6', key: 'z0biqf' }],\n];\n\n/**\n * @component @name CircleX\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8cGF0aCBkPSJtMTUgOS02IDYiIC8+CiAgPHBhdGggZD0ibTkgOSA2IDYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/circle-x\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CircleX = createLucideIcon('circle-x', __iconNode);\n\nexport default CircleX;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACzD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1C;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC3C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAU,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAiB,AAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAY,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 822, "column": 0}, "map": {"version": 3, "file": "triangle-alert.js", "sources": ["file:///D:/templete/pdf-exam-generator/node_modules/lucide-react/src/icons/triangle-alert.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'm21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3',\n      key: 'wmoenq',\n    },\n  ],\n  ['path', { d: 'M12 9v4', key: 'juzpu7' }],\n  ['path', { d: 'M12 17h.01', key: 'p32p05' }],\n];\n\n/**\n * @component @name TriangleAlert\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMjEuNzMgMTgtOC0xNGEyIDIgMCAwIDAtMy40OCAwbC04IDE0QTIgMiAwIDAgMCA0IDIxaDE2YTIgMiAwIDAgMCAxLjczLTMiIC8+CiAgPHBhdGggZD0iTTEyIDl2NCIgLz4KICA8cGF0aCBkPSJNMTIgMTdoLjAxIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/triangle-alert\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst TriangleAlert = createLucideIcon('triangle-alert', __iconNode);\n\nexport default TriangleAlert;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KAET;IACA;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxC;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC7C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,aAAA,CAAgB,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAA,AAAjB,CAAA,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAkB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 875, "column": 0}, "map": {"version": 3, "file": "play.js", "sources": ["file:///D:/templete/pdf-exam-generator/node_modules/lucide-react/src/icons/play.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['polygon', { points: '6 3 20 12 6 21 6 3', key: '1oa8hb' }]];\n\n/**\n * @component @name Play\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cG9seWdvbiBwb2ludHM9IjYgMyAyMCAxMiA2IDIxIDYgMyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/play\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Play = createLucideIcon('play', __iconNode);\n\nexport default Play;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB;IAAC;QAAC,SAAW,CAAA;QAAA,CAAA;YAAE,QAAQ,oBAAsB,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS;QAAA,CAAC;KAAC;CAAA;AAa3F,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 914, "column": 0}, "map": {"version": 3, "file": "refresh-cw.js", "sources": ["file:///D:/templete/pdf-exam-generator/node_modules/lucide-react/src/icons/refresh-cw.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8', key: 'v9h5vc' }],\n  ['path', { d: 'M21 3v5h-5', key: '1q7to0' }],\n  ['path', { d: 'M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16', key: '3uifl3' }],\n  ['path', { d: 'M8 16H3v5', key: '1cv678' }],\n];\n\n/**\n * @component @name RefreshCw\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMyAxMmE5IDkgMCAwIDEgOS05IDkuNzUgOS43NSAwIDAgMSA2Ljc0IDIuNzRMMjEgOCIgLz4KICA8cGF0aCBkPSJNMjEgM3Y1aC01IiAvPgogIDxwYXRoIGQ9Ik0yMSAxMmE5IDkgMCAwIDEtOSA5IDkuNzUgOS43NSAwIDAgMS02Ljc0LTIuNzRMMyAxNiIgLz4KICA8cGF0aCBkPSJNOCAxNkgzdjUiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/refresh-cw\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst RefreshCw = createLucideIcon('refresh-cw', __iconNode);\n\nexport default RefreshCw;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAsD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACnF;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC3C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAuD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACpF;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC5C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAY,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAc,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}