(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[859],{306:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("log-in",[["path",{d:"m10 17 5-5-5-5",key:"1bsop3"}],["path",{d:"M15 12H3",key:"6jk70r"}],["path",{d:"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4",key:"u53s6r"}]])},844:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>g,A:()=>y});var s=r(5155),a=r(2115),l=r(6203),o=r(5317),i=r(3915),n=r(858);let c={apiKey:"demo-key",authDomain:"demo-project.firebaseapp.com",projectId:"demo-project",storageBucket:"demo-project.appspot.com",messagingSenderId:"*********",appId:"1:*********:web:abcdef",measurementId:r(9509).env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID||"G-ABCDEF"},d=0===(0,i.Dk)().length?(0,i.Wp)(c):(0,i.Dk)()[0],u=(0,l.xI)(d),h=(0,o.aU)(d);(0,n.c7)(d);let m=new l.HF,x=new l.sk;m.setCustomParameters({prompt:"select_account"}),x.setCustomParameters({display:"popup"});let f=(0,a.createContext)(void 0),y=()=>{let e=(0,a.useContext)(f);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e},g=e=>{let{children:t}=e,[r,i]=(0,a.useState)(null),[n,c]=(0,a.useState)(null),[d,y]=(0,a.useState)(!0),g=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!e)return;let r=(0,o.H9)(h,"users",e.uid),s=await (0,o.x7)(r);if(s.exists()){let e={...s.data(),lastLoginAt:new Date};await (0,o.BN)(r,e,{merge:!0}),c(e)}else{let{displayName:s,email:a,photoURL:l}=e,i=new Date,n={uid:e.uid,email:a||"",displayName:s||"",photoURL:l||void 0,role:t.role||"student",createdAt:i,lastLoginAt:i,preferences:{language:"ar",theme:"light",notifications:!0},stats:{totalExams:0,averageScore:0,totalQuestions:0,studyTime:0},...t};try{await (0,o.BN)(r,n),c(n)}catch(e){console.error("Error creating user profile:",e)}}},p=async(e,t)=>{y(!0);try{let r=await (0,l.x9)(u,e,t);await g(r.user)}catch(e){throw console.error("Error signing in:",e),e}finally{y(!1)}},b=async(e,t)=>{try{return await p(e,t),!0}catch(e){return!1}},w=async(e,t,r,s)=>{y(!0);try{let a=await (0,l.eJ)(u,e,t);await (0,l.r7)(a.user,{displayName:r}),await g(a.user,{role:s,displayName:r})}catch(e){throw console.error("Error signing up:",e),e}finally{y(!1)}},v=async()=>{y(!0);try{let e=await (0,l.df)(u,m);await g(e.user)}catch(e){throw console.error("Error signing in with Google:",e),e}finally{y(!1)}},j=async()=>{y(!0);try{let e=await (0,l.df)(u,x);await g(e.user)}catch(e){throw console.error("Error signing in with Facebook:",e),e}finally{y(!1)}},N=async()=>{y(!0);try{await (0,l.CI)(u),i(null),c(null),localStorage.removeItem("examResults"),localStorage.removeItem("examAnswers"),localStorage.removeItem("uploadedPDF"),localStorage.removeItem("examConfig")}catch(e){throw console.error("Error signing out:",e),e}finally{y(!1)}},k=async e=>{try{await (0,l.J1)(u,e)}catch(e){throw console.error("Error sending password reset email:",e),e}},A=async e=>{if(r)try{let t=(0,o.H9)(h,"users",r.uid);await (0,o.BN)(t,e,{merge:!0}),n&&c({...n,...e})}catch(e){throw console.error("Error updating user profile:",e),e}};return(0,a.useEffect)(()=>(0,l.hg)(u,async e=>{e?(i(e),await g(e)):(i(null),c(null)),y(!1)}),[]),(0,s.jsx)(f.Provider,{value:{user:r,userProfile:n,loading:d,isAuthenticated:!!r,signIn:p,signUp:w,signInWithGoogle:v,signInWithFacebook:j,logout:N,resetPassword:k,updateUserProfile:A,login:b},children:t})}},2657:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},2919:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},4177:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>x});var s=r(5155),a=r(2115),l=r(5695),o=r(6874),i=r.n(o),n=r(844),c=r(306),d=r(8883),u=r(2919),h=r(8749),m=r(2657);function x(){let[e,t]=(0,a.useState)(""),[r,o]=(0,a.useState)(""),[x,f]=(0,a.useState)(!1),[y,g]=(0,a.useState)(""),[p,b]=(0,a.useState)(!1),{signIn:w,signInWithGoogle:v,signInWithFacebook:j}=(0,n.A)(),N=(0,l.useRouter)(),k=async t=>{t.preventDefault(),g(""),b(!0);try{await w(e,r),N.push("/dashboard")}catch(e){g(E(e.code||"unknown-error"))}finally{b(!1)}},A=async()=>{g(""),b(!0);try{await v(),N.push("/dashboard")}catch(e){g(E(e.code||"unknown-error"))}finally{b(!1)}},C=async()=>{g(""),b(!0);try{await j(),N.push("/dashboard")}catch(e){g(E(e.code||"unknown-error"))}finally{b(!1)}},E=e=>{switch(e){case"auth/user-not-found":return"لا يوجد حساب مرتبط بهذا البريد الإلكتروني";case"auth/wrong-password":return"كلمة المرور غير صحيحة";case"auth/invalid-email":return"البريد الإلكتروني غير صالح";case"auth/user-disabled":return"تم تعطيل هذا الحساب";case"auth/too-many-requests":return"تم تجاوز عدد المحاولات المسموح. حاول مرة أخرى لاحقاً";default:return"حدث خطأ أثناء تسجيل الدخول. حاول مرة أخرى"}};return(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4",children:(0,s.jsx)("div",{className:"max-w-md w-full space-y-8",children:(0,s.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl p-8",children:[(0,s.jsxs)("div",{className:"text-center mb-8",children:[(0,s.jsx)("div",{className:"mx-auto h-12 w-12 bg-blue-600 rounded-xl flex items-center justify-center mb-4",children:(0,s.jsx)(c.A,{className:"h-6 w-6 text-white"})}),(0,s.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-2",children:"تسجيل الدخول"}),(0,s.jsx)("p",{className:"text-gray-600",children:"ادخل إلى حسابك للوصول إلى لوحة التحكم"})]}),y&&(0,s.jsx)("div",{className:"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg",children:(0,s.jsx)("p",{className:"text-red-600 text-sm text-center",children:y})}),(0,s.jsxs)("form",{onSubmit:k,className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"البريد الإلكتروني"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:(0,s.jsx)(d.A,{className:"h-5 w-5 text-gray-400"})}),(0,s.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:e,onChange:e=>t(e.target.value),className:"block w-full pr-10 pl-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-right",placeholder:"أدخل بريدك الإلكتروني",dir:"rtl"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-2",children:"كلمة المرور"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:(0,s.jsx)(u.A,{className:"h-5 w-5 text-gray-400"})}),(0,s.jsx)("input",{id:"password",name:"password",type:x?"text":"password",autoComplete:"current-password",required:!0,value:r,onChange:e=>o(e.target.value),className:"block w-full pr-10 pl-10 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-right",placeholder:"أدخل كلمة المرور",dir:"rtl"}),(0,s.jsx)("button",{type:"button",className:"absolute inset-y-0 left-0 pl-3 flex items-center",onClick:()=>f(!x),children:x?(0,s.jsx)(h.A,{className:"h-5 w-5 text-gray-400 hover:text-gray-600"}):(0,s.jsx)(m.A,{className:"h-5 w-5 text-gray-400 hover:text-gray-600"})})]})]}),(0,s.jsx)("div",{className:"text-left",children:(0,s.jsx)(i(),{href:"/auth/forgot-password",className:"text-sm text-blue-600 hover:text-blue-500",children:"نسيت كلمة المرور؟"})}),(0,s.jsx)("button",{type:"submit",disabled:p,className:"w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:p?(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"}),"جاري تسجيل الدخول..."]}):"تسجيل الدخول"})]}),(0,s.jsx)("div",{className:"mt-6",children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,s.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,s.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,s.jsx)("span",{className:"px-2 bg-white text-gray-500",children:"أو"})})]})}),(0,s.jsxs)("div",{className:"mt-6 space-y-3",children:[(0,s.jsxs)("button",{onClick:A,disabled:p,className:"w-full flex justify-center items-center py-3 px-4 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:[(0,s.jsxs)("svg",{className:"w-5 h-5 ml-2",viewBox:"0 0 24 24",children:[(0,s.jsx)("path",{fill:"#4285F4",d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"}),(0,s.jsx)("path",{fill:"#34A853",d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"}),(0,s.jsx)("path",{fill:"#FBBC05",d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"}),(0,s.jsx)("path",{fill:"#EA4335",d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"})]}),"تسجيل الدخول بـ Google"]}),(0,s.jsxs)("button",{onClick:C,disabled:p,className:"w-full flex justify-center items-center py-3 px-4 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:[(0,s.jsx)("svg",{className:"w-5 h-5 ml-2",fill:"#1877F2",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{d:"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"})}),"تسجيل الدخول بـ Facebook"]})]}),(0,s.jsx)("div",{className:"mt-6 text-center",children:(0,s.jsxs)("p",{className:"text-sm text-gray-600",children:["ليس لديك حساب؟"," ",(0,s.jsx)(i(),{href:"/auth/register",className:"font-medium text-blue-600 hover:text-blue-500",children:"إنشاء حساب جديد"})]})})]})})})}},5695:(e,t,r)=>{"use strict";var s=r(8999);r.o(s,"usePathname")&&r.d(t,{usePathname:function(){return s.usePathname}}),r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}})},8513:(e,t,r)=>{Promise.resolve().then(r.bind(r,4177))},8749:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},8883:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[992,811,100,470,874,441,684,358],()=>t(8513)),_N_E=e.O()}]);