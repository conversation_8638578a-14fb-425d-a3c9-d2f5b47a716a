(()=>{var e={};e.id=139,e.ids=[139],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13861:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},13943:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("rotate-ccw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},25210:(e,t,s)=>{Promise.resolve().then(s.bind(s,75056))},25541:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28559:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},28947:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31158:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},35071:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},37366:e=>{"use strict";e.exports=require("dns")},40228:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},48730:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},55511:e=>{"use strict";e.exports=require("crypto")},57321:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\templete\\\\pdf-exam-generator\\\\src\\\\app\\\\results\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\templete\\pdf-exam-generator\\src\\app\\results\\page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75056:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>$});var r=s(60687),a=s(43210),l=s(16189);let c=(0,s(62688).A)("trophy",[["path",{d:"M10 14.66v1.626a2 2 0 0 1-.976 1.696A5 5 0 0 0 7 21.978",key:"1n3hpd"}],["path",{d:"M14 14.66v1.626a2 2 0 0 0 .976 1.696A5 5 0 0 1 17 21.978",key:"rfe1zi"}],["path",{d:"M18 9h1.5a1 1 0 0 0 0-5H18",key:"7xy6bh"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M6 9a6 6 0 0 0 12 0V3a1 1 0 0 0-1-1H7a1 1 0 0 0-1 1z",key:"1mhfuq"}],["path",{d:"M6 9H4.5a1 1 0 0 1 0-5H6",key:"tex48p"}]]);var n=s(40228),i=s(28947),d=s(48730),o=s(5336),m=s(35071);function x({score:e,total:t,percentage:s,completedAt:a,config:l}){let x=e=>e>=90?"text-green-600":e>=80?"text-blue-600":e>=70?"text-yellow-600":e>=60?"text-orange-600":"text-red-600";return(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,r.jsx)("div",{className:"lg:col-span-2",children:(0,r.jsx)("div",{className:`bg-white rounded-lg shadow-sm border-2 p-8 ${s>=90?"bg-green-50 border-green-200":s>=80?"bg-blue-50 border-blue-200":s>=70?"bg-yellow-50 border-yellow-200":s>=60?"bg-orange-50 border-orange-200":"bg-red-50 border-red-200"}`,children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"flex items-center justify-center mb-6",children:(0,r.jsx)(c,{className:`w-16 h-16 ${x(s)}`})}),(0,r.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Exam Complete!"}),(0,r.jsx)("p",{className:"text-gray-600 mb-8",children:"Here's how you performed on your exam"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:`text-6xl font-bold ${x(s)} mb-2`,children:s>=90?"A":s>=80?"B":s>=70?"C":s>=60?"D":"F"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Grade"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("div",{className:`text-6xl font-bold ${x(s)} mb-2`,children:[s,"%"]}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Score"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("div",{className:"text-6xl font-bold text-gray-900 mb-2",children:[e,"/",t]}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Correct"})]})]}),(0,r.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-4 mb-6",children:(0,r.jsx)("div",{className:`h-4 rounded-full transition-all duration-1000 ${s>=90?"bg-green-500":s>=80?"bg-blue-500":s>=70?"bg-yellow-500":s>=60?"bg-orange-500":"bg-red-500"}`,style:{width:`${s}%`}})}),(0,r.jsxs)("div",{className:"text-center",children:[s>=90&&(0,r.jsx)("p",{className:"text-green-700 font-medium",children:"Excellent work! Outstanding performance!"}),s>=80&&s<90&&(0,r.jsx)("p",{className:"text-blue-700 font-medium",children:"Great job! Very good performance!"}),s>=70&&s<80&&(0,r.jsx)("p",{className:"text-yellow-700 font-medium",children:"Good work! Room for improvement."}),s>=60&&s<70&&(0,r.jsx)("p",{className:"text-orange-700 font-medium",children:"Fair performance. Consider reviewing the material."}),s<60&&(0,r.jsx)("p",{className:"text-red-700 font-medium",children:"Needs improvement. Review the material and try again."})]})]})})}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Exam Details"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(n.A,{className:"w-5 h-5 text-gray-500"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"Completed"}),(0,r.jsx)("p",{className:"text-xs text-gray-600",children:new Date(a).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(i.A,{className:"w-5 h-5 text-gray-500"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"Difficulty"}),(0,r.jsx)("p",{className:"text-xs text-gray-600 capitalize",children:l?.difficulty||"Intermediate"})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(d.A,{className:"w-5 h-5 text-gray-500"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"Time Limit"}),(0,r.jsx)("p",{className:"text-xs text-gray-600",children:l?.hasTimeLimit?`${l.timeLimit} minutes`:"No limit"})]})]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Quick Stats"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(o.A,{className:"w-4 h-4 text-green-500"}),(0,r.jsx)("span",{className:"text-sm text-gray-700",children:"Correct Answers"})]}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-900",children:e})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(m.A,{className:"w-4 h-4 text-red-500"}),(0,r.jsx)("span",{className:"text-sm text-gray-700",children:"Incorrect Answers"})]}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-900",children:t-e})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(i.A,{className:"w-4 h-4 text-blue-500"}),(0,r.jsx)("span",{className:"text-sm text-gray-700",children:"Accuracy Rate"})]}),(0,r.jsxs)("span",{className:"text-sm font-medium text-gray-900",children:[s,"%"]})]})]})]}),s>=80&&(0,r.jsxs)("div",{className:"bg-gradient-to-r from-yellow-50 to-yellow-100 border border-yellow-200 rounded-lg p-6 text-center",children:[(0,r.jsx)(c,{className:"w-12 h-12 text-yellow-600 mx-auto mb-3"}),(0,r.jsx)("h4",{className:"text-lg font-semibold text-yellow-800 mb-2",children:"Achievement Unlocked!"}),(0,r.jsx)("p",{className:"text-sm text-yellow-700",children:s>=90?"Perfect Score Master":"High Achiever"})]})]})]})}var p=s(13861),h=s(93613);function u({questions:e,answers:t}){let s=(e,t)=>{if(void 0===t)return!1;if("multiple-choice"===e.type||"true-false"===e.type)return t===e.correctAnswer;if("short-answer"===e.type||"fill-blank"===e.type){let s=t.toLowerCase().trim(),r=e.correctAnswer.toLowerCase().trim();return s.includes(r)||r.includes(s)}return!1},a=(e,t)=>{if(void 0===t)return(0,r.jsx)("span",{className:"text-gray-500 italic",children:"No answer provided"});switch(e.type){case"multiple-choice":return e.options?.[t]||"Invalid answer";case"true-false":return t?"True":"False";default:return t}},l=e=>{switch(e.type){case"multiple-choice":return e.options?.[e.correctAnswer]||"Invalid answer";case"true-false":return e.correctAnswer?"True":"False";default:return e.correctAnswer}};return(0,r.jsx)("div",{className:"space-y-6",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 mb-6 flex items-center",children:[(0,r.jsx)(p.A,{className:"w-5 h-5 mr-2"}),"Question by Question Review"]}),(0,r.jsx)("div",{className:"space-y-8",children:e.map((e,c)=>{let n=t[e.id],i=s(e,n),d=void 0!==n;return(0,r.jsxs)("div",{className:"border border-gray-200 rounded-lg p-6",children:[(0,r.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("span",{className:"flex items-center justify-center w-8 h-8 bg-gray-100 rounded-full text-sm font-medium text-gray-700",children:c+1}),(0,r.jsx)("span",{className:"px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded-full capitalize",children:e.type.replace("-"," ")})]}),(0,r.jsx)("div",{className:"flex items-center space-x-2",children:d?i?(0,r.jsxs)("div",{className:"flex items-center space-x-1 text-green-600",children:[(0,r.jsx)(o.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{className:"text-sm font-medium",children:"Correct"})]}):(0,r.jsxs)("div",{className:"flex items-center space-x-1 text-red-600",children:[(0,r.jsx)(m.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{className:"text-sm font-medium",children:"Incorrect"})]}):(0,r.jsxs)("div",{className:"flex items-center space-x-1 text-gray-500",children:[(0,r.jsx)(h.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{className:"text-sm",children:"Not Answered"})]})})]}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-6 leading-relaxed",children:e.question}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,r.jsxs)("div",{className:`p-4 rounded-lg border-2 ${!d?"border-gray-200 bg-gray-50":i?"border-green-200 bg-green-50":"border-red-200 bg-red-50"}`,children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"Your Answer"}),(0,r.jsx)("p",{className:`text-sm ${!d?"text-gray-500":i?"text-green-800":"text-red-800"}`,children:a(e,n)})]}),(0,r.jsxs)("div",{className:"p-4 rounded-lg border-2 border-green-200 bg-green-50",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"Correct Answer"}),(0,r.jsx)("p",{className:"text-sm text-green-800 font-medium",children:l(e)})]})]}),"multiple-choice"===e.type&&e.options&&(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-3",children:"All Options"}),(0,r.jsx)("div",{className:"space-y-2",children:e.options.map((t,s)=>{let a=n===s,l=e.correctAnswer===s;return(0,r.jsx)("div",{className:`p-3 rounded-lg border ${l?"border-green-300 bg-green-50":a&&!l?"border-red-300 bg-red-50":"border-gray-200 bg-gray-50"}`,children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsxs)("span",{className:"text-sm font-medium text-gray-600",children:[String.fromCharCode(65+s),"."]}),(0,r.jsx)("span",{className:`text-sm ${l?"text-green-800 font-medium":a&&!l?"text-red-800":"text-gray-700"}`,children:t}),l&&(0,r.jsx)(o.A,{className:"w-4 h-4 text-green-600"}),a&&!l&&(0,r.jsx)(m.A,{className:"w-4 h-4 text-red-600"})]})},s)})})]}),(0,r.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-blue-900 mb-2",children:"Explanation"}),(0,r.jsx)("p",{className:"text-sm text-blue-800 leading-relaxed",children:e.explanation})]})]},e.id)})})]})})}var g=s(53411),y=s(25541);function b({questions:e,answers:t,scoreData:s}){let a=(()=>{let s={};return e.forEach(e=>{let r=e.type;s[r]||(s[r]={correct:0,total:0}),s[r].total++;let a=t[e.id];if(void 0!==a){let t=!1;if("multiple-choice"===e.type||"true-false"===e.type)t=a===e.correctAnswer;else if("short-answer"===e.type||"fill-blank"===e.type){let s=a.toLowerCase().trim(),r=e.correctAnswer.toLowerCase().trim();t=s.includes(r)||r.includes(s)}t&&s[r].correct++}}),s})(),l=[{topic:"Machine Learning Fundamentals",correct:4,total:5,percentage:80},{topic:"Neural Networks",correct:2,total:3,percentage:67},{topic:"Data Processing",correct:3,total:3,percentage:100},{topic:"Model Evaluation",correct:1,total:2,percentage:50}],c=e=>e.split("-").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" "),n=e=>e>=80?"text-green-600":e>=60?"text-yellow-600":"text-red-600",d=e=>e>=80?"bg-green-500":e>=60?"bg-yellow-500":"bg-red-500";return(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 mb-6 flex items-center",children:[(0,r.jsx)(g.A,{className:"w-5 h-5 mr-2"}),"Performance Analytics"]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[(0,r.jsxs)("div",{className:"text-center p-4 bg-blue-50 rounded-lg",children:[(0,r.jsxs)("div",{className:"text-3xl font-bold text-blue-600 mb-2",children:[s.percentage,"%"]}),(0,r.jsx)("p",{className:"text-sm text-blue-700",children:"Overall Score"})]}),(0,r.jsxs)("div",{className:"text-center p-4 bg-green-50 rounded-lg",children:[(0,r.jsx)("div",{className:"text-3xl font-bold text-green-600 mb-2",children:s.score}),(0,r.jsx)("p",{className:"text-sm text-green-700",children:"Correct Answers"})]}),(0,r.jsxs)("div",{className:"text-center p-4 bg-purple-50 rounded-lg",children:[(0,r.jsx)("div",{className:"text-3xl font-bold text-purple-600 mb-2",children:Object.keys(t).length}),(0,r.jsx)("p",{className:"text-sm text-purple-700",children:"Questions Attempted"})]}),(0,r.jsxs)("div",{className:"text-center p-4 bg-orange-50 rounded-lg",children:[(0,r.jsxs)("div",{className:"text-3xl font-bold text-orange-600 mb-2",children:[Math.round(Object.keys(t).length/e.length*100),"%"]}),(0,r.jsx)("p",{className:"text-sm text-orange-700",children:"Completion Rate"})]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-6 flex items-center",children:[(0,r.jsx)(i.A,{className:"w-5 h-5 mr-2"}),"Performance by Question Type"]}),(0,r.jsx)("div",{className:"space-y-4",children:Object.entries(a).map(([e,t])=>{let s=t.total>0?Math.round(t.correct/t.total*100):0;return(0,r.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900",children:c(e)}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("span",{className:"text-sm text-gray-600",children:[t.correct,"/",t.total," correct"]}),(0,r.jsxs)("span",{className:`text-sm font-medium ${n(s)}`,children:[s,"%"]})]})]}),(0,r.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,r.jsx)("div",{className:`h-2 rounded-full transition-all duration-500 ${d(s)}`,style:{width:`${s}%`}})})]},e)})})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-6 flex items-center",children:[(0,r.jsx)(y.A,{className:"w-5 h-5 mr-2"}),"Performance by Topic"]}),(0,r.jsx)("div",{className:"space-y-4",children:l.map((e,t)=>(0,r.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900",children:e.topic}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("span",{className:"text-sm text-gray-600",children:[e.correct,"/",e.total," correct"]}),(0,r.jsxs)("span",{className:`text-sm font-medium ${n(e.percentage)}`,children:[e.percentage,"%"]})]})]}),(0,r.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,r.jsx)("div",{className:`h-2 rounded-full transition-all duration-500 ${d(e.percentage)}`,style:{width:`${e.percentage}%`}})})]},t))})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-6",children:"Recommendations for Improvement"}),(0,r.jsxs)("div",{className:"space-y-4",children:[s.percentage<60&&(0,r.jsxs)("div",{className:"p-4 bg-red-50 border border-red-200 rounded-lg",children:[(0,r.jsx)("h4",{className:"font-medium text-red-900 mb-2",children:"Focus on Fundamentals"}),(0,r.jsx)("p",{className:"text-sm text-red-800",children:"Your overall score suggests you should review the basic concepts covered in this material. Consider going through the source material again before retaking the exam."})]}),Object.entries(a).map(([e,t])=>{let s=t.total>0?Math.round(t.correct/t.total*100):0;return s<70?(0,r.jsxs)("div",{className:"p-4 bg-yellow-50 border border-yellow-200 rounded-lg",children:[(0,r.jsxs)("h4",{className:"font-medium text-yellow-900 mb-2",children:["Improve ",c(e)," Skills"]}),(0,r.jsxs)("p",{className:"text-sm text-yellow-800",children:["You scored ",s,"% on ",c(e)," questions. Practice more questions of this type to improve your performance."]})]},e):null}),l.filter(e=>e.percentage<70).map((e,t)=>(0,r.jsxs)("div",{className:"p-4 bg-blue-50 border border-blue-200 rounded-lg",children:[(0,r.jsxs)("h4",{className:"font-medium text-blue-900 mb-2",children:["Study ",e.topic]}),(0,r.jsxs)("p",{className:"text-sm text-blue-800",children:["You scored ",e.percentage,"% on ",e.topic," questions. Review this topic area to strengthen your understanding."]})]},t)),s.percentage>=80&&(0,r.jsxs)("div",{className:"p-4 bg-green-50 border border-green-200 rounded-lg",children:[(0,r.jsx)("h4",{className:"font-medium text-green-900 mb-2",children:"Excellent Performance!"}),(0,r.jsx)("p",{className:"text-sm text-green-800",children:"You demonstrated strong understanding across most topics. Keep up the great work and consider advancing to more challenging material."})]})]})]})]})}var j=s(31158),f=s(81620),N=s(41550),v=s(10022);function w({examResults:e,scoreData:t}){let s=async()=>{let e={title:"My Exam Results",text:`I scored ${t.percentage}% (${t.score}/${t.total}) on my exam!`,url:window.location.href};if(navigator.share)try{await navigator.share(e)}catch(t){navigator.clipboard.writeText(`${e.text} ${e.url}`),alert("Results copied to clipboard!")}else navigator.clipboard.writeText(`${e.text} ${e.url}`),alert("Results copied to clipboard!")};return(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-6",children:"Export & Share Results"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,r.jsxs)("button",{onClick:()=>{let s=new Blob([`
EXAM RESULTS REPORT
==================

Score: ${t.score}/${t.total} (${t.percentage}%)
Completed: ${new Date(e.completedAt).toLocaleDateString()}
Difficulty: ${e.config?.difficulty||"Intermediate"}

QUESTION BREAKDOWN:
${e.questions.map((t,s)=>{let r=e.answers[t.id],a=r===t.correctAnswer;return`
${s+1}. ${t.question}
   Your Answer: ${void 0!==r?r:"No answer"}
   Correct Answer: ${t.correctAnswer}
   Result: ${a?"Correct":"Incorrect"}
`}).join("")}
    `],{type:"text/plain"}),r=URL.createObjectURL(s),a=document.createElement("a");a.href=r,a.download=`exam-results-${new Date().toISOString().split("T")[0]}.txt`,document.body.appendChild(a),a.click(),document.body.removeChild(a),URL.revokeObjectURL(r)},className:"flex flex-col items-center space-y-2 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,r.jsx)(j.A,{className:"w-6 h-6 text-blue-600"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-900",children:"Download Report"}),(0,r.jsx)("span",{className:"text-xs text-gray-600",children:"Export as text file"})]}),(0,r.jsxs)("button",{onClick:s,className:"flex flex-col items-center space-y-2 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,r.jsx)(f.A,{className:"w-6 h-6 text-green-600"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-900",children:"Share Results"}),(0,r.jsx)("span",{className:"text-xs text-gray-600",children:"Share your score"})]}),(0,r.jsxs)("button",{onClick:()=>{let s=encodeURIComponent("My Exam Results"),r=encodeURIComponent(`
I completed an exam and scored ${t.percentage}% (${t.score}/${t.total}).

Exam Details:
- Completed: ${new Date(e.completedAt).toLocaleDateString()}
- Difficulty: ${e.config?.difficulty||"Intermediate"}
- Questions: ${t.total}
- Correct Answers: ${t.score}

This exam was generated using the PDF to Interactive Exam Generator.
    `);window.open(`mailto:?subject=${s}&body=${r}`)},className:"flex flex-col items-center space-y-2 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,r.jsx)(N.A,{className:"w-6 h-6 text-purple-600"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-900",children:"Email Results"}),(0,r.jsx)("span",{className:"text-xs text-gray-600",children:"Send via email"})]}),(0,r.jsxs)("button",{onClick:()=>{window.print()},className:"flex flex-col items-center space-y-2 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,r.jsx)(v.A,{className:"w-6 h-6 text-orange-600"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-900",children:"Print Results"}),(0,r.jsx)("span",{className:"text-xs text-gray-600",children:"Print this page"})]})]}),(0,r.jsxs)("div",{className:"mt-6 p-4 bg-gray-50 rounded-lg",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-900 mb-2",children:"Quick Summary"}),(0,r.jsxs)("p",{className:"text-sm text-gray-700",children:["Exam completed on ",new Date(e.completedAt).toLocaleDateString()," • Score: ",t.percentage,"% (",t.score,"/",t.total,") • Difficulty: ",e.config?.difficulty||"Intermediate"]})]})]})}var A=s(28559),k=s(13943);function $(){let e=(0,l.useRouter)(),[t,s]=(0,a.useState)(null),[c,n]=(0,a.useState)("overview");if(!t)return(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Loading results..."})]})});let i=(()=>{if(!t)return{score:0,total:0,percentage:0};let e=0,s=t.questions.length;return t.questions.forEach(s=>{let r=t.answers[s.id];if(void 0!==r){if("multiple-choice"===s.type||"true-false"===s.type)r===s.correctAnswer&&e++;else if("short-answer"===s.type||"fill-blank"===s.type){let t=r.toLowerCase().trim(),a=s.correctAnswer.toLowerCase().trim();(t.includes(a)||a.includes(t))&&e++}}}),{score:e,total:s,percentage:Math.round(e/s*100)}})();return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)("div",{className:"bg-white border-b border-gray-200",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("button",{onClick:()=>e.push("/"),className:"flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors",children:[(0,r.jsx)(A.A,{className:"w-5 h-5"}),(0,r.jsx)("span",{children:"Back to Home"})]}),(0,r.jsx)("div",{className:"h-6 w-px bg-gray-300"}),(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Exam Results"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsxs)("button",{onClick:()=>{localStorage.removeItem("examResults"),e.push("/exam")},className:"flex items-center space-x-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,r.jsx)(k.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"Retake Exam"})]}),(0,r.jsx)("button",{onClick:()=>{localStorage.removeItem("examResults"),localStorage.removeItem("examAnswers"),localStorage.removeItem("uploadedPDF"),localStorage.removeItem("examConfig"),e.push("/")},className:"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:(0,r.jsx)("span",{children:"New Exam"})})]})]}),(0,r.jsx)("div",{className:"mt-6 border-b border-gray-200",children:(0,r.jsxs)("nav",{className:"-mb-px flex space-x-8",children:[(0,r.jsx)("button",{onClick:()=>n("overview"),className:`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${"overview"===c?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:"Overview"}),(0,r.jsx)("button",{onClick:()=>n("review"),className:`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${"review"===c?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:"Question Review"}),(0,r.jsx)("button",{onClick:()=>n("analytics"),className:`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${"analytics"===c?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:"Analytics"})]})})]})}),(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:["overview"===c&&(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsx)(x,{score:i.score,total:i.total,percentage:i.percentage,completedAt:t.completedAt,config:t.config}),(0,r.jsx)(w,{examResults:t,scoreData:i})]}),"review"===c&&(0,r.jsx)(u,{questions:t.questions,answers:t.answers}),"analytics"===c&&(0,r.jsx)(b,{questions:t.questions,answers:t.answers,scoreData:i})]})]})}},78354:(e,t,s)=>{Promise.resolve().then(s.bind(s,57321))},79350:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>c.a,__next_app__:()=>m,pages:()=>o,routeModule:()=>x,tree:()=>d});var r=s(65239),a=s(48088),l=s(88170),c=s.n(l),n=s(30893),i={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>n[e]);s.d(t,i);let d={children:["",{children:["results",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,57321)),"D:\\templete\\pdf-exam-generator\\src\\app\\results\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:"/manifest.webmanifest"}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"D:\\templete\\pdf-exam-generator\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:"/manifest.webmanifest"}}]}.children,o=["D:\\templete\\pdf-exam-generator\\src\\app\\results\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/results/page",pathname:"/results",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},79551:e=>{"use strict";e.exports=require("url")},81620:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("share-2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]])},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},93613:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,248,658,647],()=>s(79350));module.exports=r})();