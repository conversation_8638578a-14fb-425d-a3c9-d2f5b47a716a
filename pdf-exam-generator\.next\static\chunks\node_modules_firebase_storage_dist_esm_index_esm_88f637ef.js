(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/firebase/storage/dist/esm/index.esm.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_firebase_storage_dist_esm_index_esm_f78343cb.js",
  "static/chunks/node_modules_firebase_storage_dist_esm_index_esm_48aacd03.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/firebase/storage/dist/esm/index.esm.js [app-client] (ecmascript)");
    });
});
}}),
}]);