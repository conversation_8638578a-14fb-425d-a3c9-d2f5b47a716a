module.exports = {

"[project]/node_modules/firebase/storage/dist/index.mjs [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_firebase_storage_dist_index_mjs_371c6035._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/firebase/storage/dist/index.mjs [app-ssr] (ecmascript)");
    });
});
}}),

};