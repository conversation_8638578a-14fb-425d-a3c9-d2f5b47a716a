'use client';

import { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import { User, Mail, Calendar, Award, BookOpen, Clock, Edit2, Save, X } from 'lucide-react';

export default function ProfilePage() {
  const { userProfile, updateUserProfile, user } = useAuth();
  const [isEditing, setIsEditing] = useState(false);
  const [editForm, setEditForm] = useState({
    displayName: userProfile?.displayName || '',
    email: userProfile?.email || '',
    role: userProfile?.role || 'student'
  });

  const handleSave = async () => {
    try {
      await updateUserProfile({
        displayName: editForm.displayName,
        role: editForm.role as 'teacher' | 'student' | 'admin'
      });
      setIsEditing(false);
    } catch (error) {
      console.error('Error updating profile:', error);
    }
  };

  const formatDate = (date: Date | undefined) => {
    if (!date) return 'غير محدد';
    return new Date(date).toLocaleDateString('ar-SA');
  };

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white border-b border-gray-200">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <h1 className="text-2xl font-bold text-gray-900 flex items-center" dir="rtl">
              <User className="w-6 h-6 ml-3" />
              الملف الشخصي
            </h1>
            <p className="text-gray-600 mt-1" dir="rtl">عرض وتحديث معلوماتك الشخصية</p>
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="space-y-8">
            {/* Profile Information */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-lg font-semibold text-gray-900" dir="rtl">المعلومات الأساسية</h2>
                <button
                  onClick={() => setIsEditing(!isEditing)}
                  className="flex items-center px-3 py-2 text-sm font-medium text-blue-600 hover:text-blue-700"
                  dir="rtl"
                >
                  {isEditing ? (
                    <>
                      <X className="w-4 h-4 ml-1" />
                      إلغاء
                    </>
                  ) : (
                    <>
                      <Edit2 className="w-4 h-4 ml-1" />
                      تعديل
                    </>
                  )}
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Profile Picture */}
                <div className="col-span-full flex items-center space-x-6 space-x-reverse">
                  <div className="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center">
                    {userProfile?.photoURL ? (
                      <img
                        src={userProfile.photoURL}
                        alt="Profile"
                        className="w-20 h-20 rounded-full object-cover"
                      />
                    ) : (
                      <User className="w-10 h-10 text-blue-600" />
                    )}
                  </div>
                  <div>
                    <h3 className="text-lg font-medium text-gray-900" dir="rtl">
                      {userProfile?.displayName || 'مستخدم جديد'}
                    </h3>
                    <p className="text-sm text-gray-500" dir="rtl">
                      {userProfile?.role === 'teacher' ? 'معلم' : 
                       userProfile?.role === 'student' ? 'طالب' : 'مدير'}
                    </p>
                  </div>
                </div>

                {/* Name */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2" dir="rtl">
                    الاسم الكامل
                  </label>
                  {isEditing ? (
                    <input
                      type="text"
                      value={editForm.displayName}
                      onChange={(e) => setEditForm(prev => ({ ...prev, displayName: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      dir="rtl"
                    />
                  ) : (
                    <p className="text-gray-900" dir="rtl">{userProfile?.displayName || 'غير محدد'}</p>
                  )}
                </div>

                {/* Email */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2" dir="rtl">
                    البريد الإلكتروني
                  </label>
                  <p className="text-gray-900 flex items-center" dir="rtl">
                    <Mail className="w-4 h-4 ml-2" />
                    {userProfile?.email}
                  </p>
                </div>

                {/* Role */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2" dir="rtl">
                    الدور
                  </label>
                  {isEditing ? (
                    <select
                      value={editForm.role}
                      onChange={(e) => setEditForm(prev => ({ ...prev, role: e.target.value as 'teacher' | 'student' | 'admin' }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      dir="rtl"
                    >
                      <option value="student">طالب</option>
                      <option value="teacher">معلم</option>
                    </select>
                  ) : (
                    <p className="text-gray-900" dir="rtl">
                      {userProfile?.role === 'teacher' ? 'معلم' : 
                       userProfile?.role === 'student' ? 'طالب' : 'مدير'}
                    </p>
                  )}
                </div>

                {/* Join Date */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2" dir="rtl">
                    تاريخ الانضمام
                  </label>
                  <p className="text-gray-900 flex items-center" dir="rtl">
                    <Calendar className="w-4 h-4 ml-2" />
                    {formatDate(userProfile?.createdAt)}
                  </p>
                </div>
              </div>

              {isEditing && (
                <div className="mt-6 flex justify-end">
                  <button
                    onClick={handleSave}
                    className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                    dir="rtl"
                  >
                    <Save className="w-4 h-4 ml-2" />
                    حفظ التغييرات
                  </button>
                </div>
              )}
            </div>

            {/* Statistics */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-6" dir="rtl">الإحصائيات</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div className="text-center">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <BookOpen className="w-6 h-6 text-blue-600" />
                  </div>
                  <p className="text-2xl font-bold text-gray-900">{userProfile?.stats?.totalExams || 0}</p>
                  <p className="text-sm text-gray-500" dir="rtl">إجمالي الامتحانات</p>
                </div>

                <div className="text-center">
                  <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <Award className="w-6 h-6 text-green-600" />
                  </div>
                  <p className="text-2xl font-bold text-gray-900">{userProfile?.stats?.averageScore || 0}%</p>
                  <p className="text-sm text-gray-500" dir="rtl">متوسط النتائج</p>
                </div>

                <div className="text-center">
                  <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <BookOpen className="w-6 h-6 text-purple-600" />
                  </div>
                  <p className="text-2xl font-bold text-gray-900">{userProfile?.stats?.totalQuestions || 0}</p>
                  <p className="text-sm text-gray-500" dir="rtl">إجمالي الأسئلة</p>
                </div>

                <div className="text-center">
                  <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <Clock className="w-6 h-6 text-orange-600" />
                  </div>
                  <p className="text-2xl font-bold text-gray-900">{Math.round((userProfile?.stats?.studyTime || 0) / 60)}</p>
                  <p className="text-sm text-gray-500" dir="rtl">ساعات الدراسة</p>
                </div>
              </div>
            </div>

            {/* Account Security */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-6" dir="rtl">أمان الحساب</h2>
              
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div dir="rtl">
                    <h3 className="font-medium text-gray-900">كلمة المرور</h3>
                    <p className="text-sm text-gray-500">آخر تحديث: {formatDate(userProfile?.lastLoginAt)}</p>
                  </div>
                  <button className="px-4 py-2 text-sm font-medium text-blue-600 hover:text-blue-700">
                    تغيير كلمة المرور
                  </button>
                </div>

                <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div dir="rtl">
                    <h3 className="font-medium text-gray-900">المصادقة الثنائية</h3>
                    <p className="text-sm text-gray-500">حماية إضافية لحسابك</p>
                  </div>
                  <button className="px-4 py-2 text-sm font-medium text-blue-600 hover:text-blue-700">
                    تفعيل
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
