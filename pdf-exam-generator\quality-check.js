#!/usr/bin/env node

/**
 * Quality Check Script for PDF Exam Generator
 * يتحقق من جودة الكود وإعدادات المشروع
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 فحص جودة المشروع - PDF Exam Generator\n');

const checks = [];

// 1. فحص ملفات الإعداد الأساسية
function checkEssentialFiles() {
  const essentialFiles = [
    'package.json',
    'next.config.ts',
    'tailwind.config.ts',
    'tsconfig.json',
    '.env.example',
    'README.md',
    'FIREBASE_SETUP.md'
  ];

  const missing = essentialFiles.filter(file => !fs.existsSync(file));
  
  if (missing.length === 0) {
    checks.push({ name: 'الملفات الأساسية', status: '✅', message: 'جميع الملفات موجودة' });
  } else {
    checks.push({ name: 'الملفات الأساسية', status: '❌', message: `ملفات مفقودة: ${missing.join(', ')}` });
  }
}

// 2. فحص بنية المجلدات
function checkFolderStructure() {
  const requiredFolders = [
    'src/app',
    'src/components',
    'src/contexts',
    'src/lib',
    'src/app/auth',
    'src/components/auth',
    'src/components/ui'
  ];

  const missing = requiredFolders.filter(folder => !fs.existsSync(folder));
  
  if (missing.length === 0) {
    checks.push({ name: 'بنية المجلدات', status: '✅', message: 'البنية صحيحة' });
  } else {
    checks.push({ name: 'بنية المجلدات', status: '❌', message: `مجلدات مفقودة: ${missing.join(', ')}` });
  }
}

// 3. فحص ملفات Firebase
function checkFirebaseFiles() {
  const firebaseFiles = [
    'src/lib/firebase.ts',
    'src/contexts/AuthContext.tsx'
  ];

  const missing = firebaseFiles.filter(file => !fs.existsSync(file));
  
  if (missing.length === 0) {
    checks.push({ name: 'ملفات Firebase', status: '✅', message: 'ملفات Firebase موجودة' });
  } else {
    checks.push({ name: 'ملفات Firebase', status: '❌', message: `ملفات مفقودة: ${missing.join(', ')}` });
  }
}

// 4. فحص إعدادات package.json
function checkPackageJson() {
  try {
    const pkg = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const requiredDeps = ['firebase', 'next', 'react', 'tailwindcss'];
    const missing = requiredDeps.filter(dep => !pkg.dependencies[dep] && !pkg.devDependencies[dep]);
    
    if (missing.length === 0) {
      checks.push({ name: 'التبعيات المطلوبة', status: '✅', message: 'جميع التبعيات مثبتة' });
    } else {
      checks.push({ name: 'التبعيات المطلوبة', status: '❌', message: `تبعيات مفقودة: ${missing.join(', ')}` });
    }
  } catch (error) {
    checks.push({ name: 'package.json', status: '❌', message: 'خطأ في قراءة package.json' });
  }
}

// 5. فحص ملف .env.local
function checkEnvFile() {
  if (fs.existsSync('.env.local')) {
    const envContent = fs.readFileSync('.env.local', 'utf8');
    const requiredVars = [
      'NEXT_PUBLIC_FIREBASE_API_KEY',
      'NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN',
      'NEXT_PUBLIC_FIREBASE_PROJECT_ID'
    ];
    
    const missing = requiredVars.filter(varName => !envContent.includes(varName));
    
    if (missing.length === 0) {
      checks.push({ name: 'متغيرات البيئة', status: '✅', message: 'متغيرات Firebase موجودة' });
    } else {
      checks.push({ name: 'متغيرات البيئة', status: '⚠️', message: `متغيرات مفقودة: ${missing.join(', ')}` });
    }
  } else {
    checks.push({ name: 'ملف .env.local', status: '⚠️', message: 'ملف .env.local غير موجود (انسخ من .env.example)' });
  }
}

// 6. فحص TypeScript
function checkTypeScript() {
  try {
    const tsconfig = JSON.parse(fs.readFileSync('tsconfig.json', 'utf8'));
    if (tsconfig.compilerOptions && tsconfig.compilerOptions.strict) {
      checks.push({ name: 'إعدادات TypeScript', status: '✅', message: 'إعدادات TypeScript صحيحة' });
    } else {
      checks.push({ name: 'إعدادات TypeScript', status: '⚠️', message: 'يُنصح بتفعيل strict mode' });
    }
  } catch (error) {
    checks.push({ name: 'TypeScript', status: '❌', message: 'خطأ في قراءة tsconfig.json' });
  }
}

// تشغيل جميع الفحوصات
function runAllChecks() {
  checkEssentialFiles();
  checkFolderStructure();
  checkFirebaseFiles();
  checkPackageJson();
  checkEnvFile();
  checkTypeScript();
}

// عرض النتائج
function displayResults() {
  console.log('📊 نتائج الفحص:\n');
  
  checks.forEach(check => {
    console.log(`${check.status} ${check.name}: ${check.message}`);
  });
  
  const passed = checks.filter(c => c.status === '✅').length;
  const warnings = checks.filter(c => c.status === '⚠️').length;
  const failed = checks.filter(c => c.status === '❌').length;
  
  console.log('\n📈 الملخص:');
  console.log(`✅ نجح: ${passed}`);
  console.log(`⚠️ تحذيرات: ${warnings}`);
  console.log(`❌ فشل: ${failed}`);
  
  if (failed === 0 && warnings === 0) {
    console.log('\n🎉 ممتاز! المشروع جاهز للاستخدام');
  } else if (failed === 0) {
    console.log('\n👍 المشروع يعمل، لكن هناك بعض التحذيرات');
  } else {
    console.log('\n🔧 يحتاج المشروع إلى إصلاحات قبل الاستخدام');
  }
}

// تشغيل الفحص
runAllChecks();
displayResults();
