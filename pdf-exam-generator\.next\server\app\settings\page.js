(()=>{var e={};e.id=662,e.ids=[662],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8819:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},9776:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(60687);function a({size:e="md",color:t="blue",text:r,fullScreen:a=!1}){let i=(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center",children:[(0,s.jsx)("div",{className:`animate-spin rounded-full border-2 border-gray-200 ${{blue:"border-blue-600",green:"border-green-600",gray:"border-gray-600"}[t]} border-t-transparent ${{sm:"h-4 w-4",md:"h-8 w-8",lg:"h-12 w-12"}[e]}`}),r&&(0,s.jsx)("p",{className:"mt-2 text-sm text-gray-600",children:r})]});return a?(0,s.jsx)("div",{className:"fixed inset-0 bg-white bg-opacity-75 flex items-center justify-center z-50",children:i}):i}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11437:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34570:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var s=r(60687);r(43210);var a=r(16189),i=r(7613),n=r(9776);function l({children:e,requiredRole:t,redirectTo:r="/auth/login"}){let{user:l,userProfile:o,loading:d,isAuthenticated:c}=(0,i.A)();return((0,a.useRouter)(),d||!c||t&&o&&o.role!==t)?(0,s.jsx)(n.A,{}):(0,s.jsx)(s.Fragment,{children:e})}},34631:e=>{"use strict";e.exports=require("tls")},37366:e=>{"use strict";e.exports=require("dns")},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},74198:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\templete\\\\pdf-exam-generator\\\\src\\\\app\\\\settings\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\templete\\pdf-exam-generator\\src\\app\\settings\\page.tsx","default")},74992:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>x});var s=r(60687),a=r(43210),i=r(7613),n=r(34570),l=r(84027),o=r(58869),d=r(97051),c=r(99891),u=r(11437),m=r(8819);function x(){let{userProfile:e,updateUserProfile:t}=(0,i.A)(),[r,x]=(0,a.useState)({language:"ar",theme:"light",defaultDifficulty:"intermediate",defaultQuestionCount:20,emailNotifications:!0,examReminders:!0,resultNotifications:!1,dataSharing:!1,analytics:!0,apiKey:"",aiProvider:"openai"}),[p,h]=(0,a.useState)(!1),g=async()=>{try{await t({preferences:{language:r.language,theme:r.theme,notifications:r.emailNotifications}}),localStorage.setItem("userSettings",JSON.stringify(r)),h(!0),setTimeout(()=>h(!1),2e3)}catch(e){console.error("Error saving settings:",e)}},b=(e,t)=>{x(r=>({...r,[e]:t}))};return(0,s.jsx)(n.A,{children:(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,s.jsx)("div",{className:"bg-white border-b border-gray-200",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:[(0,s.jsxs)("h1",{className:"text-2xl font-bold text-gray-900 flex items-center",dir:"rtl",children:[(0,s.jsx)(l.A,{className:"w-6 h-6 ml-3"}),"الإعدادات"]}),(0,s.jsx)("p",{className:"text-gray-600 mt-1",dir:"rtl",children:"إدارة تفضيلاتك وإعدادات حسابك"})]})}),(0,s.jsx)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,s.jsxs)("div",{className:"space-y-8",children:[(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,s.jsxs)("h2",{className:"text-lg font-semibold text-gray-900 mb-6 flex items-center",dir:"rtl",children:[(0,s.jsx)(o.A,{className:"w-5 h-5 ml-2"}),"تفضيلات المستخدم"]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",dir:"rtl",children:"اللغة الافتراضية"}),(0,s.jsxs)("select",{value:r.language,onChange:e=>b("language",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",dir:"rtl",children:[(0,s.jsx)("option",{value:"en",children:"English"}),(0,s.jsx)("option",{value:"ar",children:"العربية"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",dir:"rtl",children:"المظهر"}),(0,s.jsxs)("select",{value:r.theme,onChange:e=>b("theme",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",dir:"rtl",children:[(0,s.jsx)("option",{value:"light",children:"فاتح"}),(0,s.jsx)("option",{value:"dark",children:"داكن"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Default Difficulty"}),(0,s.jsxs)("select",{value:r.defaultDifficulty,onChange:e=>b("defaultDifficulty",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[(0,s.jsx)("option",{value:"beginner",children:"Beginner"}),(0,s.jsx)("option",{value:"intermediate",children:"Intermediate"}),(0,s.jsx)("option",{value:"advanced",children:"Advanced"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Default Question Count"}),(0,s.jsx)("input",{type:"number",min:"5",max:"50",value:r.defaultQuestionCount,onChange:e=>b("defaultQuestionCount",parseInt(e.target.value)),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]})]})]}),(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,s.jsxs)("h2",{className:"text-lg font-semibold text-gray-900 mb-6 flex items-center",children:[(0,s.jsx)(d.A,{className:"w-5 h-5 mr-2"}),"Notifications"]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("label",{className:"flex items-center",children:[(0,s.jsx)("input",{type:"checkbox",checked:r.emailNotifications,onChange:e=>b("emailNotifications",e.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,s.jsx)("span",{className:"ml-3 text-sm text-gray-700",children:"Email notifications"})]}),(0,s.jsxs)("label",{className:"flex items-center",children:[(0,s.jsx)("input",{type:"checkbox",checked:r.examReminders,onChange:e=>b("examReminders",e.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,s.jsx)("span",{className:"ml-3 text-sm text-gray-700",children:"Exam reminders"})]}),(0,s.jsxs)("label",{className:"flex items-center",children:[(0,s.jsx)("input",{type:"checkbox",checked:r.resultNotifications,onChange:e=>b("resultNotifications",e.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,s.jsx)("span",{className:"ml-3 text-sm text-gray-700",children:"Result notifications"})]})]})]}),(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,s.jsxs)("h2",{className:"text-lg font-semibold text-gray-900 mb-6 flex items-center",children:[(0,s.jsx)(c.A,{className:"w-5 h-5 mr-2"}),"Privacy & Security"]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("label",{className:"flex items-center",children:[(0,s.jsx)("input",{type:"checkbox",checked:r.dataSharing,onChange:e=>b("dataSharing",e.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,s.jsx)("span",{className:"ml-3 text-sm text-gray-700",children:"Allow data sharing for service improvement"})]}),(0,s.jsxs)("label",{className:"flex items-center",children:[(0,s.jsx)("input",{type:"checkbox",checked:r.analytics,onChange:e=>b("analytics",e.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,s.jsx)("span",{className:"ml-3 text-sm text-gray-700",children:"Enable analytics to help improve the platform"})]})]})]}),(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,s.jsxs)("h2",{className:"text-lg font-semibold text-gray-900 mb-6 flex items-center",children:[(0,s.jsx)(u.A,{className:"w-5 h-5 mr-2"}),"AI Integration"]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"AI Provider"}),(0,s.jsxs)("select",{value:r.aiProvider,onChange:e=>b("aiProvider",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[(0,s.jsx)("option",{value:"openai",children:"OpenAI"}),(0,s.jsx)("option",{value:"anthropic",children:"Anthropic"}),(0,s.jsx)("option",{value:"google",children:"Google AI"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"API Key (Optional)"}),(0,s.jsx)("input",{type:"password",value:r.apiKey,onChange:e=>b("apiKey",e.target.value),placeholder:"Enter your API key for enhanced features",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Providing your own API key enables unlimited usage and advanced features"})]})]})]}),(0,s.jsx)("div",{className:"flex justify-end",children:(0,s.jsxs)("button",{onClick:g,className:`flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-all duration-200 ${p?"bg-green-600 text-white":"bg-blue-600 text-white hover:bg-blue-700"}`,children:[(0,s.jsx)(m.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:p?"Saved!":"Save Settings"})]})})]})})]})})}},75982:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var s=r(65239),a=r(48088),i=r(88170),n=r.n(i),l=r(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(t,o);let d={children:["",{children:["settings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,74198)),"D:\\templete\\pdf-exam-generator\\src\\app\\settings\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"D:\\templete\\pdf-exam-generator\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["D:\\templete\\pdf-exam-generator\\src\\app\\settings\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/settings/page",pathname:"/settings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},81697:(e,t,r)=>{Promise.resolve().then(r.bind(r,74198))},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},94849:(e,t,r)=>{Promise.resolve().then(r.bind(r,74992))}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,248,658,647],()=>r(75982));module.exports=s})();