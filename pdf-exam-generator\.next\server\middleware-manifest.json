{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "c0D9AsNoX58HZ7jc1-cok", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "lN871D3TYRwyOoXk+rHj6q/jH4+pp6KsuaMUQ83O+bw=", "__NEXT_PREVIEW_MODE_ID": "f76a87446c60889762412ed44b3ebe38", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "e5f6732264f93cf40c9d5dfaa4f449ae039680911297fc8f1797f2d190078af5", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "5401998f028b9906f9f80ddc3369383548f104d65ca4b2fe4d8d506959e563e7"}}}, "functions": {}, "sortedMiddleware": ["/"]}