rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can read and write their own profile
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Exams - teachers can create, students can read assigned exams
    match /exams/{examId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && 
        resource.data.createdBy == request.auth.uid;
    }
    
    // Exam results - users can read/write their own results
    match /examResults/{resultId} {
      allow read, write: if request.auth != null && 
        resource.data.userId == request.auth.uid;
    }
    
    // PDF files metadata
    match /pdfFiles/{fileId} {
      allow read, write: if request.auth != null && 
        resource.data.uploadedBy == request.auth.uid;
    }
  }
}
