'use client';

import { useAuth } from '@/contexts/AuthContext';
import { usePathname } from 'next/navigation';
import { Navbar } from './Navbar';
import { Footer } from './Footer';
import { DashboardLayout } from './DashboardLayout';

export function AppContent({ children }: { children: React.ReactNode }) {
  const { isAuthenticated, loading } = useAuth();
  const pathname = usePathname();

  // Show loading spinner while checking authentication
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">جاري التحميل...</p>
        </div>
      </div>
    );
  }

  // Check if it's an auth route
  const isAuthRoute = pathname.startsWith('/auth/');

  // For auth routes, show without navbar/footer
  if (isAuthRoute) {
    return <>{children}</>;
  }

  // If not authenticated and not auth route, show with navbar for landing page
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex flex-col">
        <Navbar />
        <main className="flex-1">
          {children}
        </main>
        <Footer />
      </div>
    );
  }

  // If authenticated, check if it's a dashboard route
  const isDashboardRoute = pathname.startsWith('/dashboard') || 
                          pathname === '/analyze' || 
                          pathname === '/exam' || 
                          pathname === '/results' || 
                          pathname === '/settings';

  if (isDashboardRoute) {
    return (
      <DashboardLayout>
        {children}
      </DashboardLayout>
    );
  }

  // For other authenticated routes, use regular layout
  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      <main className="flex-1">
        {children}
      </main>
      <Footer />
    </div>
  );
}
