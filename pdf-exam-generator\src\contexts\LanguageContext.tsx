'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';

type Language = 'ar' | 'en';
type Theme = 'light' | 'dark';

interface LanguageContextType {
  language: Language;
  theme: Theme;
  setLanguage: (lang: Language) => void;
  setTheme: (theme: Theme) => void;
  t: (key: string) => string;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

const translations = {
  ar: {
    // Navigation
    'nav.features': 'المميزات',
    'nav.pricing': 'الأسعار',
    'nav.about': 'حولنا',
    'nav.contact': 'اتصل بنا',
    'nav.login': 'تسجيل الدخول',
    'nav.signup': 'إنشاء حساب',
    
    // Hero Section
    'hero.title': 'حول ملفات PDF إلى اختبارات تفاعلية',
    'hero.subtitle': 'في دقائق معدودة',
    'hero.description': 'استخدم الذكاء الاصطناعي لتحويل أي ملف PDF إلى اختبار تفاعلي مع تحليلات متقدمة وتقييم تلقائي. مثالي للمعلمين والطلاب ومنصات التعلم الإلكتروني.',
    'hero.cta.primary': 'ابدأ مجاناً',
    'hero.cta.secondary': 'شاهد العرض التوضيحي',
    'hero.trusted': 'يثق به أكثر من 10,000 معلم حول العالم',
    
    // Features
    'features.title': 'مميزات قوية لتعليم أفضل',
    'features.subtitle': 'كل ما تحتاجه لإنشاء اختبارات احترافية وتحليل النتائج',
    
    'feature.ai.title': 'ذكاء اصطناعي متقدم',
    'feature.ai.description': 'يحلل محتوى PDF ويولد أسئلة متنوعة تلقائياً',
    
    'feature.interactive.title': 'اختبارات تفاعلية',
    'feature.interactive.description': 'أسئلة متعددة الخيارات، صح/خطأ، وأسئلة مفتوحة',
    
    'feature.analytics.title': 'تحليلات شاملة',
    'feature.analytics.description': 'تقارير مفصلة عن أداء الطلاب ونقاط القوة والضعف',
    
    'feature.realtime.title': 'نتائج فورية',
    'feature.realtime.description': 'تصحيح تلقائي وتقييم فوري مع ملاحظات مفصلة',
    
    'feature.multilang.title': 'دعم متعدد اللغات',
    'feature.multilang.description': 'يدعم العربية والإنجليزية مع واجهة سهلة الاستخدام',
    
    'feature.secure.title': 'آمن وموثوق',
    'feature.secure.description': 'حماية عالية للبيانات مع نسخ احتياطية تلقائية',
    
    // Pricing
    'pricing.title': 'خطط تناسب احتياجاتك',
    'pricing.subtitle': 'ابدأ مجاناً وارتقِ حسب نموك',
    'pricing.monthly': 'شهري',
    'pricing.yearly': 'سنوي',
    'pricing.save': 'وفر 20%',
    'pricing.popular': 'الأكثر شعبية',
    'pricing.questions': 'لديك أسئلة؟',
    'pricing.contact_text': 'فريقنا هنا لمساعدتك في اختيار الخطة المناسبة',
    'pricing.contact_us': 'تواصل معنا',
    
    'plan.free.name': 'مجاني',
    'plan.free.price': '0',
    'plan.free.period': '/شهر',
    'plan.free.description': 'مثالي للمعلمين المبتدئين',
    
    'plan.pro.name': 'احترافي',
    'plan.pro.price': '19',
    'plan.pro.period': '/شهر',
    'plan.pro.description': 'للمعلمين النشطين',
    
    'plan.enterprise.name': 'مؤسسات',
    'plan.enterprise.price': '99',
    'plan.enterprise.period': '/شهر',
    'plan.enterprise.description': 'للمدارس والجامعات',
    
    'pricing.cta': 'ابدأ الآن',
    'pricing.contact': 'تواصل معنا',
    
    // Footer
    'footer.product': 'المنتج',
    'footer.company': 'الشركة',
    'footer.support': 'الدعم',
    'footer.legal': 'قانوني',
    'footer.rights': 'جميع الحقوق محفوظة',
    
    // Common
    'common.loading': 'جاري التحميل...',
    'common.error': 'حدث خطأ',
    'common.success': 'تم بنجاح',
    'common.cancel': 'إلغاء',
    'common.save': 'حفظ',
    'common.edit': 'تعديل',
    'common.delete': 'حذف',
    'common.view': 'عرض',

    // Authentication
    'auth.login': 'تسجيل الدخول',
    'auth.register': 'إنشاء حساب',
    'auth.email': 'البريد الإلكتروني',
    'auth.password': 'كلمة المرور',
    'auth.confirmPassword': 'تأكيد كلمة المرور',
    'auth.fullName': 'الاسم الكامل',
    'auth.role': 'الدور',
    'auth.teacher': 'معلم',
    'auth.student': 'طالب',
    'auth.admin': 'مدير',
    'auth.forgotPassword': 'نسيت كلمة المرور؟',
    'auth.rememberMe': 'تذكرني',
    'auth.signInWith': 'تسجيل الدخول باستخدام',
    'auth.google': 'جوجل',
    'auth.facebook': 'فيسبوك',
    'auth.alreadyHaveAccount': 'لديك حساب بالفعل؟',
    'auth.dontHaveAccount': 'ليس لديك حساب؟',
    'auth.createAccount': 'إنشاء حساب',
    'auth.signIn': 'تسجيل الدخول',
    'auth.logout': 'تسجيل الخروج',

    // Dashboard
    'dashboard.title': 'لوحة التحكم',
    'dashboard.welcome': 'مرحباً',
    'dashboard.overview': 'إليك نظرة عامة على نشاطك وإنجازاتك الأخيرة',
    'dashboard.totalExams': 'إجمالي الاختبارات',
    'dashboard.averageScore': 'متوسط النتائج',
    'dashboard.totalQuestions': 'إجمالي الأسئلة',
    'dashboard.studyHours': 'ساعات الدراسة',
    'dashboard.recentExams': 'الاختبارات الأخيرة',
    'dashboard.quickActions': 'إجراءات سريعة',
    'dashboard.createExam': 'إنشاء اختبار جديد',
    'dashboard.viewAnalytics': 'عرض التحليلات',
    'dashboard.manageStudents': 'إدارة الطلاب',
    'dashboard.newAchievement': 'إنجاز جديد!',
    'dashboard.congratulations': 'تهانينا! لقد حققت إنجازاً رائعاً في رحلتك التعليمية',

    // Navigation
    'nav.dashboard': 'لوحة التحكم',
    'nav.upload': 'رفع ملف جديد',
    'nav.exams': 'الاختبارات',
    'nav.analytics': 'التحليلات',
    'nav.history': 'السجل',
    'nav.students': 'الطلاب',
    'nav.profile': 'الملف الشخصي',
    'nav.settings': 'الإعدادات',
  },
  en: {
    // Navigation
    'nav.features': 'Features',
    'nav.pricing': 'Pricing',
    'nav.about': 'About',
    'nav.contact': 'Contact',
    'nav.login': 'Login',
    'nav.signup': 'Sign Up',
    
    // Hero Section
    'hero.title': 'Transform PDFs into Interactive Exams',
    'hero.subtitle': 'in Minutes',
    'hero.description': 'Use AI to convert any PDF into interactive exams with advanced analytics and automatic grading. Perfect for teachers, students, and e-learning platforms.',
    'hero.cta.primary': 'Get Started Free',
    'hero.cta.secondary': 'Watch Demo',
    'hero.trusted': 'Trusted by 10,000+ educators worldwide',
    
    // Features
    'features.title': 'Powerful Features for Better Learning',
    'features.subtitle': 'Everything you need to create professional exams and analyze results',
    
    'feature.ai.title': 'Advanced AI',
    'feature.ai.description': 'Analyzes PDF content and generates diverse questions automatically',
    
    'feature.interactive.title': 'Interactive Exams',
    'feature.interactive.description': 'Multiple choice, true/false, and open-ended questions',
    
    'feature.analytics.title': 'Comprehensive Analytics',
    'feature.analytics.description': 'Detailed reports on student performance and learning gaps',
    
    'feature.realtime.title': 'Real-time Results',
    'feature.realtime.description': 'Automatic grading and instant feedback with detailed explanations',
    
    'feature.multilang.title': 'Multi-language Support',
    'feature.multilang.description': 'Supports Arabic and English with intuitive interface',
    
    'feature.secure.title': 'Secure & Reliable',
    'feature.secure.description': 'Enterprise-grade security with automatic backups',
    
    // Pricing
    'pricing.title': 'Plans That Fit Your Needs',
    'pricing.subtitle': 'Start free and scale as you grow',
    'pricing.monthly': 'Monthly',
    'pricing.yearly': 'Yearly',
    'pricing.save': 'Save 20%',
    'pricing.popular': 'Most Popular',
    'pricing.questions': 'Have Questions?',
    'pricing.contact_text': 'Our team is here to help you choose the right plan',
    'pricing.contact_us': 'Contact Us',
    
    'plan.free.name': 'Free',
    'plan.free.price': '0',
    'plan.free.period': '/month',
    'plan.free.description': 'Perfect for getting started',
    
    'plan.pro.name': 'Professional',
    'plan.pro.price': '19',
    'plan.pro.period': '/month',
    'plan.pro.description': 'For active educators',
    
    'plan.enterprise.name': 'Enterprise',
    'plan.enterprise.price': '99',
    'plan.enterprise.period': '/month',
    'plan.enterprise.description': 'For schools and universities',
    
    'pricing.cta': 'Get Started',
    'pricing.contact': 'Contact Sales',
    
    // Footer
    'footer.product': 'Product',
    'footer.company': 'Company',
    'footer.support': 'Support',
    'footer.legal': 'Legal',
    'footer.rights': 'All rights reserved',
    
    // Common
    'common.loading': 'Loading...',
    'common.error': 'An error occurred',
    'common.success': 'Success',
    'common.cancel': 'Cancel',
    'common.save': 'Save',
    'common.edit': 'Edit',
    'common.delete': 'Delete',
    'common.view': 'View',

    // Authentication
    'auth.login': 'Login',
    'auth.register': 'Sign Up',
    'auth.email': 'Email',
    'auth.password': 'Password',
    'auth.confirmPassword': 'Confirm Password',
    'auth.fullName': 'Full Name',
    'auth.role': 'Role',
    'auth.teacher': 'Teacher',
    'auth.student': 'Student',
    'auth.admin': 'Admin',
    'auth.forgotPassword': 'Forgot Password?',
    'auth.rememberMe': 'Remember Me',
    'auth.signInWith': 'Sign in with',
    'auth.google': 'Google',
    'auth.facebook': 'Facebook',
    'auth.alreadyHaveAccount': 'Already have an account?',
    'auth.dontHaveAccount': "Don't have an account?",
    'auth.createAccount': 'Create Account',
    'auth.signIn': 'Sign In',
    'auth.logout': 'Logout',

    // Dashboard
    'dashboard.title': 'Dashboard',
    'dashboard.welcome': 'Welcome',
    'dashboard.overview': "Here's an overview of your recent activity and achievements",
    'dashboard.totalExams': 'Total Exams',
    'dashboard.averageScore': 'Average Score',
    'dashboard.totalQuestions': 'Total Questions',
    'dashboard.studyHours': 'Study Hours',
    'dashboard.recentExams': 'Recent Exams',
    'dashboard.quickActions': 'Quick Actions',
    'dashboard.createExam': 'Create New Exam',
    'dashboard.viewAnalytics': 'View Analytics',
    'dashboard.manageStudents': 'Manage Students',
    'dashboard.newAchievement': 'New Achievement!',
    'dashboard.congratulations': 'Congratulations! You have achieved a great milestone in your learning journey',

    // Navigation
    'nav.dashboard': 'Dashboard',
    'nav.upload': 'Upload New File',
    'nav.exams': 'Exams',
    'nav.analytics': 'Analytics',
    'nav.history': 'History',
    'nav.students': 'Students',
    'nav.profile': 'Profile',
    'nav.settings': 'Settings',
  }
};

export function LanguageProvider({ children }: { children: React.ReactNode }) {
  const [language, setLanguage] = useState<Language>('en');
  const [theme, setTheme] = useState<Theme>('light');

  useEffect(() => {
    // Load saved preferences
    const savedLanguage = localStorage.getItem('language') as Language;
    const savedTheme = localStorage.getItem('theme') as Theme;
    
    if (savedLanguage) setLanguage(savedLanguage);
    if (savedTheme) setTheme(savedTheme);
    
    // Apply theme to document
    document.documentElement.classList.toggle('dark', savedTheme === 'dark');
    document.documentElement.setAttribute('dir', savedLanguage === 'ar' ? 'rtl' : 'ltr');
  }, []);

  useEffect(() => {
    // Save preferences and apply changes
    localStorage.setItem('language', language);
    localStorage.setItem('theme', theme);
    
    document.documentElement.classList.toggle('dark', theme === 'dark');
    document.documentElement.setAttribute('dir', language === 'ar' ? 'rtl' : 'ltr');
  }, [language, theme]);

  const t = (key: string): string => {
    return translations[language][key as keyof typeof translations[typeof language]] || key;
  };

  return (
    <LanguageContext.Provider value={{
      language,
      theme,
      setLanguage,
      setTheme,
      t
    }}>
      {children}
    </LanguageContext.Provider>
  );
}

export function useLanguage() {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
}
