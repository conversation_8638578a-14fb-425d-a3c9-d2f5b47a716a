(()=>{var e={};e.id=922,e.ids=[922],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12640:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("trending-down",[["path",{d:"M16 17h6v-6",key:"t6n2it"}],["path",{d:"m22 17-8.5-8.5-5 5L2 7",key:"x473p"}]])},16226:(e,t,s)=>{Promise.resolve().then(s.bind(s,34976))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},23026:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("user-plus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},25541:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28559:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31158:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},34976:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>j});var r=s(60687),a=s(43210),i=s(7613),l=s(28559),n=s(31158),d=s(23026),c=s(41312),o=s(25541),x=s(12640),m=s(86561),p=s(99270),h=s(41550),u=s(48340),g=s(81904),v=s(85814),y=s.n(v);function j(){let{user:e}=(0,i.A)(),[t,s]=(0,a.useState)(""),[v,j]=(0,a.useState)("all"),f=[{id:1,name:"سارة أحمد",email:"<EMAIL>",phone:"+966501234567",joinDate:"2024-01-15",totalExams:12,averageScore:88,lastActivity:"2024-01-20",status:"active",trend:"up",change:"+5%",avatar:null},{id:2,name:"محمد علي",email:"<EMAIL>",phone:"+966507654321",joinDate:"2024-01-10",totalExams:18,averageScore:92,lastActivity:"2024-01-21",status:"active",trend:"up",change:"+8%",avatar:null},{id:3,name:"فاطمة محمد",email:"<EMAIL>",phone:"+966509876543",joinDate:"2024-01-08",totalExams:15,averageScore:76,lastActivity:"2024-01-19",status:"inactive",trend:"down",change:"-2%",avatar:null},{id:4,name:"عبدالله سعد",email:"<EMAIL>",phone:"+966502468135",joinDate:"2024-01-12",totalExams:20,averageScore:95,lastActivity:"2024-01-21",status:"active",trend:"up",change:"+12%",avatar:null},{id:5,name:"نورا خالد",email:"<EMAIL>",phone:"+966508642097",joinDate:"2024-01-05",totalExams:22,averageScore:84,lastActivity:"2024-01-20",status:"active",trend:"up",change:"+3%",avatar:null}],b=f.filter(e=>{let s=e.name.toLowerCase().includes(t.toLowerCase())||e.email.toLowerCase().includes(t.toLowerCase()),r="all"===v||e.status===v;return s&&r}),w=e=>{switch(e){case"active":return"bg-green-100 text-green-800";case"inactive":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},N=e=>{switch(e){case"active":return"نشط";case"inactive":return"غير نشط";default:return"غير محدد"}},A={total:f.length,active:f.filter(e=>"active"===e.status).length,inactive:f.filter(e=>"inactive"===e.status).length,averageScore:Math.round(f.reduce((e,t)=>e+t.averageScore,0)/f.length)};return(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("div",{className:"flex items-center",children:(0,r.jsxs)(y(),{href:"/dashboard",className:"flex items-center text-gray-600 hover:text-gray-900 transition-colors ml-4",children:[(0,r.jsx)(l.A,{className:"w-5 h-5 ml-2"}),"العودة للوحة التحكم"]})}),(0,r.jsxs)("div",{className:"flex items-center space-x-4 rtl:space-x-reverse",children:[(0,r.jsxs)("button",{className:"flex items-center px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,r.jsx)(n.A,{className:"w-4 h-4 ml-2"}),"تصدير"]}),(0,r.jsxs)("button",{className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,r.jsx)(d.A,{className:"w-4 h-4 ml-2"}),"إضافة طالب"]})]})]}),(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"إدارة الطلاب"}),(0,r.jsx)("p",{className:"text-gray-600",children:"تتبع وإدارة الطلاب ومراقبة تقدمهم في الاختبارات"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center ml-4",children:(0,r.jsx)(c.A,{className:"w-6 h-6 text-blue-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"إجمالي الطلاب"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:A.total})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center ml-4",children:(0,r.jsx)(o.A,{className:"w-6 h-6 text-green-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"الطلاب النشطون"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:A.active})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center ml-4",children:(0,r.jsx)(x.A,{className:"w-6 h-6 text-red-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"غير النشطين"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:A.inactive})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center ml-4",children:(0,r.jsx)(m.A,{className:"w-6 h-6 text-yellow-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"متوسط النتائج"}),(0,r.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[A.averageScore,"%"]})]})]})})]}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6",children:(0,r.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between gap-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4 rtl:space-x-reverse",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(p.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),(0,r.jsx)("input",{type:"text",placeholder:"البحث عن طالب...",value:t,onChange:e=>s(e.target.value),className:"w-64 pl-4 pr-10 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]}),(0,r.jsxs)("select",{value:v,onChange:e=>j(e.target.value),className:"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[(0,r.jsx)("option",{value:"all",children:"جميع الطلاب"}),(0,r.jsx)("option",{value:"active",children:"النشطون"}),(0,r.jsx)("option",{value:"inactive",children:"غير النشطين"})]})]}),(0,r.jsxs)("div",{className:"text-sm text-gray-600",children:["عرض ",b.length," من ",f.length," طالب"]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden",children:(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"الطالب"}),(0,r.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"معلومات الاتصال"}),(0,r.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"الأداء"}),(0,r.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"الحالة"}),(0,r.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"آخر نشاط"}),(0,r.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"إجراءات"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:b.map(e=>(0,r.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center ml-4",children:(0,r.jsx)("span",{className:"text-white font-medium text-sm",children:e.name.split(" ").map(e=>e[0]).join("")})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:["انضم في ",new Date(e.joinDate).toLocaleDateString("ar-SA")]})]})]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"text-sm text-gray-900",children:[(0,r.jsxs)("div",{className:"flex items-center mb-1",children:[(0,r.jsx)(h.A,{className:"w-4 h-4 text-gray-400 ml-2"}),e.email]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(u.A,{className:"w-4 h-4 text-gray-400 ml-2"}),e.phone]})]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"text-sm text-gray-900",children:[(0,r.jsxs)("div",{className:"flex items-center mb-1",children:[(0,r.jsxs)("span",{className:"font-medium",children:[e.averageScore,"%"]}),"up"===e.trend?(0,r.jsx)(o.A,{className:"w-4 h-4 text-green-500 mr-1"}):(0,r.jsx)(x.A,{className:"w-4 h-4 text-red-500 mr-1"}),(0,r.jsx)("span",{className:`text-xs ${"up"===e.trend?"text-green-600":"text-red-600"}`,children:e.change})]}),(0,r.jsxs)("div",{className:"text-xs text-gray-500",children:[e.totalExams," اختبار"]})]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${w(e.status)}`,children:N(e.status)})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(e.lastActivity).toLocaleDateString("ar-SA")}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-left text-sm font-medium",children:(0,r.jsx)("button",{className:"text-gray-400 hover:text-gray-600",children:(0,r.jsx)(g.A,{className:"w-5 h-5"})})})]},e.id))})]})})})]})}},37366:e=>{"use strict";e.exports=require("dns")},48340:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]])},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68826:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>c});var r=s(65239),a=s(48088),i=s(88170),l=s.n(i),n=s(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);s.d(t,d);let c={children:["",{children:["dashboard",{children:["students",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,97966)),"D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\students\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"D:\\templete\\pdf-exam-generator\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\students\\page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/students/page",pathname:"/dashboard/students",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},81904:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("ellipsis-vertical",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"12",cy:"5",r:"1",key:"gxeob9"}],["circle",{cx:"12",cy:"19",r:"1",key:"lyex9k"}]])},81946:(e,t,s)=>{Promise.resolve().then(s.bind(s,97966))},86561:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},97966:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\templete\\\\pdf-exam-generator\\\\src\\\\app\\\\dashboard\\\\students\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\students\\page.tsx","default")}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,248,658,647],()=>s(68826));module.exports=r})();