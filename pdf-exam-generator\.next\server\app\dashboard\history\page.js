(()=>{var e={};e.id=610,e.ids=[610],e.modules={1672:(e,t,s)=>{Promise.resolve().then(s.bind(s,90540))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13861:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},13943:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("rotate-ccw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28559:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31158:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},35071:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},35130:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>b});var r=s(60687),a=s(43210),l=s(7613),i=s(5336),c=s(35071),d=s(93613),n=s(48730),o=s(28559),x=s(31158),m=s(10022),p=s(53411),u=s(99270),h=s(40228),g=s(13861),y=s(13943),f=s(85814),j=s.n(f);function b(){let{user:e}=(0,l.A)(),[t,s]=(0,a.useState)(""),[f,b]=(0,a.useState)("all"),[v,w]=(0,a.useState)("all"),N=[{id:1,title:"اختبار الرياضيات - الجبر المتقدم",subject:"رياضيات",date:"2024-01-20",duration:45,questions:20,score:85,status:"completed",timeSpent:42,attempts:1,difficulty:"متوسط"},{id:2,title:"اختبار الفيزياء - قوانين الحركة",subject:"فيزياء",date:"2024-01-19",duration:60,questions:25,score:92,status:"completed",timeSpent:55,attempts:1,difficulty:"صعب"},{id:3,title:"اختبار الكيمياء - الجدول الدوري",subject:"كيمياء",date:"2024-01-18",duration:30,questions:15,score:78,status:"completed",timeSpent:28,attempts:2,difficulty:"سهل"},{id:4,title:"اختبار الأحياء - الخلية والوراثة",subject:"أحياء",date:"2024-01-17",duration:50,questions:22,score:88,status:"completed",timeSpent:47,attempts:1,difficulty:"متوسط"},{id:5,title:"اختبار التاريخ - الحضارة الإسلامية",subject:"تاريخ",date:"2024-01-16",duration:40,questions:18,score:76,status:"completed",timeSpent:38,attempts:1,difficulty:"متوسط"},{id:6,title:"اختبار اللغة العربية - النحو والصرف",subject:"لغة عربية",date:"2024-01-15",duration:35,questions:16,score:0,status:"incomplete",timeSpent:12,attempts:1,difficulty:"سهل"}],k=N.filter(e=>{let s=e.title.toLowerCase().includes(t.toLowerCase())||e.subject.toLowerCase().includes(t.toLowerCase()),r="all"===f||e.status===f,a=!0;if("all"!==v){let t=new Date(e.date),s=Math.floor((new Date().getTime()-t.getTime())/864e5);switch(v){case"week":a=s<=7;break;case"month":a=s<=30;break;case"quarter":a=s<=90}}return s&&r&&a}),A=e=>{switch(e){case"completed":return(0,r.jsx)(i.A,{className:"w-5 h-5 text-green-500"});case"incomplete":return(0,r.jsx)(c.A,{className:"w-5 h-5 text-red-500"});case"in_progress":return(0,r.jsx)(d.A,{className:"w-5 h-5 text-yellow-500"});default:return(0,r.jsx)(n.A,{className:"w-5 h-5 text-gray-500"})}},q=e=>{switch(e){case"completed":return"مكتمل";case"incomplete":return"غير مكتمل";case"in_progress":return"قيد التنفيذ";default:return"غير محدد"}},M=e=>e>=90?"text-green-600 bg-green-100":e>=80?"text-blue-600 bg-blue-100":e>=70?"text-yellow-600 bg-yellow-100":e>0?"text-red-600 bg-red-100":"text-gray-600 bg-gray-100",P=e=>{switch(e){case"سهل":return"text-green-600 bg-green-100";case"متوسط":return"text-yellow-600 bg-yellow-100";case"صعب":return"text-red-600 bg-red-100";default:return"text-gray-600 bg-gray-100"}},_={total:N.length,completed:N.filter(e=>"completed"===e.status).length,incomplete:N.filter(e=>"incomplete"===e.status).length,averageScore:Math.round(N.filter(e=>"completed"===e.status).reduce((e,t)=>e+t.score,0)/N.filter(e=>"completed"===e.status).length)};return(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("div",{className:"flex items-center",children:(0,r.jsxs)(j(),{href:"/dashboard",className:"flex items-center text-gray-600 hover:text-gray-900 transition-colors ml-4",children:[(0,r.jsx)(o.A,{className:"w-5 h-5 ml-2"}),"العودة للوحة التحكم"]})}),(0,r.jsx)("div",{className:"flex items-center space-x-4 rtl:space-x-reverse",children:(0,r.jsxs)("button",{className:"flex items-center px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,r.jsx)(x.A,{className:"w-4 h-4 ml-2"}),"تصدير السجل"]})})]}),(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"سجل الاختبارات"}),(0,r.jsx)("p",{className:"text-gray-600",children:"تتبع جميع الاختبارات التي قمت بها مع تفاصيل الأداء والنتائج"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center ml-4",children:(0,r.jsx)(m.A,{className:"w-6 h-6 text-blue-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"إجمالي الاختبارات"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:_.total})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center ml-4",children:(0,r.jsx)(i.A,{className:"w-6 h-6 text-green-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"مكتملة"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:_.completed})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center ml-4",children:(0,r.jsx)(c.A,{className:"w-6 h-6 text-red-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"غير مكتملة"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:_.incomplete})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center ml-4",children:(0,r.jsx)(p.A,{className:"w-6 h-6 text-yellow-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"متوسط النتائج"}),(0,r.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[_.averageScore,"%"]})]})]})})]}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6",children:(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row items-start sm:items-center space-y-4 sm:space-y-0 sm:space-x-4 rtl:space-x-reverse",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(u.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),(0,r.jsx)("input",{type:"text",placeholder:"البحث في الاختبارات...",value:t,onChange:e=>s(e.target.value),className:"w-64 pl-4 pr-10 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]}),(0,r.jsxs)("select",{value:f,onChange:e=>b(e.target.value),className:"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[(0,r.jsx)("option",{value:"all",children:"جميع الحالات"}),(0,r.jsx)("option",{value:"completed",children:"مكتملة"}),(0,r.jsx)("option",{value:"incomplete",children:"غير مكتملة"})]}),(0,r.jsxs)("select",{value:v,onChange:e=>w(e.target.value),className:"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[(0,r.jsx)("option",{value:"all",children:"جميع الفترات"}),(0,r.jsx)("option",{value:"week",children:"آخر أسبوع"}),(0,r.jsx)("option",{value:"month",children:"آخر شهر"}),(0,r.jsx)("option",{value:"quarter",children:"آخر 3 أشهر"})]})]}),(0,r.jsxs)("div",{className:"text-sm text-gray-600",children:["عرض ",k.length," من ",N.length," اختبار"]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden",children:(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"الاختبار"}),(0,r.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"التاريخ والوقت"}),(0,r.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"النتيجة"}),(0,r.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"الحالة"}),(0,r.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"إجراءات"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:k.map(e=>(0,r.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,r.jsx)("td",{className:"px-6 py-4",children:(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)("div",{className:"w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center ml-4 flex-shrink-0",children:(0,r.jsx)(m.A,{className:"w-5 h-5 text-blue-600"})}),(0,r.jsxs)("div",{className:"min-w-0 flex-1",children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900 truncate",children:e.title}),(0,r.jsxs)("div",{className:"flex items-center space-x-4 rtl:space-x-reverse text-xs text-gray-500 mt-1",children:[(0,r.jsx)("span",{children:e.subject}),(0,r.jsx)("span",{children:"•"}),(0,r.jsxs)("span",{children:[e.questions," سؤال"]}),(0,r.jsx)("span",{children:"•"}),(0,r.jsx)("span",{className:`px-2 py-1 rounded-full ${P(e.difficulty)}`,children:e.difficulty})]})]})]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"text-sm text-gray-900",children:[(0,r.jsxs)("div",{className:"flex items-center mb-1",children:[(0,r.jsx)(h.A,{className:"w-4 h-4 text-gray-400 ml-2"}),new Date(e.date).toLocaleDateString("ar-SA")]}),(0,r.jsxs)("div",{className:"flex items-center text-xs text-gray-500",children:[(0,r.jsx)(n.A,{className:"w-3 h-3 text-gray-400 ml-1"}),e.timeSpent," من ",e.duration," دقيقة"]})]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:"completed"===e.status?(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("div",{className:`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${M(e.score)}`,children:[e.score,"%"]}),e.attempts>1&&(0,r.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:["المحاولة ",e.attempts]})]}):(0,r.jsx)("span",{className:"text-sm text-gray-500",children:"غير مكتمل"})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"flex items-center",children:[A(e.status),(0,r.jsx)("span",{className:"mr-2 text-sm text-gray-900",children:q(e.status)})]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-left text-sm font-medium",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2 rtl:space-x-reverse",children:[(0,r.jsx)("button",{className:"text-blue-600 hover:text-blue-900 p-1 rounded",children:(0,r.jsx)(g.A,{className:"w-4 h-4"})}),"incomplete"===e.status&&(0,r.jsx)("button",{className:"text-green-600 hover:text-green-900 p-1 rounded",children:(0,r.jsx)(y.A,{className:"w-4 h-4"})}),(0,r.jsx)("button",{className:"text-gray-600 hover:text-gray-900 p-1 rounded",children:(0,r.jsx)(x.A,{className:"w-4 h-4"})})]})})]},e.id))})]})})})]})}},37366:e=>{"use strict";e.exports=require("dns")},40228:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},48730:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},49896:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>n});var r=s(65239),a=s(48088),l=s(88170),i=s.n(l),c=s(30893),d={};for(let e in c)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>c[e]);s.d(t,d);let n={children:["",{children:["dashboard",{children:["history",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,90540)),"D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\history\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:"/manifest.webmanifest"}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"D:\\templete\\pdf-exam-generator\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:"/manifest.webmanifest"}}]}.children,o=["D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\history\\page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/history/page",pathname:"/dashboard/history",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:n}})},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},88520:(e,t,s)=>{Promise.resolve().then(s.bind(s,35130))},90540:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\templete\\\\pdf-exam-generator\\\\src\\\\app\\\\dashboard\\\\history\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\history\\page.tsx","default")},91645:e=>{"use strict";e.exports=require("net")},93613:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,248,658,647],()=>s(49896));module.exports=r})();