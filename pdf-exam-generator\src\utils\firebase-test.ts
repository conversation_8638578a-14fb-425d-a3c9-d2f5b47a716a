/**
 * Firebase Connection Test Utility
 * أداة اختبار الاتصال بـ Firebase
 */

import { auth, db, storage } from '@/lib/firebase';
import { connectAuthEmulator, signInAnonymously } from 'firebase/auth';
import { connectFirestoreEmulator, doc, setDoc, getDoc } from 'firebase/firestore';
import { connectStorageEmulator } from 'firebase/storage';

interface TestResult {
  service: string;
  status: 'success' | 'error' | 'warning';
  message: string;
  details?: any;
}

class FirebaseTestSuite {
  private results: TestResult[] = [];
  private isEmulator = false;

  constructor() {
    // Check if running in development with emulator
    this.isEmulator = process.env.NODE_ENV === 'development' && 
                     process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID === 'demo-project';
  }

  private addResult(service: string, status: 'success' | 'error' | 'warning', message: string, details?: any) {
    this.results.push({ service, status, message, details });
  }

  /**
   * Test Firebase Authentication
   */
  async testAuth(): Promise<void> {
    try {
      // Connect to emulator if in development
      if (this.isEmulator && !auth.config.emulator) {
        connectAuthEmulator(auth, 'http://localhost:9099');
      }

      // Test anonymous sign in
      const userCredential = await signInAnonymously(auth);
      
      if (userCredential.user) {
        this.addResult('Authentication', 'success', 'تم الاتصال بـ Firebase Auth بنجاح', {
          uid: userCredential.user.uid,
          isAnonymous: userCredential.user.isAnonymous
        });
        
        // Sign out after test
        await auth.signOut();
      } else {
        this.addResult('Authentication', 'error', 'فشل في إنشاء مستخدم مجهول');
      }
    } catch (error: any) {
      this.addResult('Authentication', 'error', `خطأ في Firebase Auth: ${error.message}`, error);
    }
  }

  /**
   * Test Firestore Database
   */
  async testFirestore(): Promise<void> {
    try {
      // Connect to emulator if in development
      if (this.isEmulator && !db._delegate._databaseId.projectId.includes('localhost')) {
        connectFirestoreEmulator(db, 'localhost', 8080);
      }

      // Test document write
      const testDocRef = doc(db, 'test', 'connection-test');
      const testData = {
        timestamp: new Date(),
        message: 'Firebase connection test',
        success: true
      };

      await setDoc(testDocRef, testData);
      
      // Test document read
      const docSnap = await getDoc(testDocRef);
      
      if (docSnap.exists()) {
        this.addResult('Firestore', 'success', 'تم الاتصال بـ Firestore بنجاح', {
          documentId: docSnap.id,
          data: docSnap.data()
        });
      } else {
        this.addResult('Firestore', 'error', 'فشل في قراءة المستند من Firestore');
      }
    } catch (error: any) {
      this.addResult('Firestore', 'error', `خطأ في Firestore: ${error.message}`, error);
    }
  }

  /**
   * Test Firebase Storage
   */
  async testStorage(): Promise<void> {
    try {
      // Connect to emulator if in development
      if (this.isEmulator && !storage.app.options.storageBucket?.includes('localhost')) {
        connectStorageEmulator(storage, 'localhost', 9199);
      }

      // Test storage reference creation
      const { ref } = await import('firebase/storage');
      const testRef = ref(storage, 'test/connection-test.txt');
      
      if (testRef) {
        this.addResult('Storage', 'success', 'تم الاتصال بـ Firebase Storage بنجاح', {
          bucket: storage.app.options.storageBucket,
          path: testRef.fullPath
        });
      } else {
        this.addResult('Storage', 'error', 'فشل في إنشاء مرجع التخزين');
      }
    } catch (error: any) {
      this.addResult('Storage', 'error', `خطأ في Firebase Storage: ${error.message}`, error);
    }
  }

  /**
   * Test Firebase Configuration
   */
  testConfig(): void {
    const config = {
      apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
      authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
      projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
      storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
      messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
      appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID
    };

    const missingVars = Object.entries(config)
      .filter(([key, value]) => !value || value.includes('your_') || value.includes('demo-'))
      .map(([key]) => key);

    if (missingVars.length === 0) {
      this.addResult('Configuration', 'success', 'جميع متغيرات Firebase مُعرَّفة', config);
    } else if (config.projectId === 'demo-project') {
      this.addResult('Configuration', 'warning', 'تستخدم إعدادات تجريبية - قم بتحديثها للإنتاج', {
        missingVars,
        isDemo: true
      });
    } else {
      this.addResult('Configuration', 'error', 'متغيرات Firebase مفقودة أو غير صحيحة', {
        missingVars
      });
    }
  }

  /**
   * Run all tests
   */
  async runAllTests(): Promise<TestResult[]> {
    console.log('🔥 بدء اختبار Firebase...\n');

    // Test configuration first
    this.testConfig();

    // Only run service tests if config is valid
    const configResult = this.results.find(r => r.service === 'Configuration');
    if (configResult && configResult.status !== 'error') {
      await this.testAuth();
      await this.testFirestore();
      await this.testStorage();
    }

    return this.results;
  }

  /**
   * Display test results
   */
  displayResults(): void {
    console.log('📊 نتائج اختبار Firebase:\n');

    this.results.forEach(result => {
      const icon = result.status === 'success' ? '✅' : 
                   result.status === 'warning' ? '⚠️' : '❌';
      
      console.log(`${icon} ${result.service}: ${result.message}`);
      
      if (result.details && process.env.NODE_ENV === 'development') {
        console.log(`   التفاصيل:`, result.details);
      }
    });

    const summary = {
      success: this.results.filter(r => r.status === 'success').length,
      warning: this.results.filter(r => r.status === 'warning').length,
      error: this.results.filter(r => r.status === 'error').length
    };

    console.log('\n📈 الملخص:');
    console.log(`✅ نجح: ${summary.success}`);
    console.log(`⚠️ تحذيرات: ${summary.warning}`);
    console.log(`❌ أخطاء: ${summary.error}`);

    if (summary.error === 0 && summary.warning === 0) {
      console.log('\n🎉 Firebase جاهز للاستخدام!');
    } else if (summary.error === 0) {
      console.log('\n👍 Firebase يعمل مع بعض التحذيرات');
    } else {
      console.log('\n🔧 يحتاج Firebase إلى إصلاح قبل الاستخدام');
    }
  }
}

// Export for use in components or pages
export { FirebaseTestSuite };

// Export a simple test function
export async function testFirebaseConnection(): Promise<boolean> {
  const testSuite = new FirebaseTestSuite();
  const results = await testSuite.runAllTests();
  testSuite.displayResults();
  
  return results.every(result => result.status !== 'error');
}
