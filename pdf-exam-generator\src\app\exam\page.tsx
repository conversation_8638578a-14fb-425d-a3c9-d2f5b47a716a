'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { ExamInterface } from '@/components/exam/ExamInterface';
import { ExamTimer } from '@/components/exam/ExamTimer';
import { ExamProgress } from '@/components/exam/ExamProgress';
import { ArrowLeft, Save, Flag } from 'lucide-react';

// Sample exam questions (in production, these would come from AI generation)
const sampleQuestions = [
  {
    id: 1,
    type: 'multiple-choice' as const,
    question: 'What is the primary goal of supervised learning in machine learning?',
    options: [
      'To find hidden patterns in unlabeled data',
      'To learn from labeled training data to make predictions on new data',
      'To reduce the dimensionality of the dataset',
      'To cluster similar data points together'
    ],
    correctAnswer: 1,
    explanation: 'Supervised learning uses labeled training data to learn patterns and make predictions on new, unseen data.'
  },
  {
    id: 2,
    type: 'true-false' as const,
    question: 'Neural networks can only be used for classification problems.',
    correctAnswer: false,
    explanation: 'Neural networks can be used for both classification and regression problems, as well as other tasks like clustering and dimensionality reduction.'
  },
  {
    id: 3,
    type: 'multiple-choice' as const,
    question: 'Which of the following is NOT a common activation function in neural networks?',
    options: [
      'ReLU (Rectified Linear Unit)',
      'Sigmoid',
      'Tanh',
      'Linear Regression'
    ],
    correctAnswer: 3,
    explanation: 'Linear Regression is a machine learning algorithm, not an activation function. ReLU, Sigmoid, and Tanh are all common activation functions.'
  },
  {
    id: 4,
    type: 'short-answer' as const,
    question: 'Explain what overfitting means in machine learning and how it can be prevented.',
    correctAnswer: 'Overfitting occurs when a model learns the training data too well, including noise and irrelevant patterns, leading to poor performance on new data. It can be prevented through techniques like cross-validation, regularization, early stopping, and using more training data.',
    explanation: 'Overfitting is a common problem where the model memorizes the training data rather than learning generalizable patterns.'
  },
  {
    id: 5,
    type: 'fill-blank' as const,
    question: 'The process of adjusting the weights in a neural network during training is called ______.',
    correctAnswer: 'backpropagation',
    explanation: 'Backpropagation is the algorithm used to calculate gradients and update weights in neural networks during training.'
  }
];

export default function ExamPage() {
  const router = useRouter();
  const [examConfig, setExamConfig] = useState<any>(null);
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [answers, setAnswers] = useState<Record<number, any>>({});
  const [timeRemaining, setTimeRemaining] = useState<number | null>(null);
  const [isExamStarted, setIsExamStarted] = useState(false);
  const [showConfirmSubmit, setShowConfirmSubmit] = useState(false);

  useEffect(() => {
    // Get exam configuration from localStorage
    const storedConfig = localStorage.getItem('examConfig');
    if (storedConfig) {
      const config = JSON.parse(storedConfig);
      setExamConfig(config);
      
      if (config.hasTimeLimit) {
        setTimeRemaining(config.timeLimit * 60); // Convert minutes to seconds
      }
    } else {
      // No config found, redirect to analyze page
      router.push('/analyze');
    }
  }, [router]);

  useEffect(() => {
    // Auto-save answers to localStorage
    if (Object.keys(answers).length > 0) {
      localStorage.setItem('examAnswers', JSON.stringify(answers));
    }
  }, [answers]);

  const handleStartExam = () => {
    setIsExamStarted(true);
  };

  const handleAnswerChange = (questionId: number, answer: any) => {
    setAnswers(prev => ({
      ...prev,
      [questionId]: answer
    }));
  };

  const handleSubmitExam = () => {
    // Store final answers and navigate to results
    localStorage.setItem('examResults', JSON.stringify({
      answers,
      questions: sampleQuestions,
      config: examConfig,
      completedAt: new Date().toISOString()
    }));
    
    router.push('/results');
  };

  const handleTimeUp = () => {
    // Auto-submit when time is up
    handleSubmitExam();
  };

  const handleBackToConfig = () => {
    router.push('/analyze');
  };

  if (!examConfig) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading exam...</p>
        </div>
      </div>
    );
  }

  if (!isExamStarted) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-2xl mx-auto p-8">
          <div className="bg-white rounded-lg shadow-lg p-8 text-center">
            <h1 className="text-3xl font-bold text-gray-900 mb-6">Ready to Start Your Exam?</h1>
            
            <div className="bg-blue-50 rounded-lg p-6 mb-8">
              <h2 className="text-lg font-semibold text-blue-900 mb-4">Exam Details</h2>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="text-blue-700 font-medium">Questions:</p>
                  <p className="text-blue-600">{sampleQuestions.length}</p>
                </div>
                <div>
                  <p className="text-blue-700 font-medium">Time Limit:</p>
                  <p className="text-blue-600">
                    {examConfig.hasTimeLimit ? `${examConfig.timeLimit} minutes` : 'No limit'}
                  </p>
                </div>
                <div>
                  <p className="text-blue-700 font-medium">Difficulty:</p>
                  <p className="text-blue-600 capitalize">{examConfig.difficulty}</p>
                </div>
                <div>
                  <p className="text-blue-700 font-medium">Language:</p>
                  <p className="text-blue-600 capitalize">{examConfig.language}</p>
                </div>
              </div>
            </div>

            <div className="space-y-4 mb-8 text-left">
              <h3 className="font-semibold text-gray-900">Instructions:</h3>
              <ul className="text-sm text-gray-600 space-y-2">
                <li>• Read each question carefully before answering</li>
                <li>• You can navigate between questions using the Previous/Next buttons</li>
                <li>• Your progress is automatically saved</li>
                <li>• You can flag questions for review</li>
                {examConfig.hasTimeLimit && (
                  <li>• The exam will auto-submit when time runs out</li>
                )}
              </ul>
            </div>

            <div className="flex space-x-4 justify-center">
              <button
                onClick={handleBackToConfig}
                className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <ArrowLeft className="w-4 h-4 inline mr-2" />
                Back to Configuration
              </button>
              <button
                onClick={handleStartExam}
                className="px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-semibold"
              >
                Start Exam
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 sticky top-0 z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <h1 className="text-xl font-semibold text-gray-900">Interactive Exam</h1>
              <ExamProgress 
                current={currentQuestion + 1} 
                total={sampleQuestions.length}
                answered={Object.keys(answers).length}
              />
            </div>
            <div className="flex items-center space-x-4">
              {timeRemaining !== null && (
                <ExamTimer 
                  timeRemaining={timeRemaining}
                  onTimeUp={handleTimeUp}
                  onTimeUpdate={setTimeRemaining}
                />
              )}
              <button
                onClick={() => setShowConfirmSubmit(true)}
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium"
              >
                Submit Exam
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <ExamInterface
          questions={sampleQuestions}
          currentQuestion={currentQuestion}
          answers={answers}
          onAnswerChange={handleAnswerChange}
          onQuestionChange={setCurrentQuestion}
          onSubmit={() => setShowConfirmSubmit(true)}
        />
      </div>

      {/* Submit Confirmation Modal */}
      {showConfirmSubmit && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md mx-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Submit Exam?</h3>
            <p className="text-gray-600 mb-6">
              Are you sure you want to submit your exam? You have answered {Object.keys(answers).length} out of {sampleQuestions.length} questions.
            </p>
            <div className="flex space-x-4">
              <button
                onClick={() => setShowConfirmSubmit(false)}
                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Continue Exam
              </button>
              <button
                onClick={handleSubmitExam}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Submit Exam
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
