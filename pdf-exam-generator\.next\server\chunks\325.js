exports.id=325,exports.ids=[325],exports.modules={7613:(e,s,t)=>{"use strict";t.d(s,{AuthProvider:()=>f,A:()=>p});var r=t(60687),a=t(43210),n=t(91042),l=t(75535),i=t(67989),o=t(70146);let c=(0,i.Wp)({apiKey:"your_api_key_here",authDomain:"your_project_id.firebaseapp.com",projectId:"your_project_id",storageBucket:"your_project_id.appspot.com",messagingSenderId:"your_sender_id",appId:"your_app_id",measurementId:"your_measurement_id"}),d=(0,n.xI)(c),m=(0,l.aU)(c);(0,o.c7)(c);let x=new n.HF,h=new n.sk;x.setCustomParameters({prompt:"select_account"}),h.setCustomParameters({display:"popup"});let u=(0,a.createContext)(void 0),p=()=>{let e=(0,a.useContext)(u);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e},f=({children:e})=>{let[s,t]=(0,a.useState)(null),[i,o]=(0,a.useState)(null),[c,p]=(0,a.useState)(!0),f=async(e,s={})=>{if(!e)return;let t=(0,l.H9)(m,"users",e.uid),r=await (0,l.x7)(t);if(r.exists()){let e={...r.data(),lastLoginAt:new Date};await (0,l.BN)(t,e,{merge:!0}),o(e)}else{let{displayName:r,email:a,photoURL:n}=e,i=new Date,c={uid:e.uid,email:a||"",displayName:r||"",photoURL:n||void 0,role:s.role||"student",createdAt:i,lastLoginAt:i,preferences:{language:"ar",theme:"light",notifications:!0},stats:{totalExams:0,averageScore:0,totalQuestions:0,studyTime:0},...s};try{await (0,l.BN)(t,c),o(c)}catch(e){console.error("Error creating user profile:",e)}}},g=async(e,s)=>{p(!0);try{let t=await (0,n.x9)(d,e,s);await f(t.user)}catch(e){throw console.error("Error signing in:",e),e}finally{p(!1)}},b=async(e,s)=>{try{return await g(e,s),!0}catch(e){return!1}},j=async(e,s,t,r)=>{p(!0);try{let a=await (0,n.eJ)(d,e,s);await (0,n.r7)(a.user,{displayName:t}),await f(a.user,{role:r,displayName:t})}catch(e){throw console.error("Error signing up:",e),e}finally{p(!1)}},v=async()=>{p(!0);try{let e=await (0,n.df)(d,x);await f(e.user)}catch(e){throw console.error("Error signing in with Google:",e),e}finally{p(!1)}},y=async()=>{p(!0);try{let e=await (0,n.df)(d,h);await f(e.user)}catch(e){throw console.error("Error signing in with Facebook:",e),e}finally{p(!1)}},N=async()=>{p(!0);try{await (0,n.CI)(d),t(null),o(null),localStorage.removeItem("examResults"),localStorage.removeItem("examAnswers"),localStorage.removeItem("uploadedPDF"),localStorage.removeItem("examConfig")}catch(e){throw console.error("Error signing out:",e),e}finally{p(!1)}},w=async e=>{try{await (0,n.J1)(d,e)}catch(e){throw console.error("Error sending password reset email:",e),e}},A=async e=>{if(s)try{let t=(0,l.H9)(m,"users",s.uid);await (0,l.BN)(t,e,{merge:!0}),i&&o({...i,...e})}catch(e){throw console.error("Error updating user profile:",e),e}};return(0,a.useEffect)(()=>(0,n.hg)(d,async e=>{e?(t(e),await f(e)):(t(null),o(null)),p(!1)}),[]),(0,r.jsx)(u.Provider,{value:{user:s,userProfile:i,loading:c,isAuthenticated:!!s,signIn:g,signUp:j,signInWithGoogle:v,signInWithFacebook:y,logout:N,resetPassword:w,updateUserProfile:A,login:b},children:e})}},29131:(e,s,t)=>{"use strict";t.d(s,{AuthProvider:()=>a});var r=t(12907);(0,r.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\templete\\pdf-exam-generator\\src\\contexts\\AuthContext.tsx","useAuth");let a=(0,r.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\templete\\pdf-exam-generator\\src\\contexts\\AuthContext.tsx","AuthProvider")},42029:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,16444,23)),Promise.resolve().then(t.t.bind(t,16042,23)),Promise.resolve().then(t.t.bind(t,88170,23)),Promise.resolve().then(t.t.bind(t,49477,23)),Promise.resolve().then(t.t.bind(t,29345,23)),Promise.resolve().then(t.t.bind(t,12089,23)),Promise.resolve().then(t.t.bind(t,46577,23)),Promise.resolve().then(t.t.bind(t,31307,23))},48747:(e,s,t)=>{"use strict";t.d(s,{AppContent:()=>D});var r=t(60687),a=t(7613),n=t(16189),l=t(85814),i=t.n(l),o=t(43210),c=t(32192),d=t(10022),m=t(53411),x=t(84027),h=t(82080),u=t(58869),p=t(11860),f=t(12941),g=t(40083);function b(){let[e,s]=(0,o.useState)(!1),[t,l]=(0,o.useState)(!1),b=(0,n.usePathname)(),{user:j,logout:v}=(0,a.A)(),y=j?[{name:"لوحة التحكم",href:"/dashboard",icon:c.A},{name:"رفع ملف",href:"/dashboard/upload",icon:d.A},{name:"السجل",href:"/dashboard/history",icon:m.A},{name:"الإعدادات",href:"/settings",icon:x.A}]:[{name:"الرئيسية",href:"/",icon:c.A}],N=()=>{v(),l(!1)},w=e=>"/"===e?"/"===b:b.startsWith(e);return(0,r.jsxs)("nav",{className:"bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50",children:[(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex justify-between h-16",children:[(0,r.jsx)("div",{className:"flex items-center",children:(0,r.jsxs)(i(),{href:"/",className:"flex items-center space-x-2 rtl:space-x-reverse",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center",children:(0,r.jsx)(h.A,{className:"w-5 h-5 text-white"})}),(0,r.jsx)("span",{className:"text-xl font-bold text-gray-900 hidden sm:block",children:"منصة الاختبارات التفاعلية"}),(0,r.jsx)("span",{className:"text-xl font-bold text-gray-900 sm:hidden",children:"الاختبارات"})]})}),(0,r.jsxs)("div",{className:"hidden md:flex items-center space-x-8 rtl:space-x-reverse",children:[y.map(e=>{let s=e.icon;return(0,r.jsxs)(i(),{href:e.href,className:`flex items-center space-x-1 rtl:space-x-reverse px-3 py-2 rounded-md text-sm font-medium transition-colors ${w(e.href)?"text-blue-600 bg-blue-50":"text-gray-600 hover:text-gray-900 hover:bg-gray-50"}`,children:[(0,r.jsx)(s,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:e.name})]},e.name)}),j?(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsxs)("button",{onClick:()=>l(!t),className:"flex items-center space-x-2 rtl:space-x-reverse px-3 py-2 rounded-md text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center",children:j.photoURL?(0,r.jsx)("img",{src:j.photoURL,alt:j?.displayName||"User",className:"w-8 h-8 rounded-full object-cover"}):(0,r.jsx)(u.A,{className:"w-4 h-4 text-white"})}),(0,r.jsx)("span",{children:j?.displayName||j?.email?.split("@")[0]||"المستخدم"})]}),t&&(0,r.jsx)("div",{className:"absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-50",children:(0,r.jsxs)("div",{className:"py-1",children:[(0,r.jsx)(i(),{href:"/dashboard",className:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50",onClick:()=>l(!1),children:"لوحة التحكم"}),(0,r.jsx)(i(),{href:"/settings",className:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50",onClick:()=>l(!1),children:"الإعدادات"}),(0,r.jsx)("button",{onClick:N,className:"w-full text-right block px-4 py-2 text-sm text-red-700 hover:bg-red-50",children:"تسجيل الخروج"})]})})]}):(0,r.jsx)(i(),{href:"/login",className:"bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 transition-colors",children:"تسجيل الدخول"})]}),(0,r.jsx)("div",{className:"md:hidden flex items-center",children:(0,r.jsx)("button",{onClick:()=>s(!e),className:"p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500","aria-label":"Toggle menu",children:e?(0,r.jsx)(p.A,{className:"w-6 h-6"}):(0,r.jsx)(f.A,{className:"w-6 h-6"})})})]})}),e&&(0,r.jsx)("div",{className:"md:hidden border-t border-gray-200 bg-white",children:(0,r.jsxs)("div",{className:"px-2 pt-2 pb-3 space-y-1",children:[y.map(e=>{let t=e.icon;return(0,r.jsxs)(i(),{href:e.href,onClick:()=>s(!1),className:`flex items-center space-x-2 rtl:space-x-reverse px-3 py-2 rounded-md text-base font-medium transition-colors ${w(e.href)?"text-blue-600 bg-blue-50":"text-gray-600 hover:text-gray-900 hover:bg-gray-50"}`,children:[(0,r.jsx)(t,{className:"w-5 h-5"}),(0,r.jsx)("span",{children:e.name})]},e.name)}),j?(0,r.jsxs)("div",{className:"border-t border-gray-200 pt-3 mt-3",children:[(0,r.jsxs)("div",{className:"flex items-center px-3 py-2 mb-2",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center ml-3",children:j.photoURL?(0,r.jsx)("img",{src:j.photoURL,alt:j?.displayName||"User",className:"w-8 h-8 rounded-full object-cover"}):(0,r.jsx)(u.A,{className:"w-4 h-4 text-white"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:j?.displayName||j?.email?.split("@")[0]||"المستخدم"}),(0,r.jsx)("div",{className:"text-xs text-gray-500 capitalize",children:"مستخدم"})]})]}),(0,r.jsxs)(i(),{href:"/dashboard",onClick:()=>s(!1),className:"flex items-center space-x-2 rtl:space-x-reverse px-3 py-2 rounded-md text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50",children:[(0,r.jsx)(m.A,{className:"w-5 h-5"}),(0,r.jsx)("span",{children:"لوحة التحكم"})]}),(0,r.jsxs)("button",{onClick:()=>{N(),s(!1)},className:"w-full flex items-center space-x-2 rtl:space-x-reverse px-3 py-2 rounded-md text-base font-medium text-red-600 hover:text-red-900 hover:bg-red-50",children:[(0,r.jsx)(g.A,{className:"w-5 h-5"}),(0,r.jsx)("span",{children:"تسجيل الخروج"})]})]}):(0,r.jsx)("div",{className:"border-t border-gray-200 pt-3 mt-3",children:(0,r.jsx)(i(),{href:"/login",onClick:()=>s(!1),className:"block px-3 py-2 rounded-md text-base font-medium bg-blue-600 text-white hover:bg-blue-700 text-center",children:"تسجيل الدخول"})})]})})]})}var j=t(99891),v=t(78005),y=t(41550);function N(){let e=new Date().getFullYear();return(0,r.jsx)("footer",{className:"bg-gray-900 text-white",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:[(0,r.jsxs)("div",{className:"lg:col-span-1",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center",children:(0,r.jsx)(d.A,{className:"w-5 h-5 text-white"})}),(0,r.jsx)("span",{className:"text-xl font-bold",children:"PDF Exam Generator"})]}),(0,r.jsx)("p",{className:"text-gray-400 text-sm mb-4",children:"Transform your PDF documents into interactive exams using AI. Perfect for educators, students, and e-learning platforms."}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-400",children:[(0,r.jsx)(j.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"Secure & Privacy-focused"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-sm font-semibold text-gray-300 uppercase tracking-wider mb-4",children:"Product"}),(0,r.jsx)("ul",{className:"space-y-2",children:[{name:"Features",href:"#features"},{name:"How it Works",href:"#how-it-works"},{name:"Pricing",href:"#pricing"},{name:"API Documentation",href:"#api"}].map(e=>(0,r.jsx)("li",{children:(0,r.jsx)(i(),{href:e.href,className:"text-gray-400 hover:text-white text-sm transition-colors",children:e.name})},e.name))})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-sm font-semibold text-gray-300 uppercase tracking-wider mb-4",children:"Legal"}),(0,r.jsx)("ul",{className:"space-y-2",children:[{name:"Privacy Policy",href:"/privacy"},{name:"Terms of Service",href:"/terms"},{name:"Cookie Policy",href:"/cookies"},{name:"GDPR Compliance",href:"/gdpr"}].map(e=>(0,r.jsx)("li",{children:(0,r.jsx)(i(),{href:e.href,className:"text-gray-400 hover:text-white text-sm transition-colors",children:e.name})},e.name))})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-sm font-semibold text-gray-300 uppercase tracking-wider mb-4",children:"Support"}),(0,r.jsx)("ul",{className:"space-y-2",children:[{name:"Help Center",href:"/help"},{name:"Contact Us",href:"/contact"},{name:"Bug Reports",href:"/bugs"},{name:"Feature Requests",href:"/features"}].map(e=>(0,r.jsx)("li",{children:(0,r.jsx)(i(),{href:e.href,className:"text-gray-400 hover:text-white text-sm transition-colors",children:e.name})},e.name))})]})]}),(0,r.jsx)("div",{className:"mt-8 pt-8 border-t border-gray-800",children:(0,r.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center",children:[(0,r.jsx)("div",{className:"flex items-center space-x-4 text-sm text-gray-400",children:(0,r.jsxs)("span",{children:["\xa9 ",e," PDF Exam Generator. All rights reserved."]})}),(0,r.jsxs)("div",{className:"flex items-center space-x-4 mt-4 md:mt-0",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-400",children:[(0,r.jsx)(v.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"AI-Powered"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-400",children:[(0,r.jsx)(y.A,{className:"w-4 h-4"}),(0,r.jsx)(i(),{href:"mailto:<EMAIL>",className:"hover:text-white transition-colors",children:"<EMAIL>"})]})]})]})})]})})}var w=t(49625),A=t(16023),P=t(39916),C=t(41312),k=t(99270),I=t(97051);function E({children:e}){let[s,t]=(0,o.useState)(!1),{user:l,logout:c}=(0,a.A)(),d=(0,n.usePathname)(),b=[{name:"لوحة التحكم",href:"/dashboard",icon:w.A},{name:"رفع ملف جديد",href:"/dashboard/upload",icon:A.A},{name:"الاختبارات",href:"/dashboard/exams",icon:h.A},{name:"التحليلات",href:"/dashboard/analytics",icon:m.A},{name:"السجل",href:"/dashboard/history",icon:P.A},{name:"الطلاب",href:"/dashboard/students",icon:C.A},{name:"الملف الشخصي",href:"/profile",icon:u.A},{name:"الإعدادات",href:"/settings",icon:x.A}];return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50 flex",children:[s&&(0,r.jsx)("div",{className:"fixed inset-0 z-40 lg:hidden",onClick:()=>t(!1),children:(0,r.jsx)("div",{className:"absolute inset-0 bg-gray-600 opacity-75"})}),(0,r.jsxs)("div",{className:`fixed inset-y-0 right-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0 ${s?"translate-x-0":"translate-x-full"}`,children:[(0,r.jsxs)("div",{className:"flex items-center justify-between h-16 px-6 border-b border-gray-200",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center",children:(0,r.jsx)(h.A,{className:"w-5 h-5 text-white"})}),(0,r.jsx)("span",{className:"mr-3 text-lg font-semibold text-gray-900",children:"منصة الاختبارات"})]}),(0,r.jsx)("button",{onClick:()=>t(!1),className:"lg:hidden text-gray-500 hover:text-gray-700",children:(0,r.jsx)(p.A,{className:"w-6 h-6"})})]}),(0,r.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center",children:l?.photoURL?(0,r.jsx)("img",{src:l.photoURL,alt:l?.displayName||"User",className:"w-12 h-12 rounded-full object-cover"}):(0,r.jsx)(u.A,{className:"w-6 h-6 text-white"})}),(0,r.jsxs)("div",{className:"mr-3",children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:l?.displayName||l?.email?.split("@")[0]||"المستخدم"}),(0,r.jsx)("div",{className:"text-xs text-gray-500 capitalize",children:"مستخدم"})]})]})}),(0,r.jsx)("nav",{className:"mt-6 px-3",children:(0,r.jsx)("div",{className:"space-y-1",children:b.map(e=>{let s=d===e.href||d.startsWith(e.href+"/");return(0,r.jsxs)(i(),{href:e.href,className:`group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors ${s?"bg-blue-50 text-blue-700 border-l-4 border-blue-700":"text-gray-700 hover:bg-gray-50 hover:text-gray-900"}`,children:[(0,r.jsx)(e.icon,{className:`ml-3 w-5 h-5 ${s?"text-blue-700":"text-gray-400 group-hover:text-gray-500"}`}),e.name]},e.name)})})}),(0,r.jsx)("div",{className:"mt-8 px-6",children:(0,r.jsxs)("div",{className:"bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-4",children:[(0,r.jsx)("h3",{className:"text-sm font-medium text-gray-900 mb-3",children:"إحصائياتك"}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex justify-between text-xs",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"الاختبارات"}),(0,r.jsx)("span",{className:"font-medium text-gray-900",children:"24"})]}),(0,r.jsxs)("div",{className:"flex justify-between text-xs",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"المعدل"}),(0,r.jsx)("span",{className:"font-medium text-gray-900",children:"85%"})]}),(0,r.jsxs)("div",{className:"flex justify-between text-xs",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"الأسئلة"}),(0,r.jsx)("span",{className:"font-medium text-gray-900",children:"342"})]})]})]})}),(0,r.jsx)("div",{className:"absolute bottom-6 left-0 right-0 px-6",children:(0,r.jsxs)("button",{onClick:()=>{c()},className:"w-full flex items-center px-3 py-2 text-sm font-medium text-red-700 hover:bg-red-50 rounded-lg transition-colors",children:[(0,r.jsx)(g.A,{className:"ml-3 w-5 h-5"}),"تسجيل الخروج"]})})]}),(0,r.jsxs)("div",{className:"flex-1 flex flex-col lg:mr-64",children:[(0,r.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center justify-between h-16 px-6",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("button",{onClick:()=>t(!0),className:"lg:hidden text-gray-500 hover:text-gray-700",children:(0,r.jsx)(f.A,{className:"w-6 h-6"})}),(0,r.jsx)("div",{className:"lg:hidden mr-4",children:(0,r.jsx)("h1",{className:"text-lg font-semibold text-gray-900",children:"لوحة التحكم"})})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4 rtl:space-x-reverse",children:[(0,r.jsxs)("div",{className:"hidden md:block relative",children:[(0,r.jsx)(k.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),(0,r.jsx)("input",{type:"text",placeholder:"البحث...",className:"w-64 pl-4 pr-10 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"})]}),(0,r.jsxs)("button",{className:"relative p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg",children:[(0,r.jsx)(I.A,{className:"w-5 h-5"}),(0,r.jsx)("span",{className:"absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full"})]}),(0,r.jsx)("div",{className:"flex items-center",children:(0,r.jsx)("div",{className:"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center",children:l?.photoURL?(0,r.jsx)("img",{src:l.photoURL,alt:l?.displayName||"User",className:"w-8 h-8 rounded-full object-cover"}):(0,r.jsx)(u.A,{className:"w-4 h-4 text-white"})})})]})]})}),(0,r.jsx)("main",{className:"flex-1 overflow-auto",children:e})]})]})}function D({children:e}){let{isAuthenticated:s,loading:t}=(0,a.A)(),l=(0,n.usePathname)();return t?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600",children:"جاري التحميل..."})]})}):l.startsWith("/auth/")?(0,r.jsx)(r.Fragment,{children:e}):s&&(l.startsWith("/dashboard")||"/analyze"===l||"/exam"===l||"/results"===l||"/settings"===l)?(0,r.jsx)(E,{children:e}):(0,r.jsxs)("div",{className:"min-h-screen flex flex-col",children:[(0,r.jsx)(b,{}),(0,r.jsx)("main",{className:"flex-1",children:e}),(0,r.jsx)(N,{})]})}},61135:()=>{},73345:(e,s,t)=>{"use strict";t.d(s,{AppContent:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call AppContent() from the server but AppContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\templete\\pdf-exam-generator\\src\\components\\layout\\AppContent.tsx","AppContent")},74818:(e,s,t)=>{Promise.resolve().then(t.bind(t,73345)),Promise.resolve().then(t.bind(t,29131))},82349:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,86346,23)),Promise.resolve().then(t.t.bind(t,27924,23)),Promise.resolve().then(t.t.bind(t,35656,23)),Promise.resolve().then(t.t.bind(t,40099,23)),Promise.resolve().then(t.t.bind(t,38243,23)),Promise.resolve().then(t.t.bind(t,28827,23)),Promise.resolve().then(t.t.bind(t,62763,23)),Promise.resolve().then(t.t.bind(t,97173,23))},92970:(e,s,t)=>{Promise.resolve().then(t.bind(t,48747)),Promise.resolve().then(t.bind(t,7613))},94431:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>d,metadata:()=>o,viewport:()=>c});var r=t(37413),a=t(25091),n=t.n(a);t(61135);var l=t(29131),i=t(73345);let o={title:"PDF to Interactive Exam Generator",description:"Transform PDF documents into interactive exams using AI. Perfect for teachers, students, and e-learning platforms.",keywords:["PDF","exam","interactive","AI","education","e-learning"],authors:[{name:"PDF Exam Generator Team"}]},c={width:"device-width",initialScale:1};function d({children:e}){return(0,r.jsx)("html",{lang:"en",className:n().variable,children:(0,r.jsx)("body",{className:"font-inter antialiased bg-gray-50 text-gray-900 min-h-screen",children:(0,r.jsx)(l.AuthProvider,{children:(0,r.jsx)(i.AppContent,{children:e})})})})}}};