{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/templete/pdf-exam-generator/src/components/landing/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useLanguage } from '@/contexts/LanguageContext';\nimport { \n  Menu, \n  X, \n  Globe, \n  Sun, \n  Moon,\n  FileText,\n  Zap\n} from 'lucide-react';\n\nexport function Header() {\n  const { language, theme, setLanguage, setTheme, t } = useLanguage();\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [isLangMenuOpen, setIsLangMenuOpen] = useState(false);\n  const router = useRouter();\n\n  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);\n  const toggleTheme = () => setTheme(theme === 'light' ? 'dark' : 'light');\n  const toggleLanguage = (lang: 'ar' | 'en') => {\n    setLanguage(lang);\n    setIsLangMenuOpen(false);\n  };\n\n  const handleLogin = () => {\n    router.push('/auth/login');\n  };\n\n  const handleSignup = () => {\n    router.push('/auth/register');\n  };\n\n  return (\n    <header className=\"bg-white dark:bg-gray-900 shadow-sm border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <div className=\"flex items-center space-x-2 rtl:space-x-reverse\">\n              <div className=\"w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center\">\n                <FileText className=\"w-5 h-5 text-white\" />\n              </div>\n              <span className=\"text-xl font-bold text-gray-900 dark:text-white\">\n                ExamAI\n              </span>\n            </div>\n          </div>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden md:flex items-center space-x-8 rtl:space-x-reverse\">\n            <a \n              href=\"#features\" \n              className=\"text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors\"\n            >\n              {t('nav.features')}\n            </a>\n            <a \n              href=\"#pricing\" \n              className=\"text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors\"\n            >\n              {t('nav.pricing')}\n            </a>\n            <a \n              href=\"#about\" \n              className=\"text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors\"\n            >\n              {t('nav.about')}\n            </a>\n            <a \n              href=\"#contact\" \n              className=\"text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors\"\n            >\n              {t('nav.contact')}\n            </a>\n          </nav>\n\n          {/* Controls */}\n          <div className=\"flex items-center space-x-4 rtl:space-x-reverse\">\n            {/* Language Selector */}\n            <div className=\"relative\">\n              <button\n                onClick={() => setIsLangMenuOpen(!isLangMenuOpen)}\n                className=\"flex items-center space-x-1 rtl:space-x-reverse text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors\"\n              >\n                <Globe className=\"w-4 h-4\" />\n                <span className=\"text-sm font-medium\">\n                  {language === 'ar' ? 'العربية' : 'English'}\n                </span>\n              </button>\n              \n              {isLangMenuOpen && (\n                <div className=\"absolute top-full mt-2 right-0 rtl:right-auto rtl:left-0 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg py-2 min-w-[120px] z-50\">\n                  <button\n                    onClick={() => toggleLanguage('ar')}\n                    className={`w-full text-right rtl:text-left px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors ${\n                      language === 'ar' ? 'text-blue-600 dark:text-blue-400 font-medium' : 'text-gray-700 dark:text-gray-300'\n                    }`}\n                  >\n                    العربية\n                  </button>\n                  <button\n                    onClick={() => toggleLanguage('en')}\n                    className={`w-full text-right rtl:text-left px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors ${\n                      language === 'en' ? 'text-blue-600 dark:text-blue-400 font-medium' : 'text-gray-700 dark:text-gray-300'\n                    }`}\n                  >\n                    English\n                  </button>\n                </div>\n              )}\n            </div>\n\n            {/* Theme Toggle */}\n            <button\n              onClick={toggleTheme}\n              className=\"p-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors\"\n            >\n              {theme === 'light' ? (\n                <Moon className=\"w-5 h-5\" />\n              ) : (\n                <Sun className=\"w-5 h-5\" />\n              )}\n            </button>\n\n            {/* Auth Buttons - Desktop */}\n            <div className=\"hidden md:flex items-center space-x-3 rtl:space-x-reverse\">\n              <button \n                onClick={handleLogin}\n                className=\"text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors\"\n              >\n                {t('nav.login')}\n              </button>\n              <button \n                onClick={handleSignup}\n                className=\"bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2 rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200 transform hover:scale-105\"\n              >\n                {t('nav.signup')}\n              </button>\n            </div>\n\n            {/* Mobile Menu Button */}\n            <button\n              onClick={toggleMenu}\n              className=\"md:hidden p-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors\"\n            >\n              {isMenuOpen ? (\n                <X className=\"w-6 h-6\" />\n              ) : (\n                <Menu className=\"w-6 h-6\" />\n              )}\n            </button>\n          </div>\n        </div>\n\n        {/* Mobile Menu */}\n        {isMenuOpen && (\n          <div className=\"md:hidden border-t border-gray-200 dark:border-gray-700 py-4\">\n            <div className=\"flex flex-col space-y-4\">\n              <a \n                href=\"#features\" \n                className=\"text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                {t('nav.features')}\n              </a>\n              <a \n                href=\"#pricing\" \n                className=\"text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                {t('nav.pricing')}\n              </a>\n              <a \n                href=\"#about\" \n                className=\"text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                {t('nav.about')}\n              </a>\n              <a \n                href=\"#contact\" \n                className=\"text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                {t('nav.contact')}\n              </a>\n              \n              <div className=\"border-t border-gray-200 dark:border-gray-700 pt-4 flex flex-col space-y-3\">\n                <button \n                  onClick={handleLogin}\n                  className=\"text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors text-right rtl:text-left\"\n                >\n                  {t('nav.login')}\n                </button>\n                <button \n                  onClick={handleSignup}\n                  className=\"bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2 rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200\"\n                >\n                  {t('nav.signup')}\n                </button>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AALA;;;;;AAeO,SAAS;;IACd,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,WAAW,EAAE,QAAQ,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,cAAW,AAAD;IAChE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,aAAa,IAAM,cAAc,CAAC;IACxC,MAAM,cAAc,IAAM,SAAS,UAAU,UAAU,SAAS;IAChE,MAAM,iBAAiB,CAAC;QACtB,YAAY;QACZ,kBAAkB;IACpB;IAEA,MAAM,cAAc;QAClB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,eAAe;QACnB,OAAO,IAAI,CAAC;IACd;IAEA,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,iNAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAEtB,6LAAC;wCAAK,WAAU;kDAAkD;;;;;;;;;;;;;;;;;sCAOtE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,WAAU;8CAET,EAAE;;;;;;8CAEL,6LAAC;oCACC,MAAK;oCACL,WAAU;8CAET,EAAE;;;;;;8CAEL,6LAAC;oCACC,MAAK;oCACL,WAAU;8CAET,EAAE;;;;;;8CAEL,6LAAC;oCACC,MAAK;oCACL,WAAU;8CAET,EAAE;;;;;;;;;;;;sCAKP,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,SAAS,IAAM,kBAAkB,CAAC;4CAClC,WAAU;;8DAEV,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,6LAAC;oDAAK,WAAU;8DACb,aAAa,OAAO,YAAY;;;;;;;;;;;;wCAIpC,gCACC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,SAAS,IAAM,eAAe;oDAC9B,WAAW,CAAC,6GAA6G,EACvH,aAAa,OAAO,iDAAiD,oCACrE;8DACH;;;;;;8DAGD,6LAAC;oDACC,SAAS,IAAM,eAAe;oDAC9B,WAAW,CAAC,6GAA6G,EACvH,aAAa,OAAO,iDAAiD,oCACrE;8DACH;;;;;;;;;;;;;;;;;;8CAQP,6LAAC;oCACC,SAAS;oCACT,WAAU;8CAET,UAAU,wBACT,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;6DAEhB,6LAAC,mMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;;;;;;8CAKnB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,SAAS;4CACT,WAAU;sDAET,EAAE;;;;;;sDAEL,6LAAC;4CACC,SAAS;4CACT,WAAU;sDAET,EAAE;;;;;;;;;;;;8CAKP,6LAAC;oCACC,SAAS;oCACT,WAAU;8CAET,2BACC,6LAAC,+LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;6DAEb,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;gBAOvB,4BACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,cAAc;0CAE5B,EAAE;;;;;;0CAEL,6LAAC;gCACC,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,cAAc;0CAE5B,EAAE;;;;;;0CAEL,6LAAC;gCACC,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,cAAc;0CAE5B,EAAE;;;;;;0CAEL,6LAAC;gCACC,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,cAAc;0CAE5B,EAAE;;;;;;0CAGL,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS;wCACT,WAAU;kDAET,EAAE;;;;;;kDAEL,6LAAC;wCACC,SAAS;wCACT,WAAU;kDAET,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASrB;GApMgB;;QACwC,sIAAA,CAAA,cAAW;QAGlD,qIAAA,CAAA,YAAS;;;KAJV", "debugId": null}}, {"offset": {"line": 394, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/templete/pdf-exam-generator/src/components/landing/Hero.tsx"], "sourcesContent": ["'use client';\n\nimport { useRouter } from 'next/navigation';\nimport { useLanguage } from '@/contexts/LanguageContext';\nimport { \n  Play, \n  Star, \n  Users, \n  FileText, \n  Zap, \n  BarChart3,\n  ArrowLeft,\n  ArrowRight,\n  CheckCircle\n} from 'lucide-react';\n\nexport function Hero() {\n  const { language, t } = useLanguage();\n  const router = useRouter();\n\n  const handleGetStarted = () => {\n    router.push('/auth/register');\n  };\n\n  const handleDemo = () => {\n    // Scroll to features section or show demo\n    document.getElementById('features')?.scrollIntoView({ behavior: 'smooth' });\n  };\n\n  return (\n    <section className=\"relative bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 py-20 lg:py-32 overflow-hidden\">\n      {/* Background Elements */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <div className=\"absolute -top-40 -right-32 w-80 h-80 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob\"></div>\n        <div className=\"absolute -bottom-40 -left-32 w-80 h-80 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000\"></div>\n        <div className=\"absolute top-40 left-40 w-80 h-80 bg-gradient-to-r from-yellow-400 to-red-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-4000\"></div>\n      </div>\n\n      <div className=\"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"grid lg:grid-cols-2 gap-12 items-center\">\n          {/* Content */}\n          <div className=\"text-center lg:text-right rtl:lg:text-left\">\n            {/* Trust Badge */}\n            <div className=\"inline-flex items-center space-x-2 rtl:space-x-reverse bg-white dark:bg-gray-800 rounded-full px-4 py-2 shadow-sm border border-gray-200 dark:border-gray-700 mb-8\">\n              <div className=\"flex items-center space-x-1 rtl:space-x-reverse\">\n                {[...Array(5)].map((_, i) => (\n                  <Star key={i} className=\"w-4 h-4 text-yellow-400 fill-current\" />\n                ))}\n              </div>\n              <span className=\"text-sm text-gray-600 dark:text-gray-300\">\n                {t('hero.trusted')}\n              </span>\n            </div>\n\n            {/* Main Heading */}\n            <h1 className=\"text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 dark:text-white mb-6 leading-tight\">\n              {t('hero.title')}\n              <span className=\"block text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600\">\n                {t('hero.subtitle')}\n              </span>\n            </h1>\n\n            {/* Description */}\n            <p className=\"text-xl text-gray-600 dark:text-gray-300 mb-8 leading-relaxed max-w-2xl mx-auto lg:mx-0\">\n              {t('hero.description')}\n            </p>\n\n            {/* CTA Buttons */}\n            <div className=\"flex flex-col sm:flex-row items-center justify-center lg:justify-start rtl:lg:justify-end space-y-4 sm:space-y-0 sm:space-x-4 rtl:space-x-reverse mb-12\">\n              <button \n                onClick={handleGetStarted}\n                className=\"w-full sm:w-auto bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-xl font-semibold text-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl\"\n              >\n                {t('hero.cta.primary')}\n                {language === 'ar' ? (\n                  <ArrowLeft className=\"w-5 h-5 mr-2 inline\" />\n                ) : (\n                  <ArrowRight className=\"w-5 h-5 ml-2 inline\" />\n                )}\n              </button>\n              \n              <button \n                onClick={handleDemo}\n                className=\"w-full sm:w-auto flex items-center justify-center space-x-2 rtl:space-x-reverse text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors\"\n              >\n                <Play className=\"w-5 h-5\" />\n                <span className=\"font-medium\">{t('hero.cta.secondary')}</span>\n              </button>\n            </div>\n\n            {/* Features Preview */}\n            <div className=\"grid grid-cols-1 sm:grid-cols-3 gap-6\">\n              <div className=\"flex items-center space-x-3 rtl:space-x-reverse\">\n                <div className=\"w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center\">\n                  <Zap className=\"w-5 h-5 text-blue-600 dark:text-blue-400\" />\n                </div>\n                <div className=\"text-right rtl:text-left\">\n                  <div className=\"font-semibold text-gray-900 dark:text-white\">AI-Powered</div>\n                  <div className=\"text-sm text-gray-600 dark:text-gray-400\">Smart Generation</div>\n                </div>\n              </div>\n\n              <div className=\"flex items-center space-x-3 rtl:space-x-reverse\">\n                <div className=\"w-10 h-10 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center\">\n                  <BarChart3 className=\"w-5 h-5 text-purple-600 dark:text-purple-400\" />\n                </div>\n                <div className=\"text-right rtl:text-left\">\n                  <div className=\"font-semibold text-gray-900 dark:text-white\">Analytics</div>\n                  <div className=\"text-sm text-gray-600 dark:text-gray-400\">Deep Insights</div>\n                </div>\n              </div>\n\n              <div className=\"flex items-center space-x-3 rtl:space-x-reverse\">\n                <div className=\"w-10 h-10 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center\">\n                  <CheckCircle className=\"w-5 h-5 text-green-600 dark:text-green-400\" />\n                </div>\n                <div className=\"text-right rtl:text-left\">\n                  <div className=\"font-semibold text-gray-900 dark:text-white\">Instant</div>\n                  <div className=\"text-sm text-gray-600 dark:text-gray-400\">Real-time Results</div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Visual */}\n          <div className=\"relative\">\n            {/* Main Dashboard Preview */}\n            <div className=\"relative bg-white dark:bg-gray-800 rounded-2xl shadow-2xl border border-gray-200 dark:border-gray-700 overflow-hidden\">\n              {/* Header */}\n              <div className=\"bg-gray-50 dark:bg-gray-900 px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n                <div className=\"flex items-center space-x-3 rtl:space-x-reverse\">\n                  <div className=\"flex space-x-2 rtl:space-x-reverse\">\n                    <div className=\"w-3 h-3 bg-red-400 rounded-full\"></div>\n                    <div className=\"w-3 h-3 bg-yellow-400 rounded-full\"></div>\n                    <div className=\"w-3 h-3 bg-green-400 rounded-full\"></div>\n                  </div>\n                  <div className=\"text-sm text-gray-600 dark:text-gray-400\">ExamAI Dashboard</div>\n                </div>\n              </div>\n\n              {/* Content */}\n              <div className=\"p-6\">\n                {/* Upload Area */}\n                <div className=\"border-2 border-dashed border-blue-300 dark:border-blue-600 rounded-lg p-8 text-center mb-6\">\n                  <FileText className=\"w-12 h-12 text-blue-600 dark:text-blue-400 mx-auto mb-4\" />\n                  <div className=\"text-lg font-semibold text-gray-900 dark:text-white mb-2\">\n                    Upload PDF Document\n                  </div>\n                  <div className=\"text-gray-600 dark:text-gray-400\">\n                    Drag & drop or click to select\n                  </div>\n                </div>\n\n                {/* Stats */}\n                <div className=\"grid grid-cols-3 gap-4\">\n                  <div className=\"text-center\">\n                    <div className=\"text-2xl font-bold text-blue-600 dark:text-blue-400\">1,234</div>\n                    <div className=\"text-sm text-gray-600 dark:text-gray-400\">Exams Created</div>\n                  </div>\n                  <div className=\"text-center\">\n                    <div className=\"text-2xl font-bold text-purple-600 dark:text-purple-400\">98%</div>\n                    <div className=\"text-sm text-gray-600 dark:text-gray-400\">Accuracy</div>\n                  </div>\n                  <div className=\"text-center\">\n                    <div className=\"text-2xl font-bold text-green-600 dark:text-green-400\">5min</div>\n                    <div className=\"text-sm text-gray-600 dark:text-gray-400\">Avg Time</div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Floating Elements */}\n            <div className=\"absolute -top-4 -right-4 bg-gradient-to-r from-blue-500 to-purple-500 text-white px-4 py-2 rounded-lg shadow-lg\">\n              <div className=\"flex items-center space-x-2 rtl:space-x-reverse\">\n                <Users className=\"w-4 h-4\" />\n                <span className=\"text-sm font-medium\">10K+ Users</span>\n              </div>\n            </div>\n\n            <div className=\"absolute -bottom-4 -left-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 px-4 py-2 rounded-lg shadow-lg\">\n              <div className=\"flex items-center space-x-2 rtl:space-x-reverse\">\n                <div className=\"w-2 h-2 bg-green-400 rounded-full animate-pulse\"></div>\n                <span className=\"text-sm text-gray-600 dark:text-gray-400\">Live Processing</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAJA;;;;AAgBO,SAAS;;IACd,MAAM,EAAE,QAAQ,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,cAAW,AAAD;IAClC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,mBAAmB;QACvB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,aAAa;QACjB,0CAA0C;QAC1C,SAAS,cAAc,CAAC,aAAa,eAAe;YAAE,UAAU;QAAS;IAC3E;IAEA,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACZ;mDAAI,MAAM;6CAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC,qMAAA,CAAA,OAAI;oDAAS,WAAU;mDAAb;;;;;;;;;;sDAGf,6LAAC;4CAAK,WAAU;sDACb,EAAE;;;;;;;;;;;;8CAKP,6LAAC;oCAAG,WAAU;;wCACX,EAAE;sDACH,6LAAC;4CAAK,WAAU;sDACb,EAAE;;;;;;;;;;;;8CAKP,6LAAC;oCAAE,WAAU;8CACV,EAAE;;;;;;8CAIL,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,SAAS;4CACT,WAAU;;gDAET,EAAE;gDACF,aAAa,qBACZ,6LAAC,mNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;yEAErB,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;sDAI1B,6LAAC;4CACC,SAAS;4CACT,WAAU;;8DAEV,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,6LAAC;oDAAK,WAAU;8DAAe,EAAE;;;;;;;;;;;;;;;;;;8CAKrC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;;;;;;8DAEjB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEAA8C;;;;;;sEAC7D,6LAAC;4DAAI,WAAU;sEAA2C;;;;;;;;;;;;;;;;;;sDAI9D,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,qNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;;;;;;8DAEvB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEAA8C;;;;;;sEAC7D,6LAAC;4DAAI,WAAU;sEAA2C;;;;;;;;;;;;;;;;;;sDAI9D,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,8NAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;;;;;;8DAEzB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEAA8C;;;;;;sEAC7D,6LAAC;4DAAI,WAAU;sEAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOlE,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;;;;;;;;;;;kEAEjB,6LAAC;wDAAI,WAAU;kEAA2C;;;;;;;;;;;;;;;;;sDAK9D,6LAAC;4CAAI,WAAU;;8DAEb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,iNAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,6LAAC;4DAAI,WAAU;sEAA2D;;;;;;sEAG1E,6LAAC;4DAAI,WAAU;sEAAmC;;;;;;;;;;;;8DAMpD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAAsD;;;;;;8EACrE,6LAAC;oEAAI,WAAU;8EAA2C;;;;;;;;;;;;sEAE5D,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAA0D;;;;;;8EACzE,6LAAC;oEAAI,WAAU;8EAA2C;;;;;;;;;;;;sEAE5D,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAAwD;;;;;;8EACvE,6LAAC;oEAAI,WAAU;8EAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAOlE,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,6LAAC;gDAAK,WAAU;0DAAsB;;;;;;;;;;;;;;;;;8CAI1C,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAK,WAAU;0DAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ3E;GA9KgB;;QACU,sIAAA,CAAA,cAAW;QACpB,qIAAA,CAAA,YAAS;;;KAFV", "debugId": null}}, {"offset": {"line": 1040, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/templete/pdf-exam-generator/src/components/landing/Features.tsx"], "sourcesContent": ["'use client';\n\nimport { useLanguage } from '@/contexts/LanguageContext';\nimport { \n  <PERSON>, \n  <PERSON>Pointer, \n  BarChart3, \n  Zap, \n  Globe, \n  Shield,\n  FileText,\n  Users,\n  Clock,\n  Target,\n  Sparkles,\n  TrendingUp\n} from 'lucide-react';\n\nexport function Features() {\n  const { t } = useLanguage();\n\n  const features = [\n    {\n      icon: Brain,\n      title: t('feature.ai.title'),\n      description: t('feature.ai.description'),\n      color: 'blue',\n      gradient: 'from-blue-500 to-cyan-500'\n    },\n    {\n      icon: MousePointer,\n      title: t('feature.interactive.title'),\n      description: t('feature.interactive.description'),\n      color: 'purple',\n      gradient: 'from-purple-500 to-pink-500'\n    },\n    {\n      icon: BarChart3,\n      title: t('feature.analytics.title'),\n      description: t('feature.analytics.description'),\n      color: 'green',\n      gradient: 'from-green-500 to-emerald-500'\n    },\n    {\n      icon: Zap,\n      title: t('feature.realtime.title'),\n      description: t('feature.realtime.description'),\n      color: 'yellow',\n      gradient: 'from-yellow-500 to-orange-500'\n    },\n    {\n      icon: Globe,\n      title: t('feature.multilang.title'),\n      description: t('feature.multilang.description'),\n      color: 'indigo',\n      gradient: 'from-indigo-500 to-blue-500'\n    },\n    {\n      icon: Shield,\n      title: t('feature.secure.title'),\n      description: t('feature.secure.description'),\n      color: 'red',\n      gradient: 'from-red-500 to-pink-500'\n    }\n  ];\n\n  const stats = [\n    {\n      icon: FileText,\n      number: '10,000+',\n      label: 'PDFs Processed',\n      color: 'blue'\n    },\n    {\n      icon: Users,\n      number: '5,000+',\n      label: 'Active Teachers',\n      color: 'green'\n    },\n    {\n      icon: Target,\n      number: '100,000+',\n      label: 'Exams Created',\n      color: 'purple'\n    },\n    {\n      icon: TrendingUp,\n      number: '95%',\n      label: 'Satisfaction Rate',\n      color: 'orange'\n    }\n  ];\n\n  return (\n    <section id=\"features\" className=\"py-20 bg-white dark:bg-gray-900\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Header */}\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-6\">\n            {t('features.title')}\n          </h2>\n          <p className=\"text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto\">\n            {t('features.subtitle')}\n          </p>\n        </div>\n\n        {/* Features Grid */}\n        <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20\">\n          {features.map((feature, index) => {\n            const Icon = feature.icon;\n            return (\n              <div\n                key={index}\n                className=\"group relative bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-200 dark:border-gray-700 hover:border-transparent hover:scale-105\"\n              >\n                {/* Gradient Border on Hover */}\n                <div className={`absolute inset-0 bg-gradient-to-r ${feature.gradient} rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10`}></div>\n                <div className=\"absolute inset-[1px] bg-white dark:bg-gray-800 rounded-2xl group-hover:bg-opacity-95 dark:group-hover:bg-opacity-95 transition-all duration-300\"></div>\n                \n                <div className=\"relative\">\n                  {/* Icon */}\n                  <div className={`w-16 h-16 bg-gradient-to-r ${feature.gradient} rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}>\n                    <Icon className=\"w-8 h-8 text-white\" />\n                  </div>\n\n                  {/* Content */}\n                  <h3 className=\"text-xl font-bold text-gray-900 dark:text-white mb-4 group-hover:text-transparent group-hover:bg-clip-text group-hover:bg-gradient-to-r group-hover:from-blue-600 group-hover:to-purple-600 transition-all duration-300\">\n                    {feature.title}\n                  </h3>\n                  <p className=\"text-gray-600 dark:text-gray-300 leading-relaxed\">\n                    {feature.description}\n                  </p>\n                </div>\n              </div>\n            );\n          })}\n        </div>\n\n        {/* Stats Section */}\n        <div className=\"bg-gradient-to-r from-blue-50 to-purple-50 dark:from-gray-800 dark:to-gray-700 rounded-3xl p-8 lg:p-12\">\n          <div className=\"text-center mb-12\">\n            <h3 className=\"text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-4\">\n              Trusted by Educators Worldwide\n            </h3>\n            <p className=\"text-gray-600 dark:text-gray-300\">\n              Join thousands of teachers who are already transforming their teaching with ExamAI\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-8\">\n            {stats.map((stat, index) => {\n              const Icon = stat.icon;\n              return (\n                <div key={index} className=\"text-center\">\n                  <div className={`w-16 h-16 mx-auto mb-4 bg-gradient-to-r ${\n                    stat.color === 'blue' ? 'from-blue-500 to-cyan-500' :\n                    stat.color === 'green' ? 'from-green-500 to-emerald-500' :\n                    stat.color === 'purple' ? 'from-purple-500 to-pink-500' :\n                    'from-orange-500 to-red-500'\n                  } rounded-xl flex items-center justify-center`}>\n                    <Icon className=\"w-8 h-8 text-white\" />\n                  </div>\n                  <div className=\"text-3xl font-bold text-gray-900 dark:text-white mb-2\">\n                    {stat.number}\n                  </div>\n                  <div className=\"text-gray-600 dark:text-gray-300\">\n                    {stat.label}\n                  </div>\n                </div>\n              );\n            })}\n          </div>\n        </div>\n\n        {/* How It Works Preview */}\n        <div className=\"mt-20\">\n          <div className=\"text-center mb-12\">\n            <h3 className=\"text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-4\">\n              How It Works\n            </h3>\n            <p className=\"text-gray-600 dark:text-gray-300\">\n              Transform your PDFs into interactive exams in just 3 simple steps\n            </p>\n          </div>\n\n          <div className=\"grid md:grid-cols-3 gap-8\">\n            {[\n              {\n                step: '1',\n                title: 'Upload PDF',\n                description: 'Simply drag and drop your PDF document or click to select from your device',\n                icon: FileText,\n                color: 'blue'\n              },\n              {\n                step: '2',\n                title: 'AI Processing',\n                description: 'Our advanced AI analyzes your content and generates relevant questions automatically',\n                icon: Brain,\n                color: 'purple'\n              },\n              {\n                step: '3',\n                title: 'Share & Analyze',\n                description: 'Share your exam with students and get detailed analytics on their performance',\n                icon: BarChart3,\n                color: 'green'\n              }\n            ].map((step, index) => {\n              const Icon = step.icon;\n              return (\n                <div key={index} className=\"text-center\">\n                  <div className={`w-20 h-20 mx-auto mb-6 bg-gradient-to-r ${\n                    step.color === 'blue' ? 'from-blue-500 to-cyan-500' :\n                    step.color === 'purple' ? 'from-purple-500 to-pink-500' :\n                    'from-green-500 to-emerald-500'\n                  } rounded-full flex items-center justify-center relative`}>\n                    <Icon className=\"w-10 h-10 text-white\" />\n                    <div className=\"absolute -top-2 -right-2 w-8 h-8 bg-white dark:bg-gray-800 rounded-full flex items-center justify-center text-sm font-bold text-gray-900 dark:text-white border-2 border-gray-200 dark:border-gray-600\">\n                      {step.step}\n                    </div>\n                  </div>\n                  <h4 className=\"text-xl font-bold text-gray-900 dark:text-white mb-3\">\n                    {step.title}\n                  </h4>\n                  <p className=\"text-gray-600 dark:text-gray-300\">\n                    {step.description}\n                  </p>\n                </div>\n              );\n            })}\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;AAkBO,SAAS;;IACd,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,cAAW,AAAD;IAExB,MAAM,WAAW;QACf;YACE,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO,EAAE;YACT,aAAa,EAAE;YACf,OAAO;YACP,UAAU;QACZ;QACA;YACE,MAAM,yNAAA,CAAA,eAAY;YAClB,OAAO,EAAE;YACT,aAAa,EAAE;YACf,OAAO;YACP,UAAU;QACZ;QACA;YACE,MAAM,qNAAA,CAAA,YAAS;YACf,OAAO,EAAE;YACT,aAAa,EAAE;YACf,OAAO;YACP,UAAU;QACZ;QACA;YACE,MAAM,mMAAA,CAAA,MAAG;YACT,OAAO,EAAE;YACT,aAAa,EAAE;YACf,OAAO;YACP,UAAU;QACZ;QACA;YACE,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO,EAAE;YACT,aAAa,EAAE;YACf,OAAO;YACP,UAAU;QACZ;QACA;YACE,MAAM,yMAAA,CAAA,SAAM;YACZ,OAAO,EAAE;YACT,aAAa,EAAE;YACf,OAAO;YACP,UAAU;QACZ;KACD;IAED,MAAM,QAAQ;QACZ;YACE,MAAM,iNAAA,CAAA,WAAQ;YACd,QAAQ;YACR,OAAO;YACP,OAAO;QACT;QACA;YACE,MAAM,uMAAA,CAAA,QAAK;YACX,QAAQ;YACR,OAAO;YACP,OAAO;QACT;QACA;YACE,MAAM,yMAAA,CAAA,SAAM;YACZ,QAAQ;YACR,OAAO;YACP,OAAO;QACT;QACA;YACE,MAAM,qNAAA,CAAA,aAAU;YAChB,QAAQ;YACR,OAAO;YACP,OAAO;QACT;KACD;IAED,qBACE,6LAAC;QAAQ,IAAG;QAAW,WAAU;kBAC/B,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCACX,EAAE;;;;;;sCAEL,6LAAC;4BAAE,WAAU;sCACV,EAAE;;;;;;;;;;;;8BAKP,6LAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,SAAS;wBACtB,MAAM,OAAO,QAAQ,IAAI;wBACzB,qBACE,6LAAC;4BAEC,WAAU;;8CAGV,6LAAC;oCAAI,WAAW,CAAC,kCAAkC,EAAE,QAAQ,QAAQ,CAAC,oFAAoF,CAAC;;;;;;8CAC3J,6LAAC;oCAAI,WAAU;;;;;;8CAEf,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAW,CAAC,2BAA2B,EAAE,QAAQ,QAAQ,CAAC,yGAAyG,CAAC;sDACvK,cAAA,6LAAC;gDAAK,WAAU;;;;;;;;;;;sDAIlB,6LAAC;4CAAG,WAAU;sDACX,QAAQ,KAAK;;;;;;sDAEhB,6LAAC;4CAAE,WAAU;sDACV,QAAQ,WAAW;;;;;;;;;;;;;2BAlBnB;;;;;oBAuBX;;;;;;8BAIF,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAoE;;;;;;8CAGlF,6LAAC;oCAAE,WAAU;8CAAmC;;;;;;;;;;;;sCAKlD,6LAAC;4BAAI,WAAU;sCACZ,MAAM,GAAG,CAAC,CAAC,MAAM;gCAChB,MAAM,OAAO,KAAK,IAAI;gCACtB,qBACE,6LAAC;oCAAgB,WAAU;;sDACzB,6LAAC;4CAAI,WAAW,CAAC,wCAAwC,EACvD,KAAK,KAAK,KAAK,SAAS,8BACxB,KAAK,KAAK,KAAK,UAAU,kCACzB,KAAK,KAAK,KAAK,WAAW,gCAC1B,6BACD,4CAA4C,CAAC;sDAC5C,cAAA,6LAAC;gDAAK,WAAU;;;;;;;;;;;sDAElB,6LAAC;4CAAI,WAAU;sDACZ,KAAK,MAAM;;;;;;sDAEd,6LAAC;4CAAI,WAAU;sDACZ,KAAK,KAAK;;;;;;;mCAbL;;;;;4BAiBd;;;;;;;;;;;;8BAKJ,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAoE;;;;;;8CAGlF,6LAAC;oCAAE,WAAU;8CAAmC;;;;;;;;;;;;sCAKlD,6LAAC;4BAAI,WAAU;sCACZ;gCACC;oCACE,MAAM;oCACN,OAAO;oCACP,aAAa;oCACb,MAAM,iNAAA,CAAA,WAAQ;oCACd,OAAO;gCACT;gCACA;oCACE,MAAM;oCACN,OAAO;oCACP,aAAa;oCACb,MAAM,uMAAA,CAAA,QAAK;oCACX,OAAO;gCACT;gCACA;oCACE,MAAM;oCACN,OAAO;oCACP,aAAa;oCACb,MAAM,qNAAA,CAAA,YAAS;oCACf,OAAO;gCACT;6BACD,CAAC,GAAG,CAAC,CAAC,MAAM;gCACX,MAAM,OAAO,KAAK,IAAI;gCACtB,qBACE,6LAAC;oCAAgB,WAAU;;sDACzB,6LAAC;4CAAI,WAAW,CAAC,wCAAwC,EACvD,KAAK,KAAK,KAAK,SAAS,8BACxB,KAAK,KAAK,KAAK,WAAW,gCAC1B,gCACD,uDAAuD,CAAC;;8DACvD,6LAAC;oDAAK,WAAU;;;;;;8DAChB,6LAAC;oDAAI,WAAU;8DACZ,KAAK,IAAI;;;;;;;;;;;;sDAGd,6LAAC;4CAAG,WAAU;sDACX,KAAK,KAAK;;;;;;sDAEb,6LAAC;4CAAE,WAAU;sDACV,KAAK,WAAW;;;;;;;mCAfX;;;;;4BAmBd;;;;;;;;;;;;;;;;;;;;;;;AAMZ;GA1NgB;;QACA,sIAAA,CAAA,cAAW;;;KADX", "debugId": null}}, {"offset": {"line": 1463, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/templete/pdf-exam-generator/src/components/landing/Pricing.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useLanguage } from '@/contexts/LanguageContext';\nimport { \n  Check, \n  Star, \n  Zap, \n  Crown, \n  Building,\n  ArrowLeft,\n  ArrowRight\n} from 'lucide-react';\n\nexport function Pricing() {\n  const { language, t } = useLanguage();\n  const [isYearly, setIsYearly] = useState(false);\n  const router = useRouter();\n\n  const handleGetStarted = (planType: string) => {\n    // Navigate to registration with plan parameter\n    router.push(`/auth/register?plan=${planType}`);\n  };\n\n  const plans = [\n    {\n      name: t('plan.free.name'),\n      price: t('plan.free.price'),\n      period: t('plan.free.period'),\n      yearlyPrice: '0',\n      description: t('plan.free.description'),\n      icon: Star,\n      color: 'gray',\n      gradient: 'from-gray-500 to-gray-600',\n      popular: false,\n      features: [\n        '5 PDFs per month',\n        '50 questions generated',\n        'Basic analytics',\n        'Email support',\n        'Standard templates'\n      ],\n      cta: t('pricing.cta'),\n      ctaStyle: 'border border-gray-300 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700',\n      planType: 'free'\n    },\n    {\n      name: t('plan.pro.name'),\n      price: t('plan.pro.price'),\n      period: t('plan.pro.period'),\n      yearlyPrice: '15',\n      description: t('plan.pro.description'),\n      icon: Zap,\n      color: 'blue',\n      gradient: 'from-blue-600 to-purple-600',\n      popular: true,\n      features: [\n        'Unlimited PDFs',\n        'Unlimited questions',\n        'Advanced analytics',\n        'Priority support',\n        'Custom templates',\n        'Team collaboration',\n        'Export options'\n      ],\n      cta: t('pricing.cta'),\n      ctaStyle: 'bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700',\n      planType: 'pro'\n    },\n    {\n      name: t('plan.enterprise.name'),\n      price: t('plan.enterprise.price'),\n      period: t('plan.enterprise.period'),\n      yearlyPrice: 'Custom',\n      description: t('plan.enterprise.description'),\n      icon: Building,\n      color: 'purple',\n      gradient: 'from-purple-600 to-pink-600',\n      popular: false,\n      features: [\n        'Everything in Pro',\n        'Custom integrations',\n        'Dedicated support',\n        'SLA guarantee',\n        'Custom branding',\n        'Advanced security',\n        'Training sessions'\n      ],\n      cta: t('pricing.contact'),\n      ctaStyle: 'border border-purple-300 text-purple-700 dark:text-purple-300 hover:bg-purple-50 dark:hover:bg-purple-900',\n      planType: 'enterprise'\n    }\n  ];\n\n  return (\n    <section id=\"pricing\" className=\"py-20 bg-gray-50 dark:bg-gray-800\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Header */}\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-6\">\n            {t('pricing.title')}\n          </h2>\n          <p className=\"text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-8\">\n            {t('pricing.subtitle')}\n          </p>\n\n          {/* Billing Toggle */}\n          <div className=\"inline-flex items-center bg-white dark:bg-gray-700 rounded-full p-1 border border-gray-200 dark:border-gray-600\">\n            <button\n              onClick={() => setIsYearly(false)}\n              className={`px-6 py-2 rounded-full text-sm font-medium transition-all duration-200 ${\n                !isYearly\n                  ? 'bg-blue-600 text-white shadow-sm'\n                  : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'\n              }`}\n            >\n              {t('pricing.monthly')}\n            </button>\n            <button\n              onClick={() => setIsYearly(true)}\n              className={`px-6 py-2 rounded-full text-sm font-medium transition-all duration-200 ${\n                isYearly\n                  ? 'bg-blue-600 text-white shadow-sm'\n                  : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'\n              }`}\n            >\n              {t('pricing.yearly')}\n              <span className=\"ml-2 bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full\">\n                {t('pricing.save')}\n              </span>\n            </button>\n          </div>\n        </div>\n\n        {/* Plans Grid */}\n        <div className=\"grid lg:grid-cols-3 gap-8 max-w-5xl mx-auto\">\n          {plans.map((plan, index) => {\n            const Icon = plan.icon;\n            const currentPrice = isYearly ? plan.yearlyPrice : plan.price;\n            \n            return (\n              <div\n                key={index}\n                className={`relative bg-white dark:bg-gray-900 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 ${\n                  plan.popular \n                    ? 'border-2 border-blue-500 scale-105 lg:scale-110' \n                    : 'border border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-blue-600'\n                }`}\n              >\n                {/* Popular Badge */}\n                {plan.popular && (\n                  <div className=\"absolute -top-4 left-1/2 transform -translate-x-1/2\">\n                    <div className=\"bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2 rounded-full text-sm font-medium\">\n                      {t('pricing.popular')}\n                    </div>\n                  </div>\n                )}\n\n                <div className=\"p-8\">\n                  {/* Plan Header */}\n                  <div className=\"text-center mb-8\">\n                    <div className={`w-16 h-16 mx-auto mb-4 bg-gradient-to-r ${plan.gradient} rounded-xl flex items-center justify-center`}>\n                      <Icon className=\"w-8 h-8 text-white\" />\n                    </div>\n                    <h3 className=\"text-2xl font-bold text-gray-900 dark:text-white mb-2\">\n                      {plan.name}\n                    </h3>\n                    <p className=\"text-gray-600 dark:text-gray-300\">\n                      {plan.description}\n                    </p>\n                  </div>\n\n                  {/* Price */}\n                  <div className=\"text-center mb-8\">\n                    <div className=\"flex items-baseline justify-center\">\n                      <span className=\"text-4xl font-bold text-gray-900 dark:text-white\">\n                        {currentPrice === 'Custom' ? 'Custom' : `$${currentPrice}`}\n                      </span>\n                      {currentPrice !== 'Custom' && currentPrice !== '0' && (\n                        <span className=\"text-gray-600 dark:text-gray-300 ml-2\">\n                          /{isYearly ? 'year' : 'month'}\n                        </span>\n                      )}\n                    </div>\n                    {isYearly && currentPrice !== 'Custom' && currentPrice !== '0' && (\n                      <div className=\"text-sm text-green-600 dark:text-green-400 mt-2\">\n                        Save ${(parseFloat(plan.price) * 12 - parseFloat(plan.yearlyPrice)).toFixed(0)} per year\n                      </div>\n                    )}\n                  </div>\n\n                  {/* Features */}\n                  <ul className=\"space-y-4 mb-8\">\n                    {plan.features.map((feature, featureIndex) => (\n                      <li key={featureIndex} className=\"flex items-center space-x-3 rtl:space-x-reverse\">\n                        <Check className=\"w-5 h-5 text-green-500 flex-shrink-0\" />\n                        <span className=\"text-gray-600 dark:text-gray-300\">{feature}</span>\n                      </li>\n                    ))}\n                  </ul>\n\n                  {/* CTA Button */}\n                  <button\n                    onClick={() => handleGetStarted(plan.planType)}\n                    className={`w-full py-3 px-6 rounded-xl font-semibold transition-all duration-200 transform hover:scale-105 ${plan.ctaStyle}`}\n                  >\n                    {plan.cta}\n                    {language === 'ar' ? (\n                      <ArrowLeft className=\"w-4 h-4 mr-2 inline\" />\n                    ) : (\n                      <ArrowRight className=\"w-4 h-4 ml-2 inline\" />\n                    )}\n                  </button>\n                </div>\n              </div>\n            );\n          })}\n        </div>\n\n        {/* FAQ Preview */}\n        <div className=\"mt-20 text-center\">\n          <h3 className=\"text-2xl font-bold text-gray-900 dark:text-white mb-4\">\n            {t('pricing.questions')}\n          </h3>\n          <p className=\"text-gray-600 dark:text-gray-300 mb-6\">\n            {t('pricing.contact_text')}\n          </p>\n          <button className=\"bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-3 rounded-xl font-semibold hover:from-blue-700 hover:to-purple-700 transition-all duration-200 transform hover:scale-105\">\n            {t('pricing.contact_us')}\n          </button>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AALA;;;;;AAeO,SAAS;;IACd,MAAM,EAAE,QAAQ,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,cAAW,AAAD;IAClC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,mBAAmB,CAAC;QACxB,+CAA+C;QAC/C,OAAO,IAAI,CAAC,CAAC,oBAAoB,EAAE,UAAU;IAC/C;IAEA,MAAM,QAAQ;QACZ;YACE,MAAM,EAAE;YACR,OAAO,EAAE;YACT,QAAQ,EAAE;YACV,aAAa;YACb,aAAa,EAAE;YACf,MAAM,qMAAA,CAAA,OAAI;YACV,OAAO;YACP,UAAU;YACV,SAAS;YACT,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;aACD;YACD,KAAK,EAAE;YACP,UAAU;YACV,UAAU;QACZ;QACA;YACE,MAAM,EAAE;YACR,OAAO,EAAE;YACT,QAAQ,EAAE;YACV,aAAa;YACb,aAAa,EAAE;YACf,MAAM,mMAAA,CAAA,MAAG;YACT,OAAO;YACP,UAAU;YACV,SAAS;YACT,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,KAAK,EAAE;YACP,UAAU;YACV,UAAU;QACZ;QACA;YACE,MAAM,EAAE;YACR,OAAO,EAAE;YACT,QAAQ,EAAE;YACV,aAAa;YACb,aAAa,EAAE;YACf,MAAM,6MAAA,CAAA,WAAQ;YACd,OAAO;YACP,UAAU;YACV,SAAS;YACT,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,KAAK,EAAE;YACP,UAAU;YACV,UAAU;QACZ;KACD;IAED,qBACE,6LAAC;QAAQ,IAAG;QAAU,WAAU;kBAC9B,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCACX,EAAE;;;;;;sCAEL,6LAAC;4BAAE,WAAU;sCACV,EAAE;;;;;;sCAIL,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,YAAY;oCAC3B,WAAW,CAAC,uEAAuE,EACjF,CAAC,WACG,qCACA,8EACJ;8CAED,EAAE;;;;;;8CAEL,6LAAC;oCACC,SAAS,IAAM,YAAY;oCAC3B,WAAW,CAAC,uEAAuE,EACjF,WACI,qCACA,8EACJ;;wCAED,EAAE;sDACH,6LAAC;4CAAK,WAAU;sDACb,EAAE;;;;;;;;;;;;;;;;;;;;;;;;8BAOX,6LAAC;oBAAI,WAAU;8BACZ,MAAM,GAAG,CAAC,CAAC,MAAM;wBAChB,MAAM,OAAO,KAAK,IAAI;wBACtB,MAAM,eAAe,WAAW,KAAK,WAAW,GAAG,KAAK,KAAK;wBAE7D,qBACE,6LAAC;4BAEC,WAAW,CAAC,qGAAqG,EAC/G,KAAK,OAAO,GACR,oDACA,gGACJ;;gCAGD,KAAK,OAAO,kBACX,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACZ,EAAE;;;;;;;;;;;8CAKT,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAW,CAAC,wCAAwC,EAAE,KAAK,QAAQ,CAAC,4CAA4C,CAAC;8DACpH,cAAA,6LAAC;wDAAK,WAAU;;;;;;;;;;;8DAElB,6LAAC;oDAAG,WAAU;8DACX,KAAK,IAAI;;;;;;8DAEZ,6LAAC;oDAAE,WAAU;8DACV,KAAK,WAAW;;;;;;;;;;;;sDAKrB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEACb,iBAAiB,WAAW,WAAW,CAAC,CAAC,EAAE,cAAc;;;;;;wDAE3D,iBAAiB,YAAY,iBAAiB,qBAC7C,6LAAC;4DAAK,WAAU;;gEAAwC;gEACpD,WAAW,SAAS;;;;;;;;;;;;;gDAI3B,YAAY,iBAAiB,YAAY,iBAAiB,qBACzD,6LAAC;oDAAI,WAAU;;wDAAkD;wDACxD,CAAC,WAAW,KAAK,KAAK,IAAI,KAAK,WAAW,KAAK,WAAW,CAAC,EAAE,OAAO,CAAC;wDAAG;;;;;;;;;;;;;sDAMrF,6LAAC;4CAAG,WAAU;sDACX,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,6BAC3B,6LAAC;oDAAsB,WAAU;;sEAC/B,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,6LAAC;4DAAK,WAAU;sEAAoC;;;;;;;mDAF7C;;;;;;;;;;sDAQb,6LAAC;4CACC,SAAS,IAAM,iBAAiB,KAAK,QAAQ;4CAC7C,WAAW,CAAC,gGAAgG,EAAE,KAAK,QAAQ,EAAE;;gDAE5H,KAAK,GAAG;gDACR,aAAa,qBACZ,6LAAC,mNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;yEAErB,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;2BApEvB;;;;;oBA0EX;;;;;;8BAIF,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCACX,EAAE;;;;;;sCAEL,6LAAC;4BAAE,WAAU;sCACV,EAAE;;;;;;sCAEL,6LAAC;4BAAO,WAAU;sCACf,EAAE;;;;;;;;;;;;;;;;;;;;;;;AAMf;GA5NgB;;QACU,sIAAA,CAAA,cAAW;QAEpB,qIAAA,CAAA,YAAS;;;KAHV", "debugId": null}}, {"offset": {"line": 1879, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/templete/pdf-exam-generator/src/components/landing/Footer.tsx"], "sourcesContent": ["'use client';\n\nimport { useLanguage } from '@/contexts/LanguageContext';\nimport { \n  FileText, \n  Mail, \n  Phone, \n  MapPin,\n  Twitter,\n  Facebook,\n  Linkedin,\n  Instagram,\n  Youtube,\n  Heart\n} from 'lucide-react';\n\nexport function Footer() {\n  const { t } = useLanguage();\n\n  const footerLinks = {\n    product: [\n      { name: 'Features', href: '#features' },\n      { name: 'Pricing', href: '#pricing' },\n      { name: 'API', href: '/api' },\n      { name: 'Integrations', href: '/integrations' },\n      { name: 'Changelog', href: '/changelog' }\n    ],\n    company: [\n      { name: 'About Us', href: '/about' },\n      { name: 'Careers', href: '/careers' },\n      { name: 'Blog', href: '/blog' },\n      { name: 'Press', href: '/press' },\n      { name: 'Partners', href: '/partners' }\n    ],\n    support: [\n      { name: 'Help Center', href: '/help' },\n      { name: 'Documentation', href: '/docs' },\n      { name: 'Community', href: '/community' },\n      { name: 'Contact Us', href: '/contact' },\n      { name: 'Status', href: '/status' }\n    ],\n    legal: [\n      { name: 'Privacy Policy', href: '/privacy' },\n      { name: 'Terms of Service', href: '/terms' },\n      { name: 'Cookie Policy', href: '/cookies' },\n      { name: 'GDPR', href: '/gdpr' },\n      { name: 'Security', href: '/security' }\n    ]\n  };\n\n  const socialLinks = [\n    { name: 'Twitter', icon: Twitter, href: 'https://twitter.com/examai' },\n    { name: 'Facebook', icon: Facebook, href: 'https://facebook.com/examai' },\n    { name: 'LinkedIn', icon: Linkedin, href: 'https://linkedin.com/company/examai' },\n    { name: 'Instagram', icon: Instagram, href: 'https://instagram.com/examai' },\n    { name: 'YouTube', icon: Youtube, href: 'https://youtube.com/examai' }\n  ];\n\n  return (\n    <footer className=\"bg-gray-900 dark:bg-black text-white\">\n      {/* Main Footer */}\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8\">\n          {/* Brand Section */}\n          <div className=\"lg:col-span-2\">\n            <div className=\"flex items-center space-x-2 rtl:space-x-reverse mb-6\">\n              <div className=\"w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center\">\n                <FileText className=\"w-5 h-5 text-white\" />\n              </div>\n              <span className=\"text-xl font-bold\">ExamAI</span>\n            </div>\n            \n            <p className=\"text-gray-400 mb-6 leading-relaxed\">\n              Transform your PDFs into interactive exams with the power of AI. \n              Join thousands of educators worldwide who trust ExamAI for better learning outcomes.\n            </p>\n\n            {/* Contact Info */}\n            <div className=\"space-y-3\">\n              <div className=\"flex items-center space-x-3 rtl:space-x-reverse text-gray-400\">\n                <Mail className=\"w-4 h-4\" />\n                <span><EMAIL></span>\n              </div>\n              <div className=\"flex items-center space-x-3 rtl:space-x-reverse text-gray-400\">\n                <Phone className=\"w-4 h-4\" />\n                <span>+****************</span>\n              </div>\n              <div className=\"flex items-center space-x-3 rtl:space-x-reverse text-gray-400\">\n                <MapPin className=\"w-4 h-4\" />\n                <span>San Francisco, CA</span>\n              </div>\n            </div>\n          </div>\n\n          {/* Product Links */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-6\">{t('footer.product')}</h3>\n            <ul className=\"space-y-3\">\n              {footerLinks.product.map((link, index) => (\n                <li key={index}>\n                  <a \n                    href={link.href}\n                    className=\"text-gray-400 hover:text-white transition-colors\"\n                  >\n                    {link.name}\n                  </a>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Company Links */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-6\">{t('footer.company')}</h3>\n            <ul className=\"space-y-3\">\n              {footerLinks.company.map((link, index) => (\n                <li key={index}>\n                  <a \n                    href={link.href}\n                    className=\"text-gray-400 hover:text-white transition-colors\"\n                  >\n                    {link.name}\n                  </a>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Support Links */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-6\">{t('footer.support')}</h3>\n            <ul className=\"space-y-3\">\n              {footerLinks.support.map((link, index) => (\n                <li key={index}>\n                  <a \n                    href={link.href}\n                    className=\"text-gray-400 hover:text-white transition-colors\"\n                  >\n                    {link.name}\n                  </a>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Legal Links */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-6\">{t('footer.legal')}</h3>\n            <ul className=\"space-y-3\">\n              {footerLinks.legal.map((link, index) => (\n                <li key={index}>\n                  <a \n                    href={link.href}\n                    className=\"text-gray-400 hover:text-white transition-colors\"\n                  >\n                    {link.name}\n                  </a>\n                </li>\n              ))}\n            </ul>\n          </div>\n        </div>\n\n        {/* Newsletter Signup */}\n        <div className=\"border-t border-gray-800 pt-12 mt-12\">\n          <div className=\"max-w-md mx-auto text-center\">\n            <h3 className=\"text-xl font-semibold mb-4\">Stay Updated</h3>\n            <p className=\"text-gray-400 mb-6\">\n              Get the latest updates, tips, and educational resources delivered to your inbox.\n            </p>\n            <div className=\"flex space-x-3 rtl:space-x-reverse\">\n              <input\n                type=\"email\"\n                placeholder=\"Enter your email\"\n                className=\"flex-1 px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg focus:outline-none focus:border-blue-500 text-white placeholder-gray-400\"\n              />\n              <button className=\"bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200 font-medium\">\n                Subscribe\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Bottom Footer */}\n      <div className=\"border-t border-gray-800\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\">\n            {/* Copyright */}\n            <div className=\"text-gray-400 text-sm\">\n              © 2024 ExamAI. All rights reserved. Made with{' '}\n              <Heart className=\"w-4 h-4 text-red-500 inline mx-1\" />\n              for educators worldwide.\n            </div>\n\n            {/* Social Links */}\n            <div className=\"flex items-center space-x-4 rtl:space-x-reverse\">\n              {socialLinks.map((social, index) => {\n                const Icon = social.icon;\n                return (\n                  <a\n                    key={index}\n                    href={social.href}\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    className=\"text-gray-400 hover:text-white transition-colors\"\n                    aria-label={social.name}\n                  >\n                    <Icon className=\"w-5 h-5\" />\n                  </a>\n                );\n              })}\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;AAgBO,SAAS;;IACd,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,cAAW,AAAD;IAExB,MAAM,cAAc;QAClB,SAAS;YACP;gBAAE,MAAM;gBAAY,MAAM;YAAY;YACtC;gBAAE,MAAM;gBAAW,MAAM;YAAW;YACpC;gBAAE,MAAM;gBAAO,MAAM;YAAO;YAC5B;gBAAE,MAAM;gBAAgB,MAAM;YAAgB;YAC9C;gBAAE,MAAM;gBAAa,MAAM;YAAa;SACzC;QACD,SAAS;YACP;gBAAE,MAAM;gBAAY,MAAM;YAAS;YACnC;gBAAE,MAAM;gBAAW,MAAM;YAAW;YACpC;gBAAE,MAAM;gBAAQ,MAAM;YAAQ;YAC9B;gBAAE,MAAM;gBAAS,MAAM;YAAS;YAChC;gBAAE,MAAM;gBAAY,MAAM;YAAY;SACvC;QACD,SAAS;YACP;gBAAE,MAAM;gBAAe,MAAM;YAAQ;YACrC;gBAAE,MAAM;gBAAiB,MAAM;YAAQ;YACvC;gBAAE,MAAM;gBAAa,MAAM;YAAa;YACxC;gBAAE,MAAM;gBAAc,MAAM;YAAW;YACvC;gBAAE,MAAM;gBAAU,MAAM;YAAU;SACnC;QACD,OAAO;YACL;gBAAE,MAAM;gBAAkB,MAAM;YAAW;YAC3C;gBAAE,MAAM;gBAAoB,MAAM;YAAS;YAC3C;gBAAE,MAAM;gBAAiB,MAAM;YAAW;YAC1C;gBAAE,MAAM;gBAAQ,MAAM;YAAQ;YAC9B;gBAAE,MAAM;gBAAY,MAAM;YAAY;SACvC;IACH;IAEA,MAAM,cAAc;QAClB;YAAE,MAAM;YAAW,MAAM,2MAAA,CAAA,UAAO;YAAE,MAAM;QAA6B;QACrE;YAAE,MAAM;YAAY,MAAM,6MAAA,CAAA,WAAQ;YAAE,MAAM;QAA8B;QACxE;YAAE,MAAM;YAAY,MAAM,6MAAA,CAAA,WAAQ;YAAE,MAAM;QAAsC;QAChF;YAAE,MAAM;YAAa,MAAM,+MAAA,CAAA,YAAS;YAAE,MAAM;QAA+B;QAC3E;YAAE,MAAM;YAAW,MAAM,2MAAA,CAAA,UAAO;YAAE,MAAM;QAA6B;KACtE;IAED,qBACE,6LAAC;QAAO,WAAU;;0BAEhB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,iNAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;0DAEtB,6LAAC;gDAAK,WAAU;0DAAoB;;;;;;;;;;;;kDAGtC,6LAAC;wCAAE,WAAU;kDAAqC;;;;;;kDAMlD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,6LAAC;kEAAK;;;;;;;;;;;;0DAER,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,6LAAC;kEAAK;;;;;;;;;;;;0DAER,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,6LAAC;kEAAK;;;;;;;;;;;;;;;;;;;;;;;;0CAMZ,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA8B,EAAE;;;;;;kDAC9C,6LAAC;wCAAG,WAAU;kDACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC9B,6LAAC;0DACC,cAAA,6LAAC;oDACC,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL;;;;;;;;;;;;;;;;0CAaf,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA8B,EAAE;;;;;;kDAC9C,6LAAC;wCAAG,WAAU;kDACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC9B,6LAAC;0DACC,cAAA,6LAAC;oDACC,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL;;;;;;;;;;;;;;;;0CAaf,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA8B,EAAE;;;;;;kDAC9C,6LAAC;wCAAG,WAAU;kDACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC9B,6LAAC;0DACC,cAAA,6LAAC;oDACC,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL;;;;;;;;;;;;;;;;0CAaf,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA8B,EAAE;;;;;;kDAC9C,6LAAC;wCAAG,WAAU;kDACX,YAAY,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC5B,6LAAC;0DACC,cAAA,6LAAC;oDACC,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL;;;;;;;;;;;;;;;;;;;;;;kCAcjB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAGlC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,WAAU;;;;;;sDAEZ,6LAAC;4CAAO,WAAU;sDAA+J;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASzL,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;oCAAwB;oCACS;kDAC9C,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAAqC;;;;;;;0CAKxD,6LAAC;gCAAI,WAAU;0CACZ,YAAY,GAAG,CAAC,CAAC,QAAQ;oCACxB,MAAM,OAAO,OAAO,IAAI;oCACxB,qBACE,6LAAC;wCAEC,MAAM,OAAO,IAAI;wCACjB,QAAO;wCACP,KAAI;wCACJ,WAAU;wCACV,cAAY,OAAO,IAAI;kDAEvB,cAAA,6LAAC;4CAAK,WAAU;;;;;;uCAPX;;;;;gCAUX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOd;GA1MgB;;QACA,sIAAA,CAAA,cAAW;;;KADX", "debugId": null}}, {"offset": {"line": 2470, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/templete/pdf-exam-generator/src/components/pages/LandingPage.tsx"], "sourcesContent": ["'use client';\n\nimport { Head<PERSON> } from '@/components/landing/Header';\nimport { <PERSON> } from '@/components/landing/Hero';\nimport { Features } from '@/components/landing/Features';\nimport { Pricing } from '@/components/landing/Pricing';\nimport { Footer } from '@/components/landing/Footer';\nimport { LanguageProvider } from '@/contexts/LanguageContext';\n\nexport function LandingPage() {\n  return (\n    <LanguageProvider>\n      <div className=\"min-h-screen bg-white dark:bg-gray-900\">\n        <Header />\n        <main>\n          <Hero />\n          <Features />\n          <Pricing />\n        </main>\n        <Footer />\n      </div>\n    </LanguageProvider>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AASO,SAAS;IACd,qBACE,6LAAC,sIAAA,CAAA,mBAAgB;kBACf,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,0IAAA,CAAA,SAAM;;;;;8BACP,6LAAC;;sCACC,6LAAC,wIAAA,CAAA,OAAI;;;;;sCACL,6LAAC,4IAAA,CAAA,WAAQ;;;;;sCACT,6LAAC,2IAAA,CAAA,UAAO;;;;;;;;;;;8BAEV,6LAAC,0IAAA,CAAA,SAAM;;;;;;;;;;;;;;;;AAIf;KAdgB", "debugId": null}}, {"offset": {"line": 2550, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/templete/pdf-exam-generator/src/components/ui/LoadingSpinner.tsx"], "sourcesContent": ["interface LoadingSpinnerProps {\n  size?: 'sm' | 'md' | 'lg';\n  color?: 'blue' | 'green' | 'gray';\n  text?: string;\n  fullScreen?: boolean;\n}\n\nexport default function LoadingSpinner({ \n  size = 'md', \n  color = 'blue', \n  text,\n  fullScreen = false \n}: LoadingSpinnerProps) {\n  const sizeClasses = {\n    sm: 'h-4 w-4',\n    md: 'h-8 w-8',\n    lg: 'h-12 w-12'\n  };\n\n  const colorClasses = {\n    blue: 'border-blue-600',\n    green: 'border-green-600',\n    gray: 'border-gray-600'\n  };\n\n  const spinner = (\n    <div className=\"flex flex-col items-center justify-center\">\n      <div\n        className={`animate-spin rounded-full border-2 border-gray-200 ${colorClasses[color]} border-t-transparent ${sizeClasses[size]}`}\n      />\n      {text && (\n        <p className=\"mt-2 text-sm text-gray-600\">{text}</p>\n      )}\n    </div>\n  );\n\n  if (fullScreen) {\n    return (\n      <div className=\"fixed inset-0 bg-white bg-opacity-75 flex items-center justify-center z-50\">\n        {spinner}\n      </div>\n    );\n  }\n\n  return spinner;\n}\n"], "names": [], "mappings": ";;;;;AAOe,SAAS,eAAe,EACrC,OAAO,IAAI,EACX,QAAQ,MAAM,EACd,IAAI,EACJ,aAAa,KAAK,EACE;IACpB,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,eAAe;QACnB,MAAM;QACN,OAAO;QACP,MAAM;IACR;IAEA,MAAM,wBACJ,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACC,WAAW,CAAC,mDAAmD,EAAE,YAAY,CAAC,MAAM,CAAC,sBAAsB,EAAE,WAAW,CAAC,KAAK,EAAE;;;;;;YAEjI,sBACC,6LAAC;gBAAE,WAAU;0BAA8B;;;;;;;;;;;;IAKjD,IAAI,YAAY;QACd,qBACE,6LAAC;YAAI,WAAU;sBACZ;;;;;;IAGP;IAEA,OAAO;AACT;KAtCwB", "debugId": null}}, {"offset": {"line": 2614, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/templete/pdf-exam-generator/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { LandingPage } from '@/components/pages/LandingPage';\nimport LoadingSpinner from '@/components/ui/LoadingSpinner';\n\nexport default function Home() {\n  const { isAuthenticated, loading } = useAuth();\n  const router = useRouter();\n\n  useEffect(() => {\n    if (!loading && isAuthenticated) {\n      router.push('/dashboard');\n    }\n  }, [isAuthenticated, loading, router]);\n\n  if (loading) {\n    return <LoadingSpinner fullScreen text=\"جاري التحميل...\" />;\n  }\n\n  if (isAuthenticated) {\n    return <LoadingSpinner fullScreen text=\"جاري التوجيه إلى لوحة التحكم...\" />;\n  }\n\n  return <LandingPage />;\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAQe,SAAS;;IACtB,MAAM,EAAE,eAAe,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,IAAI,CAAC,WAAW,iBAAiB;gBAC/B,OAAO,IAAI,CAAC;YACd;QACF;yBAAG;QAAC;QAAiB;QAAS;KAAO;IAErC,IAAI,SAAS;QACX,qBAAO,6LAAC,6IAAA,CAAA,UAAc;YAAC,UAAU;YAAC,MAAK;;;;;;IACzC;IAEA,IAAI,iBAAiB;QACnB,qBAAO,6LAAC,6IAAA,CAAA,UAAc;YAAC,UAAU;YAAC,MAAK;;;;;;IACzC;IAEA,qBAAO,6LAAC,6IAAA,CAAA,cAAW;;;;;AACrB;GAnBwB;;QACe,kIAAA,CAAA,UAAO;QAC7B,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}]}