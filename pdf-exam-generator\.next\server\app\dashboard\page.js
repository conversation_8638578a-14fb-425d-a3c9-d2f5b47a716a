(()=>{var e={};e.id=105,e.ids=[105],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9776:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});var s=t(60687);function a({size:e="md",color:r="blue",text:t,fullScreen:a=!1}){let i=(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center",children:[(0,s.jsx)("div",{className:`animate-spin rounded-full border-2 border-gray-200 ${{blue:"border-blue-600",green:"border-green-600",gray:"border-gray-600"}[r]} border-t-transparent ${{sm:"h-4 w-4",md:"h-8 w-8",lg:"h-12 w-12"}[e]}`}),t&&(0,s.jsx)("p",{className:"mt-2 text-sm text-gray-600",children:t})]});return a?(0,s.jsx)("div",{className:"fixed inset-0 bg-white bg-opacity-75 flex items-center justify-center z-50",children:i}):i}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13051:(e,r,t)=>{Promise.resolve().then(t.bind(t,63144))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28947:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34570:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});var s=t(60687);t(43210);var a=t(16189),i=t(7613),d=t(9776);function n({children:e,requiredRole:r,redirectTo:t="/auth/login"}){let{user:n,userProfile:o,loading:l,isAuthenticated:c}=(0,i.A)();return((0,a.useRouter)(),l||!c||r&&o&&o.role!==r)?(0,s.jsx)(d.A,{}):(0,s.jsx)(s.Fragment,{children:e})}},34631:e=>{"use strict";e.exports=require("tls")},37366:e=>{"use strict";e.exports=require("dns")},48730:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55912:(e,r,t)=>{Promise.resolve().then(t.bind(t,58061))},58061:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>g});var s=t(60687),a=t(7613),i=t(34393),d=t(82080),n=t(28947),o=t(10022),l=t(48730),c=t(96474),x=t(53411),m=t(41312),p=t(86561),h=t(85814),u=t.n(h);function g(){let{user:e}=(0,a.A)(),{t:r}=(0,i.o)(),t=[{name:r("dashboard.totalExams"),value:24,change:"+12%",changeType:"positive",icon:d.A,gradient:"from-blue-500 to-cyan-500"},{name:r("dashboard.averageScore"),value:"85%",change:"+5%",changeType:"positive",icon:n.A,gradient:"from-green-500 to-emerald-500"},{name:r("dashboard.totalQuestions"),value:342,change:"+18%",changeType:"positive",icon:o.A,gradient:"from-purple-500 to-pink-500"},{name:r("dashboard.studyHours"),value:42,change:"+8%",changeType:"positive",icon:l.A,gradient:"from-orange-500 to-red-500"}],h=[{name:r("dashboard.createExam"),description:"ارفع ملف PDF وأنشئ اختبار تفاعلي",href:"/dashboard/upload",icon:c.A,gradient:"from-blue-600 to-purple-600"},{name:r("dashboard.viewAnalytics"),description:"تحليل مفصل للأداء والتقدم",href:"/dashboard/analytics",icon:x.A,gradient:"from-green-600 to-emerald-600"},{name:r("dashboard.manageStudents"),description:"تتبع تقدم الطلاب والمجموعات",href:"/dashboard/students",icon:m.A,gradient:"from-purple-600 to-pink-600"}];return(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsxs)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-2",children:[r("dashboard.welcome"),"، ",e?.displayName||e?.email?.split("@")[0]||"المستخدم"]}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:r("dashboard.overview")})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:t.map((e,r)=>(0,s.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:e.name}),(0,s.jsx)("p",{className:"text-3xl font-bold text-gray-900 dark:text-white mt-2",children:e.value}),(0,s.jsxs)("div",{className:"flex items-center mt-2",children:[(0,s.jsx)("span",{className:`text-sm font-medium ${"positive"===e.changeType?"text-green-600 dark:text-green-400":"text-red-600 dark:text-red-400"}`,children:e.change}),(0,s.jsx)("span",{className:"text-sm text-gray-500 dark:text-gray-400 mr-2",children:"من الشهر الماضي"})]})]}),(0,s.jsx)("div",{className:`w-12 h-12 bg-gradient-to-r ${e.gradient} rounded-lg flex items-center justify-center`,children:(0,s.jsx)(e.icon,{className:"w-6 h-6 text-white"})})]})},r))}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,s.jsx)("div",{className:"lg:col-span-2",children:(0,s.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700",children:[(0,s.jsx)("div",{className:"p-6 border-b border-gray-200 dark:border-gray-700",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"الاختبارات الأخيرة"}),(0,s.jsx)(u(),{href:"/dashboard/exams",className:"text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium",children:"عرض الكل"})]})}),(0,s.jsx)("div",{className:"p-6",children:(0,s.jsx)("div",{className:"space-y-4",children:[{id:1,title:"اختبار الرياضيات - الجبر",subject:"رياضيات",score:85,date:"2024-01-15",questions:20,status:"completed"},{id:2,title:"اختبار الفيزياء - الحركة",subject:"فيزياء",score:92,date:"2024-01-14",questions:15,status:"completed"},{id:3,title:"اختبار الكيمياء - العناصر",subject:"كيمياء",score:78,date:"2024-01-13",questions:25,status:"completed"}].map(e=>(0,s.jsxs)("div",{className:"flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4 rtl:space-x-reverse",children:[(0,s.jsx)("div",{className:"w-10 h-10 bg-gradient-to-r from-blue-100 to-purple-100 dark:from-blue-900 dark:to-purple-900 rounded-lg flex items-center justify-center",children:(0,s.jsx)(d.A,{className:"w-5 h-5 text-blue-600 dark:text-blue-400"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-medium text-gray-900 dark:text-white",children:e.title}),(0,s.jsxs)("div",{className:"flex items-center space-x-4 rtl:space-x-reverse text-sm text-gray-500 dark:text-gray-400",children:[(0,s.jsx)("span",{children:e.subject}),(0,s.jsx)("span",{children:"•"}),(0,s.jsxs)("span",{children:[e.questions," سؤال"]}),(0,s.jsx)("span",{children:"•"}),(0,s.jsx)("span",{children:new Date(e.date).toLocaleDateString("ar-SA")})]})]})]}),(0,s.jsxs)("div",{className:"text-left",children:[(0,s.jsxs)("div",{className:`text-lg font-bold ${e.score>=90?"text-green-600 dark:text-green-400":e.score>=80?"text-blue-600 dark:text-blue-400":e.score>=70?"text-yellow-600 dark:text-yellow-400":"text-red-600 dark:text-red-400"}`,children:[e.score,"%"]}),(0,s.jsx)("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:"النتيجة"})]})]},e.id))})})]})}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6",children:[(0,s.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"إجراءات سريعة"}),(0,s.jsx)("div",{className:"space-y-3",children:h.map((e,r)=>(0,s.jsx)(u(),{href:e.href,className:`block p-4 bg-gradient-to-r ${e.gradient} text-white rounded-lg transition-all hover:scale-105 hover:shadow-lg`,children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(e.icon,{className:"w-5 h-5 ml-3"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-medium",children:e.name}),(0,s.jsx)("div",{className:"text-sm opacity-90",children:e.description})]})]})},r))})]}),(0,s.jsxs)("div",{className:"bg-gradient-to-r from-yellow-400 to-orange-500 rounded-lg p-6 text-white shadow-lg",children:[(0,s.jsxs)("div",{className:"flex items-center mb-4",children:[(0,s.jsx)(p.A,{className:"w-8 h-8 ml-3"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-bold",children:"إنجاز جديد!"}),(0,s.jsx)("p",{className:"text-sm opacity-90",children:"أكملت 15 اختبار"})]})]}),(0,s.jsx)("p",{className:"text-sm opacity-90",children:"تهانينا! لقد حققت إنجازاً رائعاً في رحلتك التعليمية"})]})]})]})]})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63144:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\templete\\\\pdf-exam-generator\\\\src\\\\app\\\\dashboard\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\layout.tsx","default")},63902:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>d});var s=t(60687),a=t(34570),i=t(60149);function d({children:e}){return(0,s.jsx)(a.A,{children:(0,s.jsx)(i.N,{children:e})})}},66195:(e,r,t)=>{Promise.resolve().then(t.bind(t,63902))},70440:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},80559:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\templete\\\\pdf-exam-generator\\\\src\\\\app\\\\dashboard\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\page.tsx","default")},81630:e=>{"use strict";e.exports=require("http")},83118:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>d.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>m,tree:()=>l});var s=t(65239),a=t(48088),i=t(88170),d=t.n(i),n=t(30893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);t.d(r,o);let l={children:["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,80559)),"D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,63144)),"D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:"/manifest.webmanifest"}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"D:\\templete\\pdf-exam-generator\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:"/manifest.webmanifest"}}]}.children,c=["D:\\templete\\pdf-exam-generator\\src\\app\\dashboard\\page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},86561:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},91645:e=>{"use strict";e.exports=require("net")},92360:(e,r,t)=>{Promise.resolve().then(t.bind(t,80559))},94735:e=>{"use strict";e.exports=require("events")},96474:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,248,658,647],()=>t(83118));module.exports=s})();