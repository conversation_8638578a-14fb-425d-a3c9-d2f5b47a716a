'use client';

import { useAuth } from '@/contexts/AuthContext';
import { 
  BookOpen, 
  Users, 
  BarChart3, 
  TrendingUp, 
  Clock, 
  Award,
  FileText,
  Target,
  Calendar,
  Plus
} from 'lucide-react';
import Link from 'next/link';

export default function DashboardPage() {
  const { user } = useAuth();

  const stats = [
    {
      name: 'إجمالي الاختبارات',
      value: 24,
      change: '+12%',
      changeType: 'positive',
      icon: BookOpen,
      color: 'bg-blue-500'
    },
    {
      name: 'متوسط النتائج',
      value: '85%',
      change: '+5%',
      changeType: 'positive',
      icon: Target,
      color: 'bg-green-500'
    },
    {
      name: 'إجمالي الأسئلة',
      value: 342,
      change: '+18%',
      changeType: 'positive',
      icon: FileText,
      color: 'bg-purple-500'
    },
    {
      name: 'ساعات الدراسة',
      value: 42,
      change: '+8%',
      changeType: 'positive',
      icon: Clock,
      color: 'bg-orange-500'
    }
  ];

  const recentExams = [
    {
      id: 1,
      title: 'اختبار الرياضيات - الجبر',
      subject: 'رياضيات',
      score: 85,
      date: '2024-01-15',
      questions: 20,
      status: 'completed'
    },
    {
      id: 2,
      title: 'اختبار الفيزياء - الحركة',
      subject: 'فيزياء',
      score: 92,
      date: '2024-01-14',
      questions: 15,
      status: 'completed'
    },
    {
      id: 3,
      title: 'اختبار الكيمياء - العناصر',
      subject: 'كيمياء',
      score: 78,
      date: '2024-01-13',
      questions: 25,
      status: 'completed'
    }
  ];

  const quickActions = [
    {
      name: 'إنشاء اختبار جديد',
      description: 'ارفع ملف PDF وأنشئ اختبار تفاعلي',
      href: '/dashboard/upload',
      icon: Plus,
      color: 'bg-blue-600 hover:bg-blue-700'
    },
    {
      name: 'عرض التحليلات',
      description: 'تحليل مفصل للأداء والتقدم',
      href: '/dashboard/analytics',
      icon: BarChart3,
      color: 'bg-green-600 hover:bg-green-700'
    },
    {
      name: 'إدارة الطلاب',
      description: 'تتبع تقدم الطلاب والمجموعات',
      href: '/dashboard/students',
      icon: Users,
      color: 'bg-purple-600 hover:bg-purple-700'
    }
  ];

  return (
    <div className="p-6">
      {/* Welcome Section */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          مرحباً، {user?.displayName || user?.email?.split('@')[0] || 'المستخدم'}
        </h1>
        <p className="text-gray-600">
          إليك نظرة عامة على نشاطك وإنجازاتك الأخيرة
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {stats.map((stat, index) => (
          <div key={index} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{stat.name}</p>
                <p className="text-3xl font-bold text-gray-900 mt-2">{stat.value}</p>
                <div className="flex items-center mt-2">
                  <span className={`text-sm font-medium ${
                    stat.changeType === 'positive' ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {stat.change}
                  </span>
                  <span className="text-sm text-gray-500 mr-2">من الشهر الماضي</span>
                </div>
              </div>
              <div className={`w-12 h-12 ${stat.color} rounded-lg flex items-center justify-center`}>
                <stat.icon className="w-6 h-6 text-white" />
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Recent Exams */}
        <div className="lg:col-span-2">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h2 className="text-lg font-semibold text-gray-900">الاختبارات الأخيرة</h2>
                <Link 
                  href="/dashboard/exams"
                  className="text-sm text-blue-600 hover:text-blue-700 font-medium"
                >
                  عرض الكل
                </Link>
              </div>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {recentExams.map((exam) => (
                  <div key={exam.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-4 rtl:space-x-reverse">
                      <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                        <BookOpen className="w-5 h-5 text-blue-600" />
                      </div>
                      <div>
                        <h3 className="font-medium text-gray-900">{exam.title}</h3>
                        <div className="flex items-center space-x-4 rtl:space-x-reverse text-sm text-gray-500">
                          <span>{exam.subject}</span>
                          <span>•</span>
                          <span>{exam.questions} سؤال</span>
                          <span>•</span>
                          <span>{new Date(exam.date).toLocaleDateString('ar-SA')}</span>
                        </div>
                      </div>
                    </div>
                    <div className="text-left">
                      <div className={`text-lg font-bold ${
                        exam.score >= 90 ? 'text-green-600' :
                        exam.score >= 80 ? 'text-blue-600' :
                        exam.score >= 70 ? 'text-yellow-600' : 'text-red-600'
                      }`}>
                        {exam.score}%
                      </div>
                      <div className="text-xs text-gray-500">النتيجة</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div>
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">إجراءات سريعة</h2>
            <div className="space-y-3">
              {quickActions.map((action, index) => (
                <Link
                  key={index}
                  href={action.href}
                  className={`block p-4 ${action.color} text-white rounded-lg transition-colors`}
                >
                  <div className="flex items-center">
                    <action.icon className="w-5 h-5 ml-3" />
                    <div>
                      <div className="font-medium">{action.name}</div>
                      <div className="text-sm opacity-90">{action.description}</div>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </div>

          {/* Achievement */}
          <div className="bg-gradient-to-r from-yellow-400 to-orange-500 rounded-lg p-6 text-white">
            <div className="flex items-center mb-4">
              <Award className="w-8 h-8 ml-3" />
              <div>
                <h3 className="font-bold">إنجاز جديد!</h3>
                <p className="text-sm opacity-90">أكملت 15 اختبار</p>
              </div>
            </div>
            <p className="text-sm opacity-90">
              تهانينا! لقد حققت إنجازاً رائعاً في رحلتك التعليمية
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
