'use client';

import { useAuth } from '@/contexts/AuthContext';
import { useLanguage } from '@/contexts/LanguageContext';
import {
  BookOpen,
  Users,
  BarChart3,
  TrendingUp,
  Clock,
  Award,
  FileText,
  Target,
  Calendar,
  Plus
} from 'lucide-react';
import Link from 'next/link';

export default function DashboardPage() {
  const { user } = useAuth();
  const { t } = useLanguage();

  const stats = [
    {
      name: t('dashboard.totalExams'),
      value: 24,
      change: '+12%',
      changeType: 'positive',
      icon: BookOpen,
      gradient: 'from-blue-500 to-cyan-500'
    },
    {
      name: t('dashboard.averageScore'),
      value: '85%',
      change: '+5%',
      changeType: 'positive',
      icon: Target,
      gradient: 'from-green-500 to-emerald-500'
    },
    {
      name: t('dashboard.totalQuestions'),
      value: 342,
      change: '+18%',
      changeType: 'positive',
      icon: FileText,
      gradient: 'from-purple-500 to-pink-500'
    },
    {
      name: t('dashboard.studyHours'),
      value: 42,
      change: '+8%',
      changeType: 'positive',
      icon: Clock,
      gradient: 'from-orange-500 to-red-500'
    }
  ];

  const recentExams = [
    {
      id: 1,
      title: 'اختبار الرياضيات - الجبر',
      subject: 'رياضيات',
      score: 85,
      date: '2024-01-15',
      questions: 20,
      status: 'completed'
    },
    {
      id: 2,
      title: 'اختبار الفيزياء - الحركة',
      subject: 'فيزياء',
      score: 92,
      date: '2024-01-14',
      questions: 15,
      status: 'completed'
    },
    {
      id: 3,
      title: 'اختبار الكيمياء - العناصر',
      subject: 'كيمياء',
      score: 78,
      date: '2024-01-13',
      questions: 25,
      status: 'completed'
    }
  ];

  const quickActions = [
    {
      name: t('dashboard.createExam'),
      description: 'ارفع ملف PDF وأنشئ اختبار تفاعلي',
      href: '/dashboard/upload',
      icon: Plus,
      gradient: 'from-blue-600 to-purple-600'
    },
    {
      name: t('dashboard.viewAnalytics'),
      description: 'تحليل مفصل للأداء والتقدم',
      href: '/dashboard/analytics',
      icon: BarChart3,
      gradient: 'from-green-600 to-emerald-600'
    },
    {
      name: t('dashboard.manageStudents'),
      description: 'تتبع تقدم الطلاب والمجموعات',
      href: '/dashboard/students',
      icon: Users,
      gradient: 'from-purple-600 to-pink-600'
    }
  ];

  return (
    <div className="p-6">
      {/* Welcome Section */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          {t('dashboard.welcome')}، {user?.displayName || user?.email?.split('@')[0] || 'المستخدم'}
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          {t('dashboard.overview')}
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {stats.map((stat, index) => (
          <div key={index} className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{stat.name}</p>
                <p className="text-3xl font-bold text-gray-900 dark:text-white mt-2">{stat.value}</p>
                <div className="flex items-center mt-2">
                  <span className={`text-sm font-medium ${
                    stat.changeType === 'positive' ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'
                  }`}>
                    {stat.change}
                  </span>
                  <span className="text-sm text-gray-500 dark:text-gray-400 mr-2">من الشهر الماضي</span>
                </div>
              </div>
              <div className={`w-12 h-12 bg-gradient-to-r ${stat.gradient} rounded-lg flex items-center justify-center`}>
                <stat.icon className="w-6 h-6 text-white" />
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Recent Exams */}
        <div className="lg:col-span-2">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="p-6 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <h2 className="text-lg font-semibold text-gray-900 dark:text-white">الاختبارات الأخيرة</h2>
                <Link
                  href="/dashboard/exams"
                  className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium"
                >
                  عرض الكل
                </Link>
              </div>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {recentExams.map((exam) => (
                  <div key={exam.id} className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
                    <div className="flex items-center space-x-4 rtl:space-x-reverse">
                      <div className="w-10 h-10 bg-gradient-to-r from-blue-100 to-purple-100 dark:from-blue-900 dark:to-purple-900 rounded-lg flex items-center justify-center">
                        <BookOpen className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                      </div>
                      <div>
                        <h3 className="font-medium text-gray-900 dark:text-white">{exam.title}</h3>
                        <div className="flex items-center space-x-4 rtl:space-x-reverse text-sm text-gray-500 dark:text-gray-400">
                          <span>{exam.subject}</span>
                          <span>•</span>
                          <span>{exam.questions} سؤال</span>
                          <span>•</span>
                          <span>{new Date(exam.date).toLocaleDateString('ar-SA')}</span>
                        </div>
                      </div>
                    </div>
                    <div className="text-left">
                      <div className={`text-lg font-bold ${
                        exam.score >= 90 ? 'text-green-600 dark:text-green-400' :
                        exam.score >= 80 ? 'text-blue-600 dark:text-blue-400' :
                        exam.score >= 70 ? 'text-yellow-600 dark:text-yellow-400' : 'text-red-600 dark:text-red-400'
                      }`}>
                        {exam.score}%
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">النتيجة</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div>
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">إجراءات سريعة</h2>
            <div className="space-y-3">
              {quickActions.map((action, index) => (
                <Link
                  key={index}
                  href={action.href}
                  className={`block p-4 bg-gradient-to-r ${action.gradient} text-white rounded-lg transition-all hover:scale-105 hover:shadow-lg`}
                >
                  <div className="flex items-center">
                    <action.icon className="w-5 h-5 ml-3" />
                    <div>
                      <div className="font-medium">{action.name}</div>
                      <div className="text-sm opacity-90">{action.description}</div>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </div>

          {/* Achievement */}
          <div className="bg-gradient-to-r from-yellow-400 to-orange-500 rounded-lg p-6 text-white shadow-lg">
            <div className="flex items-center mb-4">
              <Award className="w-8 h-8 ml-3" />
              <div>
                <h3 className="font-bold">إنجاز جديد!</h3>
                <p className="text-sm opacity-90">أكملت 15 اختبار</p>
              </div>
            </div>
            <p className="text-sm opacity-90">
              تهانينا! لقد حققت إنجازاً رائعاً في رحلتك التعليمية
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
