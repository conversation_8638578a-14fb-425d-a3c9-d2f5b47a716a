(()=>{var e={};e.id=974,e.ids=[974],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},9776:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(60687);function a({size:e="md",color:t="blue",text:r,fullScreen:a=!1}){let l=(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center",children:[(0,s.jsx)("div",{className:`animate-spin rounded-full border-2 border-gray-200 ${{blue:"border-blue-600",green:"border-green-600",gray:"border-gray-600"}[t]} border-t-transparent ${{sm:"h-4 w-4",md:"h-8 w-8",lg:"h-12 w-12"}[e]}`}),r&&(0,s.jsx)("p",{className:"mt-2 text-sm text-gray-600",children:r})]});return a?(0,s.jsx)("div",{className:"fixed inset-0 bg-white bg-opacity-75 flex items-center justify-center z-50",children:l}):l}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10896:(e,t,r)=>{Promise.resolve().then(r.bind(r,21204))},11437:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19587:(e,t)=>{"use strict";function r(e){return e.split("/").map(e=>encodeURIComponent(e)).join("/")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"encodeURIPath",{enumerable:!0,get:function(){return r}})},19771:e=>{"use strict";e.exports=require("process")},20624:(e,t,r)=>{Promise.resolve().then(r.bind(r,93745))},21204:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\templete\\\\pdf-exam-generator\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\templete\\pdf-exam-generator\\src\\app\\page.tsx","default")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28559:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37366:e=>{"use strict";e.exports=require("dns")},45583:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},49488:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>u,tree:()=>o});var s=r(65239),a=r(48088),l=r(88170),i=r.n(l),n=r(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);r.d(t,d);let o={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,21204)),"D:\\templete\\pdf-exam-generator\\src\\app\\page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:"/manifest.webmanifest"}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"D:\\templete\\pdf-exam-generator\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:"/manifest.webmanifest"}}]}.children,c=["D:\\templete\\pdf-exam-generator\\src\\app\\page.tsx"],x={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},49587:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return a}});let s=r(14985)._(r(64963));function a(e,t){var r;let a={};"function"==typeof e&&(a.loader=e);let l={...a,...t};return(0,s.default)({...l,modules:null==(r=l.loadableGenerated)?void 0:r.modules})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},55511:e=>{"use strict";e.exports=require("crypto")},56780:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"BailoutToCSR",{enumerable:!0,get:function(){return a}});let s=r(81208);function a(e){let{reason:t,children:r}=e;throw Object.defineProperty(new s.BailoutToCSRError(t),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64398:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},64777:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PreloadChunks",{enumerable:!0,get:function(){return n}});let s=r(60687),a=r(51215),l=r(29294),i=r(19587);function n(e){let{moduleIds:t}=e,r=l.workAsyncStorage.getStore();if(void 0===r)return null;let n=[];if(r.reactLoadableManifest&&t){let e=r.reactLoadableManifest;for(let r of t){if(!e[r])continue;let t=e[r].files;n.push(...t)}}return 0===n.length?null:(0,s.jsx)(s.Fragment,{children:n.map(e=>{let t=r.assetPrefix+"/_next/"+(0,i.encodeURIPath)(e);return e.endsWith(".css")?(0,s.jsx)("link",{precedence:"dynamic",href:t,rel:"stylesheet",as:"style"},e):((0,a.preload)(t,{as:"script",fetchPriority:"low"}),null)})})}},64963:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return o}});let s=r(60687),a=r(43210),l=r(56780),i=r(64777);function n(e){return{default:e&&"default"in e?e.default:e}}let d={loader:()=>Promise.resolve(n(()=>null)),loading:null,ssr:!0},o=function(e){let t={...d,...e},r=(0,a.lazy)(()=>t.loader().then(n)),o=t.loading;function c(e){let n=o?(0,s.jsx)(o,{isLoading:!0,pastDelay:!0,error:null}):null,d=!t.ssr||!!t.loading,c=d?a.Suspense:a.Fragment,x=t.ssr?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(i.PreloadChunks,{moduleIds:t.modules}),(0,s.jsx)(r,{...e})]}):(0,s.jsx)(l.BailoutToCSR,{reason:"next/dynamic",children:(0,s.jsx)(r,{...e})});return(0,s.jsx)(c,{...d?{fallback:n}:{},children:x})}return c.displayName="LoadableComponent",c}},70334:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},93745:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>R});var s=r(60687),a=r(43210),l=r(16189),i=r(7613),n=r(49587),d=r.n(n),o=r(34393),c=r(10022),x=r(11437),u=r(363),m=r(21134),h=r(11860),g=r(12941);function p(){let{language:e,theme:t,setLanguage:r,setTheme:i,t:n}=(0,o.o)(),[d,p]=(0,a.useState)(!1),[b,f]=(0,a.useState)(!1),y=(0,l.useRouter)(),v=e=>{r(e),f(!1)},j=()=>{y.push("/auth/login")},k=()=>{y.push("/auth/register")};return(0,s.jsx)("header",{className:"bg-white dark:bg-gray-900 shadow-sm border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsxs)("div",{className:"flex items-center space-x-2 rtl:space-x-reverse",children:[(0,s.jsx)("div",{className:"w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center",children:(0,s.jsx)(c.A,{className:"w-5 h-5 text-white"})}),(0,s.jsx)("span",{className:"text-xl font-bold text-gray-900 dark:text-white",children:"ExamAI"})]})}),(0,s.jsxs)("nav",{className:"hidden md:flex items-center space-x-8 rtl:space-x-reverse",children:[(0,s.jsx)("a",{href:"#features",className:"text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors",children:n("nav.features")}),(0,s.jsx)("a",{href:"#pricing",className:"text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors",children:n("nav.pricing")}),(0,s.jsx)("a",{href:"#about",className:"text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors",children:n("nav.about")}),(0,s.jsx)("a",{href:"#contact",className:"text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors",children:n("nav.contact")})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-4 rtl:space-x-reverse",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsxs)("button",{onClick:()=>f(!b),className:"flex items-center space-x-1 rtl:space-x-reverse text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors",children:[(0,s.jsx)(x.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{className:"text-sm font-medium",children:"ar"===e?"العربية":"English"})]}),b&&(0,s.jsxs)("div",{className:"absolute top-full mt-2 right-0 rtl:right-auto rtl:left-0 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg py-2 min-w-[120px] z-50",children:[(0,s.jsx)("button",{onClick:()=>v("ar"),className:`w-full text-right rtl:text-left px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors ${"ar"===e?"text-blue-600 dark:text-blue-400 font-medium":"text-gray-700 dark:text-gray-300"}`,children:"العربية"}),(0,s.jsx)("button",{onClick:()=>v("en"),className:`w-full text-right rtl:text-left px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors ${"en"===e?"text-blue-600 dark:text-blue-400 font-medium":"text-gray-700 dark:text-gray-300"}`,children:"English"})]})]}),(0,s.jsx)("button",{onClick:()=>i("light"===t?"dark":"light"),className:"p-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors",children:"light"===t?(0,s.jsx)(u.A,{className:"w-5 h-5"}):(0,s.jsx)(m.A,{className:"w-5 h-5"})}),(0,s.jsxs)("div",{className:"hidden md:flex items-center space-x-3 rtl:space-x-reverse",children:[(0,s.jsx)("button",{onClick:j,className:"text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors",children:n("nav.login")}),(0,s.jsx)("button",{onClick:k,className:"bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2 rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200 transform hover:scale-105",children:n("nav.signup")})]}),(0,s.jsx)("button",{onClick:()=>p(!d),className:"md:hidden p-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors",children:d?(0,s.jsx)(h.A,{className:"w-6 h-6"}):(0,s.jsx)(g.A,{className:"w-6 h-6"})})]})]}),d&&(0,s.jsx)("div",{className:"md:hidden border-t border-gray-200 dark:border-gray-700 py-4",children:(0,s.jsxs)("div",{className:"flex flex-col space-y-4",children:[(0,s.jsx)("a",{href:"#features",className:"text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors",onClick:()=>p(!1),children:n("nav.features")}),(0,s.jsx)("a",{href:"#pricing",className:"text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors",onClick:()=>p(!1),children:n("nav.pricing")}),(0,s.jsx)("a",{href:"#about",className:"text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors",onClick:()=>p(!1),children:n("nav.about")}),(0,s.jsx)("a",{href:"#contact",className:"text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors",onClick:()=>p(!1),children:n("nav.contact")}),(0,s.jsxs)("div",{className:"border-t border-gray-200 dark:border-gray-700 pt-4 flex flex-col space-y-3",children:[(0,s.jsx)("button",{onClick:j,className:"text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors text-right rtl:text-left",children:n("nav.login")}),(0,s.jsx)("button",{onClick:k,className:"bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2 rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200",children:n("nav.signup")})]})]})})]})})}var b=r(64398),f=r(28559),y=r(70334),v=r(97840),j=r(45583),k=r(53411),N=r(5336),w=r(41312);function A(){let{language:e,t}=(0,o.o)(),r=(0,l.useRouter)();return(0,s.jsxs)("section",{className:"relative bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 py-20 lg:py-32 overflow-hidden",children:[(0,s.jsxs)("div",{className:"absolute inset-0 overflow-hidden",children:[(0,s.jsx)("div",{className:"absolute -top-40 -right-32 w-80 h-80 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob"}),(0,s.jsx)("div",{className:"absolute -bottom-40 -left-32 w-80 h-80 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000"}),(0,s.jsx)("div",{className:"absolute top-40 left-40 w-80 h-80 bg-gradient-to-r from-yellow-400 to-red-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-4000"})]}),(0,s.jsx)("div",{className:"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"grid lg:grid-cols-2 gap-12 items-center",children:[(0,s.jsxs)("div",{className:"text-center lg:text-right rtl:lg:text-left",children:[(0,s.jsxs)("div",{className:"inline-flex items-center space-x-2 rtl:space-x-reverse bg-white dark:bg-gray-800 rounded-full px-4 py-2 shadow-sm border border-gray-200 dark:border-gray-700 mb-8",children:[(0,s.jsx)("div",{className:"flex items-center space-x-1 rtl:space-x-reverse",children:[void 0,void 0,void 0,void 0,void 0].map((e,t)=>(0,s.jsx)(b.A,{className:"w-4 h-4 text-yellow-400 fill-current"},t))}),(0,s.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-300",children:t("hero.trusted")})]}),(0,s.jsxs)("h1",{className:"text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 dark:text-white mb-6 leading-tight",children:[t("hero.title"),(0,s.jsx)("span",{className:"block text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600",children:t("hero.subtitle")})]}),(0,s.jsx)("p",{className:"text-xl text-gray-600 dark:text-gray-300 mb-8 leading-relaxed max-w-2xl mx-auto lg:mx-0",children:t("hero.description")}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row items-center justify-center lg:justify-start rtl:lg:justify-end space-y-4 sm:space-y-0 sm:space-x-4 rtl:space-x-reverse mb-12",children:[(0,s.jsxs)("button",{onClick:()=>{r.push("/auth/register")},className:"w-full sm:w-auto bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-xl font-semibold text-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl",children:[t("hero.cta.primary"),"ar"===e?(0,s.jsx)(f.A,{className:"w-5 h-5 mr-2 inline"}):(0,s.jsx)(y.A,{className:"w-5 h-5 ml-2 inline"})]}),(0,s.jsxs)("button",{onClick:()=>{document.getElementById("features")?.scrollIntoView({behavior:"smooth"})},className:"w-full sm:w-auto flex items-center justify-center space-x-2 rtl:space-x-reverse text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors",children:[(0,s.jsx)(v.A,{className:"w-5 h-5"}),(0,s.jsx)("span",{className:"font-medium",children:t("hero.cta.secondary")})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-6",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3 rtl:space-x-reverse",children:[(0,s.jsx)("div",{className:"w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center",children:(0,s.jsx)(j.A,{className:"w-5 h-5 text-blue-600 dark:text-blue-400"})}),(0,s.jsxs)("div",{className:"text-right rtl:text-left",children:[(0,s.jsx)("div",{className:"font-semibold text-gray-900 dark:text-white",children:"AI-Powered"}),(0,s.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Smart Generation"})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-3 rtl:space-x-reverse",children:[(0,s.jsx)("div",{className:"w-10 h-10 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center",children:(0,s.jsx)(k.A,{className:"w-5 h-5 text-purple-600 dark:text-purple-400"})}),(0,s.jsxs)("div",{className:"text-right rtl:text-left",children:[(0,s.jsx)("div",{className:"font-semibold text-gray-900 dark:text-white",children:"Analytics"}),(0,s.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Deep Insights"})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-3 rtl:space-x-reverse",children:[(0,s.jsx)("div",{className:"w-10 h-10 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center",children:(0,s.jsx)(N.A,{className:"w-5 h-5 text-green-600 dark:text-green-400"})}),(0,s.jsxs)("div",{className:"text-right rtl:text-left",children:[(0,s.jsx)("div",{className:"font-semibold text-gray-900 dark:text-white",children:"Instant"}),(0,s.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Real-time Results"})]})]})]})]}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsxs)("div",{className:"relative bg-white dark:bg-gray-800 rounded-2xl shadow-2xl border border-gray-200 dark:border-gray-700 overflow-hidden",children:[(0,s.jsx)("div",{className:"bg-gray-50 dark:bg-gray-900 px-6 py-4 border-b border-gray-200 dark:border-gray-700",children:(0,s.jsxs)("div",{className:"flex items-center space-x-3 rtl:space-x-reverse",children:[(0,s.jsxs)("div",{className:"flex space-x-2 rtl:space-x-reverse",children:[(0,s.jsx)("div",{className:"w-3 h-3 bg-red-400 rounded-full"}),(0,s.jsx)("div",{className:"w-3 h-3 bg-yellow-400 rounded-full"}),(0,s.jsx)("div",{className:"w-3 h-3 bg-green-400 rounded-full"})]}),(0,s.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:"ExamAI Dashboard"})]})}),(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsxs)("div",{className:"border-2 border-dashed border-blue-300 dark:border-blue-600 rounded-lg p-8 text-center mb-6",children:[(0,s.jsx)(c.A,{className:"w-12 h-12 text-blue-600 dark:text-blue-400 mx-auto mb-4"}),(0,s.jsx)("div",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-2",children:"Upload PDF Document"}),(0,s.jsx)("div",{className:"text-gray-600 dark:text-gray-400",children:"Drag & drop or click to select"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-blue-600 dark:text-blue-400",children:"1,234"}),(0,s.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Exams Created"})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-purple-600 dark:text-purple-400",children:"98%"}),(0,s.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Accuracy"})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-green-600 dark:text-green-400",children:"5min"}),(0,s.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Avg Time"})]})]})]})]}),(0,s.jsx)("div",{className:"absolute -top-4 -right-4 bg-gradient-to-r from-blue-500 to-purple-500 text-white px-4 py-2 rounded-lg shadow-lg",children:(0,s.jsxs)("div",{className:"flex items-center space-x-2 rtl:space-x-reverse",children:[(0,s.jsx)(w.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{className:"text-sm font-medium",children:"10K+ Users"})]})}),(0,s.jsx)("div",{className:"absolute -bottom-4 -left-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 px-4 py-2 rounded-lg shadow-lg",children:(0,s.jsxs)("div",{className:"flex items-center space-x-2 rtl:space-x-reverse",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-green-400 rounded-full animate-pulse"}),(0,s.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Live Processing"})]})})]})]})})]})}let P=d()(()=>r.e(518).then(r.bind(r,20518)).then(e=>({default:e.Features})),{loadableGenerated:{modules:["components\\pages\\LandingPage.tsx -> @/components/landing/Features"]},loading:()=>(0,s.jsx)("div",{className:"h-96 bg-gray-50 dark:bg-gray-800 animate-pulse rounded-lg"})}),_=d()(()=>r.e(785).then(r.bind(r,65166)).then(e=>({default:e.Pricing})),{loadableGenerated:{modules:["components\\pages\\LandingPage.tsx -> @/components/landing/Pricing"]},loading:()=>(0,s.jsx)("div",{className:"h-96 bg-gray-50 dark:bg-gray-800 animate-pulse rounded-lg"})}),C=d()(()=>r.e(906).then(r.bind(r,74906)).then(e=>({default:e.Footer})),{loadableGenerated:{modules:["components\\pages\\LandingPage.tsx -> @/components/landing/Footer"]},loading:()=>(0,s.jsx)("div",{className:"h-32 bg-gray-50 dark:bg-gray-800 animate-pulse"})});function q(){return(0,s.jsx)(o.LanguageProvider,{children:(0,s.jsxs)("div",{className:"min-h-screen bg-white dark:bg-gray-900",children:[(0,s.jsx)(p,{}),(0,s.jsxs)("main",{children:[(0,s.jsx)(A,{}),(0,s.jsx)(P,{}),(0,s.jsx)(_,{})]}),(0,s.jsx)(C,{})]})})}var M=r(9776);function R(){let{isAuthenticated:e,loading:t}=(0,i.A)();return((0,l.useRouter)(),t)?(0,s.jsx)(M.A,{fullScreen:!0,text:"جاري التحميل..."}):e?(0,s.jsx)(M.A,{fullScreen:!0,text:"جاري التوجيه إلى لوحة التحكم..."}):(0,s.jsx)(q,{})}},94735:e=>{"use strict";e.exports=require("events")},97840:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]])}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,248,658,647],()=>r(49488));module.exports=s})();