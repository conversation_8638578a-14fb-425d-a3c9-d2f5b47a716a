(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[105],{2713:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(9946).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},4186:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(9946).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},4616:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(9946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},4879:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>y});var t=s(5155),r=s(844),d=s(9283),l=s(5040),c=s(6785),i=s(7434),n=s(4186),o=s(4616),h=s(2713),x=s(7580),m=s(9037),g=s(6874),p=s.n(g);function y(){var e;let{user:a}=(0,r.A)(),{t:s}=(0,d.o)(),g=[{name:s("dashboard.totalExams"),value:24,change:"+12%",changeType:"positive",icon:l.A,gradient:"from-blue-500 to-cyan-500"},{name:s("dashboard.averageScore"),value:"85%",change:"+5%",changeType:"positive",icon:c.A,gradient:"from-green-500 to-emerald-500"},{name:s("dashboard.totalQuestions"),value:342,change:"+18%",changeType:"positive",icon:i.A,gradient:"from-purple-500 to-pink-500"},{name:s("dashboard.studyHours"),value:42,change:"+8%",changeType:"positive",icon:n.A,gradient:"from-orange-500 to-red-500"}],y=[{name:s("dashboard.createExam"),description:"ارفع ملف PDF وأنشئ اختبار تفاعلي",href:"/dashboard/upload",icon:o.A,gradient:"from-blue-600 to-purple-600"},{name:s("dashboard.viewAnalytics"),description:"تحليل مفصل للأداء والتقدم",href:"/dashboard/analytics",icon:h.A,gradient:"from-green-600 to-emerald-600"},{name:s("dashboard.manageStudents"),description:"تتبع تقدم الطلاب والمجموعات",href:"/dashboard/students",icon:x.A,gradient:"from-purple-600 to-pink-600"}];return(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsxs)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-2",children:[s("dashboard.welcome"),"، ",(null==a?void 0:a.displayName)||(null==a||null==(e=a.email)?void 0:e.split("@")[0])||"المستخدم"]}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:s("dashboard.overview")})]}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:g.map((e,a)=>(0,t.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:e.name}),(0,t.jsx)("p",{className:"text-3xl font-bold text-gray-900 dark:text-white mt-2",children:e.value}),(0,t.jsxs)("div",{className:"flex items-center mt-2",children:[(0,t.jsx)("span",{className:"text-sm font-medium ".concat("positive"===e.changeType?"text-green-600 dark:text-green-400":"text-red-600 dark:text-red-400"),children:e.change}),(0,t.jsx)("span",{className:"text-sm text-gray-500 dark:text-gray-400 mr-2",children:"من الشهر الماضي"})]})]}),(0,t.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r ".concat(e.gradient," rounded-lg flex items-center justify-center"),children:(0,t.jsx)(e.icon,{className:"w-6 h-6 text-white"})})]})},a))}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,t.jsx)("div",{className:"lg:col-span-2",children:(0,t.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700",children:[(0,t.jsx)("div",{className:"p-6 border-b border-gray-200 dark:border-gray-700",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"الاختبارات الأخيرة"}),(0,t.jsx)(p(),{href:"/dashboard/exams",className:"text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium",children:"عرض الكل"})]})}),(0,t.jsx)("div",{className:"p-6",children:(0,t.jsx)("div",{className:"space-y-4",children:[{id:1,title:"اختبار الرياضيات - الجبر",subject:"رياضيات",score:85,date:"2024-01-15",questions:20,status:"completed"},{id:2,title:"اختبار الفيزياء - الحركة",subject:"فيزياء",score:92,date:"2024-01-14",questions:15,status:"completed"},{id:3,title:"اختبار الكيمياء - العناصر",subject:"كيمياء",score:78,date:"2024-01-13",questions:25,status:"completed"}].map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4 rtl:space-x-reverse",children:[(0,t.jsx)("div",{className:"w-10 h-10 bg-gradient-to-r from-blue-100 to-purple-100 dark:from-blue-900 dark:to-purple-900 rounded-lg flex items-center justify-center",children:(0,t.jsx)(l.A,{className:"w-5 h-5 text-blue-600 dark:text-blue-400"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium text-gray-900 dark:text-white",children:e.title}),(0,t.jsxs)("div",{className:"flex items-center space-x-4 rtl:space-x-reverse text-sm text-gray-500 dark:text-gray-400",children:[(0,t.jsx)("span",{children:e.subject}),(0,t.jsx)("span",{children:"•"}),(0,t.jsxs)("span",{children:[e.questions," سؤال"]}),(0,t.jsx)("span",{children:"•"}),(0,t.jsx)("span",{children:new Date(e.date).toLocaleDateString("ar-SA")})]})]})]}),(0,t.jsxs)("div",{className:"text-left",children:[(0,t.jsxs)("div",{className:"text-lg font-bold ".concat(e.score>=90?"text-green-600 dark:text-green-400":e.score>=80?"text-blue-600 dark:text-blue-400":e.score>=70?"text-yellow-600 dark:text-yellow-400":"text-red-600 dark:text-red-400"),children:[e.score,"%"]}),(0,t.jsx)("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:"النتيجة"})]})]},e.id))})})]})}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6",children:[(0,t.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"إجراءات سريعة"}),(0,t.jsx)("div",{className:"space-y-3",children:y.map((e,a)=>(0,t.jsx)(p(),{href:e.href,className:"block p-4 bg-gradient-to-r ".concat(e.gradient," text-white rounded-lg transition-all hover:scale-105 hover:shadow-lg"),children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(e.icon,{className:"w-5 h-5 ml-3"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium",children:e.name}),(0,t.jsx)("div",{className:"text-sm opacity-90",children:e.description})]})]})},a))})]}),(0,t.jsxs)("div",{className:"bg-gradient-to-r from-yellow-400 to-orange-500 rounded-lg p-6 text-white shadow-lg",children:[(0,t.jsxs)("div",{className:"flex items-center mb-4",children:[(0,t.jsx)(m.A,{className:"w-8 h-8 ml-3"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-bold",children:"إنجاز جديد!"}),(0,t.jsx)("p",{className:"text-sm opacity-90",children:"أكملت 15 اختبار"})]})]}),(0,t.jsx)("p",{className:"text-sm opacity-90",children:"تهانينا! لقد حققت إنجازاً رائعاً في رحلتك التعليمية"})]})]})]})]})}},5040:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(9946).A)("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},5808:(e,a,s)=>{Promise.resolve().then(s.bind(s,4879))},6785:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(9946).A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},7434:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(9946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},7580:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(9946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},9037:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(9946).A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])}},e=>{var a=a=>e(e.s=a);e.O(0,[992,811,100,470,874,642,441,684,358],()=>a(5808)),_N_E=e.O()}]);