'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useLanguage } from '@/contexts/LanguageContext';
import { 
  Check, 
  Star, 
  Zap, 
  Crown, 
  Building,
  ArrowLeft,
  ArrowRight
} from 'lucide-react';

export function Pricing() {
  const { language, t } = useLanguage();
  const [isYearly, setIsYearly] = useState(false);
  const router = useRouter();

  const handleGetStarted = (planType: string) => {
    // Navigate to registration with plan parameter
    router.push(`/auth/register?plan=${planType}`);
  };

  const plans = [
    {
      name: t('plan.free.name'),
      price: t('plan.free.price'),
      period: t('plan.free.period'),
      yearlyPrice: '0',
      description: t('plan.free.description'),
      icon: Star,
      color: 'gray',
      gradient: 'from-gray-500 to-gray-600',
      popular: false,
      features: [
        '5 PDFs per month',
        '50 questions generated',
        'Basic analytics',
        'Email support',
        'Standard templates'
      ],
      cta: t('pricing.cta'),
      ctaStyle: 'border border-gray-300 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700',
      planType: 'free'
    },
    {
      name: t('plan.pro.name'),
      price: t('plan.pro.price'),
      period: t('plan.pro.period'),
      yearlyPrice: '15',
      description: t('plan.pro.description'),
      icon: Zap,
      color: 'blue',
      gradient: 'from-blue-600 to-purple-600',
      popular: true,
      features: [
        'Unlimited PDFs',
        'Unlimited questions',
        'Advanced analytics',
        'Priority support',
        'Custom templates',
        'Team collaboration',
        'Export options'
      ],
      cta: t('pricing.cta'),
      ctaStyle: 'bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700',
      planType: 'pro'
    },
    {
      name: t('plan.enterprise.name'),
      price: t('plan.enterprise.price'),
      period: t('plan.enterprise.period'),
      yearlyPrice: 'Custom',
      description: t('plan.enterprise.description'),
      icon: Building,
      color: 'purple',
      gradient: 'from-purple-600 to-pink-600',
      popular: false,
      features: [
        'Everything in Pro',
        'Custom integrations',
        'Dedicated support',
        'SLA guarantee',
        'Custom branding',
        'Advanced security',
        'Training sessions'
      ],
      cta: t('pricing.contact'),
      ctaStyle: 'border border-purple-300 text-purple-700 dark:text-purple-300 hover:bg-purple-50 dark:hover:bg-purple-900',
      planType: 'enterprise'
    }
  ];

  return (
    <section id="pricing" className="py-20 bg-gray-50 dark:bg-gray-800">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-6">
            {t('pricing.title')}
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-8">
            {t('pricing.subtitle')}
          </p>

          {/* Billing Toggle */}
          <div className="inline-flex items-center bg-white dark:bg-gray-700 rounded-full p-1 border border-gray-200 dark:border-gray-600">
            <button
              onClick={() => setIsYearly(false)}
              className={`px-6 py-2 rounded-full text-sm font-medium transition-all duration-200 ${
                !isYearly
                  ? 'bg-blue-600 text-white shadow-sm'
                  : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'
              }`}
            >
              {t('pricing.monthly')}
            </button>
            <button
              onClick={() => setIsYearly(true)}
              className={`px-6 py-2 rounded-full text-sm font-medium transition-all duration-200 ${
                isYearly
                  ? 'bg-blue-600 text-white shadow-sm'
                  : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'
              }`}
            >
              {t('pricing.yearly')}
              <span className="ml-2 bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                {t('pricing.save')}
              </span>
            </button>
          </div>
        </div>

        {/* Plans Grid */}
        <div className="grid lg:grid-cols-3 gap-8 max-w-5xl mx-auto">
          {plans.map((plan, index) => {
            const Icon = plan.icon;
            const currentPrice = isYearly ? plan.yearlyPrice : plan.price;
            
            return (
              <div
                key={index}
                className={`relative bg-white dark:bg-gray-900 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 ${
                  plan.popular 
                    ? 'border-2 border-blue-500 scale-105 lg:scale-110' 
                    : 'border border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-blue-600'
                }`}
              >
                {/* Popular Badge */}
                {plan.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2 rounded-full text-sm font-medium">
                      {t('pricing.popular')}
                    </div>
                  </div>
                )}

                <div className="p-8">
                  {/* Plan Header */}
                  <div className="text-center mb-8">
                    <div className={`w-16 h-16 mx-auto mb-4 bg-gradient-to-r ${plan.gradient} rounded-xl flex items-center justify-center`}>
                      <Icon className="w-8 h-8 text-white" />
                    </div>
                    <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                      {plan.name}
                    </h3>
                    <p className="text-gray-600 dark:text-gray-300">
                      {plan.description}
                    </p>
                  </div>

                  {/* Price */}
                  <div className="text-center mb-8">
                    <div className="flex items-baseline justify-center">
                      <span className="text-4xl font-bold text-gray-900 dark:text-white">
                        {currentPrice === 'Custom' ? 'Custom' : `$${currentPrice}`}
                      </span>
                      {currentPrice !== 'Custom' && currentPrice !== '0' && (
                        <span className="text-gray-600 dark:text-gray-300 ml-2">
                          /{isYearly ? 'year' : 'month'}
                        </span>
                      )}
                    </div>
                    {isYearly && currentPrice !== 'Custom' && currentPrice !== '0' && (
                      <div className="text-sm text-green-600 dark:text-green-400 mt-2">
                        Save ${(parseFloat(plan.price) * 12 - parseFloat(plan.yearlyPrice)).toFixed(0)} per year
                      </div>
                    )}
                  </div>

                  {/* Features */}
                  <ul className="space-y-4 mb-8">
                    {plan.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center space-x-3 rtl:space-x-reverse">
                        <Check className="w-5 h-5 text-green-500 flex-shrink-0" />
                        <span className="text-gray-600 dark:text-gray-300">{feature}</span>
                      </li>
                    ))}
                  </ul>

                  {/* CTA Button */}
                  <button
                    onClick={() => handleGetStarted(plan.planType)}
                    className={`w-full py-3 px-6 rounded-xl font-semibold transition-all duration-200 transform hover:scale-105 ${plan.ctaStyle}`}
                  >
                    {plan.cta}
                    {language === 'ar' ? (
                      <ArrowLeft className="w-4 h-4 mr-2 inline" />
                    ) : (
                      <ArrowRight className="w-4 h-4 ml-2 inline" />
                    )}
                  </button>
                </div>
              </div>
            );
          })}
        </div>

        {/* FAQ Preview */}
        <div className="mt-20 text-center">
          <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            {t('pricing.questions')}
          </h3>
          <p className="text-gray-600 dark:text-gray-300 mb-6">
            {t('pricing.contact_text')}
          </p>
          <button className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-3 rounded-xl font-semibold hover:from-blue-700 hover:to-purple-700 transition-all duration-200 transform hover:scale-105">
            {t('pricing.contact_us')}
          </button>
        </div>
      </div>
    </section>
  );
}
