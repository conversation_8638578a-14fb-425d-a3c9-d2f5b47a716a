'use client';

import { useState } from 'react';
import { FirebaseTestSuite } from '@/utils/firebase-test';
import { CheckCircle, XCircle, AlertTriangle, Play, RefreshCw } from 'lucide-react';

interface TestResult {
  service: string;
  status: 'success' | 'error' | 'warning';
  message: string;
  details?: any;
}

export default function TestFirebasePage() {
  const [results, setResults] = useState<TestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [hasRun, setHasRun] = useState(false);

  const runTests = async () => {
    setIsRunning(true);
    setResults([]);
    
    try {
      const testSuite = new FirebaseTestSuite();
      const testResults = await testSuite.runAllTests();
      setResults(testResults);
      setHasRun(true);
    } catch (error) {
      console.error('Error running tests:', error);
      setResults([{
        service: 'Test Suite',
        status: 'error',
        message: 'فشل في تشغيل الاختبارات',
        details: error
      }]);
    } finally {
      setIsRunning(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'warning':
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
      case 'error':
        return <XCircle className="h-5 w-5 text-red-500" />;
      default:
        return null;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
        return 'border-green-200 bg-green-50';
      case 'warning':
        return 'border-yellow-200 bg-yellow-50';
      case 'error':
        return 'border-red-200 bg-red-50';
      default:
        return 'border-gray-200 bg-gray-50';
    }
  };

  const summary = {
    success: results.filter(r => r.status === 'success').length,
    warning: results.filter(r => r.status === 'warning').length,
    error: results.filter(r => r.status === 'error').length
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            اختبار اتصال Firebase
          </h1>
          <p className="text-gray-600">
            تحقق من صحة إعدادات Firebase والاتصال بالخدمات
          </p>
        </div>

        {/* Test Controls */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-lg font-semibold text-gray-900 mb-1">
                تشغيل الاختبارات
              </h2>
              <p className="text-sm text-gray-600">
                اختبار الاتصال بـ Authentication و Firestore و Storage
              </p>
            </div>
            <button
              onClick={runTests}
              disabled={isRunning}
              className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {isRunning ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  جاري الاختبار...
                </>
              ) : (
                <>
                  <Play className="h-4 w-4 mr-2" />
                  {hasRun ? 'إعادة الاختبار' : 'بدء الاختبار'}
                </>
              )}
            </button>
          </div>
        </div>

        {/* Results Summary */}
        {hasRun && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">ملخص النتائج</h3>
            <div className="grid grid-cols-3 gap-4">
              <div className="text-center p-4 bg-green-50 rounded-lg border border-green-200">
                <div className="text-2xl font-bold text-green-600">{summary.success}</div>
                <div className="text-sm text-green-700">نجح</div>
              </div>
              <div className="text-center p-4 bg-yellow-50 rounded-lg border border-yellow-200">
                <div className="text-2xl font-bold text-yellow-600">{summary.warning}</div>
                <div className="text-sm text-yellow-700">تحذيرات</div>
              </div>
              <div className="text-center p-4 bg-red-50 rounded-lg border border-red-200">
                <div className="text-2xl font-bold text-red-600">{summary.error}</div>
                <div className="text-sm text-red-700">أخطاء</div>
              </div>
            </div>
          </div>
        )}

        {/* Test Results */}
        {results.length > 0 && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">نتائج الاختبارات</h3>
            {results.map((result, index) => (
              <div
                key={index}
                className={`border rounded-lg p-4 ${getStatusColor(result.status)}`}
              >
                <div className="flex items-start">
                  <div className="flex-shrink-0 mt-0.5">
                    {getStatusIcon(result.status)}
                  </div>
                  <div className="mr-3 flex-1">
                    <h4 className="font-medium text-gray-900">{result.service}</h4>
                    <p className="text-sm text-gray-700 mt-1">{result.message}</p>
                    {result.details && (
                      <details className="mt-2">
                        <summary className="text-xs text-gray-500 cursor-pointer hover:text-gray-700">
                          عرض التفاصيل
                        </summary>
                        <pre className="mt-2 text-xs bg-gray-100 p-2 rounded overflow-auto max-h-32">
                          {JSON.stringify(result.details, null, 2)}
                        </pre>
                      </details>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Instructions */}
        {!hasRun && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-blue-900 mb-2">
              تعليمات الاختبار
            </h3>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• تأكد من وجود ملف .env.local مع إعدادات Firebase</li>
              <li>• تحقق من صحة متغيرات البيئة</li>
              <li>• تأكد من تفعيل خدمات Firebase في Console</li>
              <li>• للإعدادات التجريبية، استخدم demo-project كـ project ID</li>
            </ul>
          </div>
        )}

        {/* Overall Status */}
        {hasRun && (
          <div className="mt-6 text-center">
            {summary.error === 0 && summary.warning === 0 && (
              <div className="text-green-600 font-semibold">
                🎉 Firebase جاهز للاستخدام!
              </div>
            )}
            {summary.error === 0 && summary.warning > 0 && (
              <div className="text-yellow-600 font-semibold">
                👍 Firebase يعمل مع بعض التحذيرات
              </div>
            )}
            {summary.error > 0 && (
              <div className="text-red-600 font-semibold">
                🔧 يحتاج Firebase إلى إصلاح قبل الاستخدام
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
